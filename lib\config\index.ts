// Application Configuration
export const APP_CONFIG = {
  // Application Info
  name: 'Pemakaman Digital',
  version: '1.0.0',
  description: 'Digital Memorial Management System',
  
  // API Configuration
  api: {
    baseUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api',
    timeout: 30000, // 30 seconds
    retries: 3,
  },

  // Pagination Configuration
  pagination: {
    defaultLimit: 10,
    maxLimit: 50,
    defaultPage: 1,
  },

  // Cache Configuration
  cache: {
    defaultTimeout: 5 * 60 * 1000, // 5 minutes
    memorialTimeout: 10 * 60 * 1000, // 10 minutes
    locationTimeout: 30 * 60 * 1000, // 30 minutes
    religionTimeout: 60 * 60 * 1000, // 1 hour
  },

  // File Upload Configuration
  upload: {
    maxFileSize: 5 * 1024 * 1024, // 5MB
    allowedImageTypes: ['image/jpeg', 'image/png', 'image/webp'],
    allowedDocumentTypes: ['application/pdf', 'image/jpeg', 'image/png'],
    maxImages: 5,
  },

  // Database Configuration
  database: {
    connectionTimeout: 10000, // 10 seconds
    queryTimeout: 30000, // 30 seconds
    maxConnections: 20,
  },

  // Security Configuration
  security: {
    bcryptRounds: 12,
    jwtExpiry: '7d',
    sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
  },

  // Feature Flags
  features: {
    enableCache: true,
    enableLogging: true,
    enableAnalytics: false,
    enableSearch: true,
    enableImageOptimization: true,
  },

  // UI Configuration
  ui: {
    itemsPerPage: 10,
    loadMoreIncrement: 10,
    searchDebounceMs: 300,
    toastDuration: 5000,
  },

  // External Services
  services: {
    supabase: {
      url: process.env.NEXT_PUBLIC_SUPABASE_URL,
      anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    },
    googleMaps: {
      apiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY,
    },
    payment: {
      midtransClientKey: process.env.NEXT_PUBLIC_MIDTRANS_CLIENT_KEY,
      midtransServerKey: process.env.MIDTRANS_SERVER_KEY,
    },
  },

  // Environment Configuration
  env: {
    isDevelopment: process.env.NODE_ENV === 'development',
    isProduction: process.env.NODE_ENV === 'production',
    isTest: process.env.NODE_ENV === 'test',
  },

  // Logging Configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    enableConsole: true,
    enableFile: false,
    maxLogSize: 10 * 1024 * 1024, // 10MB
  },

  // Performance Configuration
  performance: {
    enablePrefetch: true,
    enableLazyLoading: true,
    imageQuality: 80,
    enableCompression: true,
  },
} as const;

// Type-safe configuration access
export type AppConfig = typeof APP_CONFIG;

// Configuration validation
export function validateConfig(): boolean {
  const requiredEnvVars = [
    'DATABASE_URL',
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  ];

  const missingVars = requiredEnvVars.filter(
    (varName) => !process.env[varName]
  );

  if (missingVars.length > 0) {
    console.error('Missing required environment variables:', missingVars);
    return false;
  }

  return true;
}

// Get configuration value with type safety
export function getConfig<K extends keyof AppConfig>(key: K): AppConfig[K] {
  return APP_CONFIG[key];
}

// Get nested configuration value
export function getNestedConfig<
  K extends keyof AppConfig,
  NK extends keyof AppConfig[K]
>(key: K, nestedKey: NK): AppConfig[K][NK] {
  return APP_CONFIG[key][nestedKey];
}

// Environment helpers
export const isDevelopment = APP_CONFIG.env.isDevelopment;
export const isProduction = APP_CONFIG.env.isProduction;
export const isTest = APP_CONFIG.env.isTest;

// Feature flag helpers
export function isFeatureEnabled(feature: keyof typeof APP_CONFIG.features): boolean {
  return APP_CONFIG.features[feature];
}

// Service configuration helpers
export function getServiceConfig(service: keyof typeof APP_CONFIG.services) {
  return APP_CONFIG.services[service];
}

// Database configuration helpers
export function getDatabaseConfig() {
  return APP_CONFIG.database;
}

// Cache configuration helpers
export function getCacheTimeout(type: keyof typeof APP_CONFIG.cache): number {
  return APP_CONFIG.cache[type];
}

// Upload configuration helpers
export function getUploadConfig() {
  return APP_CONFIG.upload;
}

// Pagination configuration helpers
export function getPaginationConfig() {
  return APP_CONFIG.pagination;
}

// API configuration helpers
export function getApiConfig() {
  return APP_CONFIG.api;
}

// UI configuration helpers
export function getUIConfig() {
  return APP_CONFIG.ui;
}

// Performance configuration helpers
export function getPerformanceConfig() {
  return APP_CONFIG.performance;
}

// Logging configuration helpers
export function getLoggingConfig() {
  return APP_CONFIG.logging;
}

// Security configuration helpers
export function getSecurityConfig() {
  return APP_CONFIG.security;
}

// Initialize configuration on app start
export function initializeConfig(): void {
  if (!validateConfig()) {
    throw new Error('Configuration validation failed');
  }

  console.log(`🚀 ${APP_CONFIG.name} v${APP_CONFIG.version} initialized`);
  console.log(`📊 Environment: ${process.env.NODE_ENV}`);
  console.log(`🔧 Features enabled:`, Object.entries(APP_CONFIG.features)
    .filter(([, enabled]) => enabled)
    .map(([feature]) => feature)
  );
}
