import React from "react";
import { X } from "lucide-react";

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

export function Modal({ isOpen, onClose, title, children }: ModalProps) {
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4 overflow-y-auto">
      <div className="my-auto bg-memorial-900 border border-memorial-700 rounded-lg shadow-xl w-full max-w-sm md:max-w-lg lg:max-w-xl p-6 relative">
        <button 
          onClick={onClose}
          className="absolute top-4 right-4 text-memorial-400 hover:text-memorial-200"
        >
          <X className="h-5 w-5 md:h-6 md:w-6" />
        </button>
        
        <h2 className="text-xl md:text-2xl lg:text-3xl font-bold text-memorial-50 mb-4 md:mb-6 text-center">{title}</h2>
        
        <div className="md:text-lg">
          {children}
        </div>
      </div>
    </div>
  );
}


