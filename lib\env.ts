/**
 * Environment configuration loader
 * Ensures proper environment variables are loaded based on NODE_ENV
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// Load environment variables based on NODE_ENV
function loadEnvironment() {
  const nodeEnv = process.env.NODE_ENV || 'development';

  // Determine which env file to load
  let envFile = '.env.development';
  if (nodeEnv === 'production') {
    envFile = '.env.production';
  } else if (nodeEnv === 'development') {
    envFile = '.env.development';
  }

  // Load environment variables
  const envPath = resolve(process.cwd(), envFile);
  const result = config({ path: envPath });

  if (result.error) {
    console.warn(`⚠️ Could not load ${envFile}:`, result.error.message);
    console.log('   Using system environment variables or Next.js defaults');
  }

  return {
    nodeEnv,
    envFile,
    loaded: !result.error
  };
}

// Load environment on module import
const envInfo = loadEnvironment();

// Environment variable getters with fallbacks
export const env = {
  NODE_ENV: process.env.NODE_ENV || 'development',
  
  // Database
  DATABASE_URL: process.env.DATABASE_URL,
  
  // Supabase
  NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
  NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY,
  
  // Google Maps
  NEXT_PUBLIC_GOOGLE_MAPS_API_KEY: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY,
  
  // Environment info
  _envInfo: envInfo
};

// Validation functions
export function validateEnvironment() {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required for all environments
  if (!env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY) {
    errors.push('NEXT_PUBLIC_GOOGLE_MAPS_API_KEY is required');
  }

  // Database validation
  if (!env.DATABASE_URL) {
    errors.push('DATABASE_URL is required');
  }

  // Production-specific validation
  if (env.NODE_ENV === 'production') {
    if (!env.NEXT_PUBLIC_SUPABASE_URL) {
      warnings.push('NEXT_PUBLIC_SUPABASE_URL not set - will use placeholder images');
    }
    
    if (!env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      warnings.push('NEXT_PUBLIC_SUPABASE_ANON_KEY not set - will use placeholder images');
    }
    
    if (!env.SUPABASE_SERVICE_ROLE_KEY) {
      warnings.push('SUPABASE_SERVICE_ROLE_KEY not set - uploads may fail due to RLS');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    envInfo
  };
}

// Log environment status
export function logEnvironmentStatus() {
  const validation = validateEnvironment();
  
  console.log(`🔧 Environment: ${env.NODE_ENV}`);
  console.log(`📁 Config file: ${validation.envInfo.envFile}`);
  console.log(`✅ Loaded: ${validation.envInfo.loaded}`);
  
  if (validation.errors.length > 0) {
    console.error('❌ Environment Errors:');
    validation.errors.forEach(error => console.error(`  - ${error}`));
  }
  
  if (validation.warnings.length > 0) {
    console.warn('⚠️ Environment Warnings:');
    validation.warnings.forEach(warning => console.warn(`  - ${warning}`));
  }
  
  return validation;
}

// Export environment info
export { envInfo };

// Auto-validate in development
if (env.NODE_ENV === 'development') {
  const validation = validateEnvironment();
  if (!validation.isValid) {
    console.error('❌ Environment validation failed:', validation.errors);
  }
}
