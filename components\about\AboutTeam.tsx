import Image from "next/image";

export default function AboutTeam() {
  const team = [
    {
      name: "<PERSON>",
      role: "Founder & CEO",
      image: "https://placehold.co/300x300/nature/white?text=AR",
      bio: "Berpengalaman dalam teknologi dan manajemen pemakaman selama 10 tahun."
    },
    {
      name: "<PERSON><PERSON>",
      role: "Chief Technology Officer",
      image: "https://placehold.co/300x300/nature/white?text=SN",
      bio: "Ahli dalam pengembangan aplikasi dan sistem manajemen data."
    },
    {
      name: "<PERSON><PERSON>",
      role: "Operations Manager",
      image: "https://placehold.co/300x300/nature/white?text=BS",
      bio: "Spesialis dalam koordinasi dengan pengelola TPU di seluruh Indonesia."
    },
    {
      name: "<PERSON><PERSON>",
      role: "Customer Relations",
      image: "https://placehold.co/300x300/nature/white?text=DL",
      bio: "Fokus pada pengalaman pengguna dan layanan pelanggan."
    }
  ];

  return (
    <section className="mb-16">
      <h2 className="text-2xl md:text-3xl font-bold mb-8 text-center"><PERSON></h2>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {team.map((member, index) => (
          <div key={index} className="bg-memorial-900 border border-memorial-800 rounded-lg overflow-hidden">
            <div className="relative h-64 w-full">
              <Image 
                src={member.image} 
                alt={member.name}
                fill
                className="object-cover"
              />
            </div>
            <div className="p-4 text-center">
              <h3 className="text-xl font-semibold text-memorial-50">{member.name}</h3>
              <p className="text-candle-500 mb-2">{member.role}</p>
              <p className="text-memorial-300 text-sm">{member.bio}</p>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}