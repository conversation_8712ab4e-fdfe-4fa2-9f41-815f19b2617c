"use client";

import { format } from 'date-fns';
import { id } from 'date-fns/locale';
import { MapPin, Calendar, User, Heart, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { MemorialImageGallery } from './MemorialImageGallery';
import { MemorialBioSection } from './MemorialBioSection';
import { MemorialLifeStory } from './MemorialLifeStory';
import { useState, useEffect } from 'react';

interface MemorialImage {
  id: string;
  imageUrl: string;
  caption: string | null;
  createdAt: Date;
}

interface Religion {
  id: string;
  name: string;
  slug: string;
}

interface Location {
  id: string;
  name: string;
  slug: string;
  addressDetail: string;
  province: string;
  city: string;
  district: string;
  subDistrict: string;
  latitude: string;
  longitude: string;
  placeId: string | null;
}

interface Memorial {
  id: string;
  slug: string;
  name: string;
  birthPlace: string;
  birthDate: string;
  deathDate: string;
  description: string | null;
  submittedBy: string;
  evidenceName: string;
  evidenceImageUrl: string | null;
  createdAt: Date;
  updatedAt: Date;
  religion: Religion | null;
  location: Location | null;
  images: MemorialImage[];
  // Add package information from orders
  packageId?: string;
  packageName?: string;
}

interface MemorialDisplayProps {
  memorial: Memorial;
}

export default function MemorialDisplay({ memorial }: MemorialDisplayProps) {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd MMMM yyyy', { locale: id });
    } catch (error) {
      return dateString;
    }
  };

  const calculateAge = (birthDate: string, deathDate: string) => {
    try {
      const birth = new Date(birthDate);
      const death = new Date(deathDate);
      const ageInYears = death.getFullYear() - birth.getFullYear();
      const monthDiff = death.getMonth() - birth.getMonth();
      
      if (monthDiff < 0 || (monthDiff === 0 && death.getDate() < birth.getDate())) {
        return ageInYears - 1;
      }
      return ageInYears;
    } catch (error) {
      return null;
    }
  };

  const getLocationUrl = () => {
    // Only show maps link for standard and premium packages
    if (memorial.packageId === 'free') {
      return null;
    }

    if (memorial.location?.placeId) {
      return `https://www.google.com/maps/place/?q=place_id:${memorial.location.placeId}`;
    }
    if (memorial.location?.name) {
      const encodedName = encodeURIComponent(memorial.location.name);
      return `https://www.google.com/maps/search/${encodedName}`;
    }
    return null;
  };

  const age = calculateAge(memorial.birthDate, memorial.deathDate);
  const locationUrl = getLocationUrl();

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      {/* Header Section */}
      <div className="bg-memorial-900 rounded-lg border border-memorial-800 p-4 sm:p-6 mb-4 sm:mb-6">
        <div className="text-center mb-4 sm:mb-6">
          <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-memorial-50 mb-2">
            {memorial.name}
          </h1>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-1 sm:gap-2 text-memorial-300 text-base sm:text-lg">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 sm:h-5 sm:w-5" />
              <span className="text-sm sm:text-base">
                {formatDate(memorial.birthDate)} - {formatDate(memorial.deathDate)}
              </span>
            </div>
            {age && <span className="text-sm sm:text-base">({age} tahun)</span>}
          </div>
        </div>

        {/* Basic Info */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 text-xs sm:text-sm">
          <div className="flex items-start gap-2 text-memorial-300">
            <MapPin className="h-4 w-4 flex-shrink-0 mt-0.5" />
            <span>Tempat Lahir: {memorial.birthPlace}</span>
          </div>

          {memorial.religion && (
            <div className="flex items-start gap-2 text-memorial-300">
              <Heart className="h-4 w-4 flex-shrink-0 mt-0.5" />
              <span>Agama: {memorial.religion.name}</span>
            </div>
          )}

          {memorial.location && (
            <div className="flex items-start gap-2 text-memorial-300 sm:col-span-2">
              <MapPin className="h-4 w-4 flex-shrink-0 mt-0.5" />
              <span>Lokasi Pemakaman: {memorial.location.name}</span>
            </div>
          )}

          <div className="flex items-start gap-2 text-memorial-300 sm:col-span-2">
            <User className="h-4 w-4 flex-shrink-0 mt-0.5" />
            <span>Disubmit oleh: {memorial.submittedBy}</span>
          </div>
        </div>

        {/* Location Button */}
        {locationUrl && (
          <div className="mt-4 text-center">
            <Button
              variant="outline"
              size={isMobile ? "sm" : "default"}
              onClick={() => window.open(locationUrl, '_blank')}
              className="border-memorial-600 text-memorial-300 hover:bg-memorial-800 text-xs sm:text-sm"
            >
              <ExternalLink className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
              Lihat Lokasi di Maps
            </Button>
          </div>
        )}
      </div>

      {/* Images and Bio Section */}
      {isMobile ? (
        // Mobile Layout: Stack vertically
        <div className="space-y-4 mb-4">
          {/* Images */}
          <div>
            <MemorialImageGallery
              images={memorial.images.map(img => img.imageUrl)}
              name={memorial.name}
              isMobile={true}
            />
          </div>

          {/* Bio Section */}
          <div>
            <MemorialBioSection
              deceased={{
                id: memorial.id,
                name: memorial.name,
                birthYear: new Date(memorial.birthDate).getFullYear(),
                deathYear: new Date(memorial.deathDate).getFullYear(),
                images: memorial.images.map(img => img.imageUrl),
                birthPlace: memorial.birthPlace,
                birthDate: memorial.birthDate,
                deathDate: memorial.deathDate,
                cemeteryName: memorial.location?.name || 'Lokasi tidak diketahui',
                submittedBy: memorial.submittedBy,
                description: memorial.description || '',
                location: memorial.location ? {
                  name: memorial.location.name,
                  address: memorial.location.addressDetail,
                  province: memorial.location.province,
                  city: memorial.location.city,
                  district: memorial.location.district,
                  subDistrict: memorial.location.subDistrict,
                  latitude: memorial.location.latitude,
                  longitude: memorial.location.longitude,
                  place_id: memorial.location.placeId || undefined,
                } : undefined,
              }}
              packageId={memorial.packageId}
            />
          </div>
        </div>
      ) : (
        // Desktop Layout: Side by side
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          {/* Images */}
          <div className="lg:col-span-2">
            <MemorialImageGallery
              images={memorial.images.map(img => img.imageUrl)}
              name={memorial.name}
              isMobile={false}
            />
          </div>

          {/* Bio Section */}
          <div className="lg:col-span-1">
            <MemorialBioSection
              deceased={{
                id: memorial.id,
                name: memorial.name,
                birthYear: new Date(memorial.birthDate).getFullYear(),
                deathYear: new Date(memorial.deathDate).getFullYear(),
                images: memorial.images.map(img => img.imageUrl),
                birthPlace: memorial.birthPlace,
                birthDate: memorial.birthDate,
                deathDate: memorial.deathDate,
                cemeteryName: memorial.location?.name || 'Lokasi tidak diketahui',
                submittedBy: memorial.submittedBy,
                description: memorial.description || '',
                location: memorial.location ? {
                  name: memorial.location.name,
                  address: memorial.location.addressDetail,
                  province: memorial.location.province,
                  city: memorial.location.city,
                  district: memorial.location.district,
                  subDistrict: memorial.location.subDistrict,
                  latitude: memorial.location.latitude,
                  longitude: memorial.location.longitude,
                  place_id: memorial.location.placeId || undefined,
                } : undefined,
              }}
              packageId={memorial.packageId}
            />
          </div>
        </div>
      )}

      {/* Life Story Section */}
      {memorial.description && (
        <div className="mb-4 sm:mb-6">
          <MemorialLifeStory
            description={memorial.description}
          />
        </div>
      )}

      {/* Footer Info */}
      <div className="bg-memorial-900 rounded-lg border border-memorial-800 p-3 sm:p-4 text-center">
        <p className="text-memorial-400 text-xs sm:text-sm">
          Memorial ini dibuat pada {formatDate(memorial.createdAt.toISOString())}
        </p>
        <p className="text-memorial-500 text-xs mt-1">
          Bukti: {memorial.evidenceName}
        </p>
      </div>
    </div>
  );
}
