import { But<PERSON> } from "@/components/ui/button";
import { CheckCircle, ExternalLink } from "lucide-react";
import { PurchaseResult } from "@/types";

interface ConfirmationStepProps {
  purchaseResult?: PurchaseResult | null;
  onClearState?: () => void;
}

export default function ConfirmationStep({
  purchaseResult,
  onClearState
}: ConfirmationStepProps) {
  const formatPrice = (price: number) => {
    if (price === 0) return "Gratis";
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      maximumFractionDigits: 0
    }).format(price);
  };

  return (
    <div className="bg-memorial-900 p-6 rounded-lg border border-memorial-800">
      <div className="text-center mb-6">
        <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
        <h2 className="text-2xl font-semibold mb-2">Memorial Digital Berhasil Dibuat!</h2>
        <p className="text-memorial-300">
          Data memorial telah berhasil disimpan ke database dan siap diakses.
        </p>
      </div>

      {purchaseResult && (
        <div className="bg-memorial-800 p-4 rounded-lg mb-6">
          <h3 className="text-lg font-medium mb-3">Data Memorial yang Tersimpan</h3>
          <div className="space-y-3 text-sm">
            <div className="flex justify-between">
              <span className="text-memorial-300">ID Memorial:</span>
              <span className="font-medium text-candle-400">#{purchaseResult.memorialId}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-memorial-300">ID Lokasi:</span>
              <span className="font-medium">#{purchaseResult.locationId}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-memorial-300">ID Order:</span>
              <span className="font-medium">#{purchaseResult.orderId}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-memorial-300">Paket:</span>
              <span className="font-medium">{purchaseResult.packageDetails.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-memorial-300">Total Pembayaran:</span>
              <span className="font-medium text-candle-500">
                {formatPrice(purchaseResult.pricing.total)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-memorial-300">Masa Aktif:</span>
              <span className="font-medium">{purchaseResult.packageDetails.duration}</span>
            </div>
            {purchaseResult.imageUrls && purchaseResult.imageUrls.length > 0 && (
              <div className="flex justify-between">
                <span className="text-memorial-300">Foto Tersimpan:</span>
                <span className="font-medium">{purchaseResult.imageUrls.length} foto</span>
              </div>
            )}
            {purchaseResult.evidenceImageUrl && (
              <div className="flex justify-between">
                <span className="text-memorial-300">Bukti Tersimpan:</span>
                <span className="font-medium text-green-400">✓ Tersimpan</span>
              </div>
            )}
          </div>
        </div>
      )}

      <div className="bg-green-900/30 border border-green-800 rounded-lg p-4 mb-6">
        <h3 className="text-lg font-medium mb-2 text-green-200">Memorial Siap Diakses</h3>
        <ul className="text-sm text-green-300 space-y-1">
          <li>• Memorial digital telah berhasil dibuat dan tersimpan di database</li>
          <li>• Data biodata, foto, dan lokasi telah terintegrasi</li>
          <li>• Memorial dapat diakses langsung melalui link di bawah</li>
          <li>• Simpan ID Memorial untuk referensi di masa depan</li>
          {purchaseResult?.packageDetails.price === 0 && (
            <li>• Memorial gratis akan aktif selama 3 bulan</li>
          )}
        </ul>
      </div>



      <div className="flex flex-col sm:flex-row gap-3">
        <Button
          variant="outline"
          className="flex-1 border-memorial-600 text-memorial-300 hover:bg-memorial-800"
          onClick={() => {
            onClearState?.();
            window.location.href = '/';
          }}
        >
          Kembali ke Beranda
        </Button>
        {purchaseResult && (
          <Button
            className="flex-1 bg-candle-500 text-memorial-950 hover:bg-candle-400"
            onClick={() => {
              onClearState?.();
              window.location.href = `/memorial/${purchaseResult.memorialSlug}`;
            }}
          >
            <ExternalLink className="mr-2 h-4 w-4" />
            Lihat Memorial
          </Button>
        )}
      </div>
    </div>
  );
}