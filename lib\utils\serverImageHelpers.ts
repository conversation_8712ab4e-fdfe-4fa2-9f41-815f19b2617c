/**
 * Server-side only image utilities using Sharp
 * This file should only be imported in server-side code (API routes, server components)
 */

import sharp from 'sharp';
import { CompressionOptions, CompressionResult } from './imageHelpers';

export class ServerImageHelpers {
  /**
   * Compress image using Sharp (server-side only)
   * Reduces file size while maintaining good quality
   */
  static async compressImage(
    inputBuffer: Buffer, 
    options: CompressionOptions = {}
  ): Promise<CompressionResult> {
    const {
      quality = 80,
      maxWidth = 1920,
      maxHeight = 1080,
      format = 'jpeg',
      progressive = true
    } = options;

    try {
      const originalSize = inputBuffer.length;
      
      // Get image metadata
      const metadata = await sharp(inputBuffer).metadata();
      
      // Calculate resize dimensions while maintaining aspect ratio
      let resizeWidth = metadata.width;
      let resizeHeight = metadata.height;
      
      if (metadata.width && metadata.height) {
        if (metadata.width > maxWidth || metadata.height > maxHeight) {
          const aspectRatio = metadata.width / metadata.height;
          
          if (aspectRatio > 1) {
            // Landscape
            resizeWidth = Math.min(maxWidth, metadata.width);
            resizeHeight = Math.round(resizeWidth / aspectRatio);
          } else {
            // Portrait
            resizeHeight = Math.min(maxHeight, metadata.height);
            resizeWidth = Math.round(resizeHeight * aspectRatio);
          }
        }
      }

      // Create Sharp pipeline
      let pipeline = sharp(inputBuffer);

      // Resize if needed
      if (resizeWidth !== metadata.width || resizeHeight !== metadata.height) {
        pipeline = pipeline.resize(resizeWidth, resizeHeight, {
          fit: 'inside',
          withoutEnlargement: true
        });
      }

      // Apply format-specific compression
      let compressedBuffer: Buffer;
      
      switch (format) {
        case 'jpeg':
          compressedBuffer = await pipeline
            .jpeg({ 
              quality, 
              progressive,
              mozjpeg: true // Use mozjpeg for better compression
            })
            .toBuffer();
          break;
          
        case 'webp':
          compressedBuffer = await pipeline
            .webp({ 
              quality,
              effort: 6 // Higher effort for better compression
            })
            .toBuffer();
          break;
          
        case 'png':
          compressedBuffer = await pipeline
            .png({ 
              quality,
              compressionLevel: 9,
              progressive
            })
            .toBuffer();
          break;
          
        default:
          throw new Error(`Unsupported format: ${format}`);
      }

      const compressedSize = compressedBuffer.length;
      const compressionRatio = ((originalSize - compressedSize) / originalSize) * 100;

      return {
        buffer: compressedBuffer,
        originalSize,
        compressedSize,
        compressionRatio,
        format
      };

    } catch (error) {
      throw new Error(`Image compression failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Smart compression based on file size and type
   * Automatically chooses optimal settings
   */
  static async smartCompress(inputBuffer: Buffer): Promise<CompressionResult> {
    const originalSize = inputBuffer.length;
    const metadata = await sharp(inputBuffer).metadata();
    
    // Determine optimal settings based on file size and dimensions
    let options: CompressionOptions = {};
    
    if (originalSize > 3 * 1024 * 1024) {
      // Files > 3MB: Aggressive compression
      options = {
        quality: 70,
        maxWidth: 1600,
        maxHeight: 1200,
        format: 'webp',
        progressive: true
      };
    } else if (originalSize > 1 * 1024 * 1024) {
      // Files > 1MB: Moderate compression
      options = {
        quality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
        format: 'jpeg',
        progressive: true
      };
    } else {
      // Files < 1MB: Light compression
      options = {
        quality: 85,
        maxWidth: 2048,
        maxHeight: 1536,
        format: metadata.format === 'png' ? 'png' : 'jpeg',
        progressive: true
      };
    }

    return this.compressImage(inputBuffer, options);
  }

  /**
   * Get image metadata using Sharp
   */
  static async getImageMetadata(inputBuffer: Buffer) {
    try {
      return await sharp(inputBuffer).metadata();
    } catch (error) {
      throw new Error(`Failed to get image metadata: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Convert image format
   */
  static async convertFormat(
    inputBuffer: Buffer, 
    targetFormat: 'jpeg' | 'png' | 'webp',
    quality: number = 80
  ): Promise<Buffer> {
    try {
      let pipeline = sharp(inputBuffer);

      switch (targetFormat) {
        case 'jpeg':
          return await pipeline.jpeg({ quality, progressive: true }).toBuffer();
        case 'webp':
          return await pipeline.webp({ quality }).toBuffer();
        case 'png':
          return await pipeline.png({ quality, compressionLevel: 9 }).toBuffer();
        default:
          throw new Error(`Unsupported target format: ${targetFormat}`);
      }
    } catch (error) {
      throw new Error(`Format conversion failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Resize image while maintaining aspect ratio
   */
  static async resizeImage(
    inputBuffer: Buffer,
    maxWidth: number,
    maxHeight: number,
    options: {
      fit?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside';
      withoutEnlargement?: boolean;
    } = {}
  ): Promise<Buffer> {
    try {
      const { fit = 'inside', withoutEnlargement = true } = options;
      
      return await sharp(inputBuffer)
        .resize(maxWidth, maxHeight, { fit, withoutEnlargement })
        .toBuffer();
    } catch (error) {
      throw new Error(`Image resize failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Create thumbnail
   */
  static async createThumbnail(
    inputBuffer: Buffer,
    size: number = 400,
    quality: number = 75
  ): Promise<Buffer> {
    try {
      return await sharp(inputBuffer)
        .resize(size, size, { fit: 'cover' })
        .jpeg({ quality, progressive: true })
        .toBuffer();
    } catch (error) {
      throw new Error(`Thumbnail creation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Optimize image for web
   */
  static async optimizeForWeb(
    inputBuffer: Buffer,
    options: {
      maxWidth?: number;
      maxHeight?: number;
      quality?: number;
      format?: 'jpeg' | 'webp';
    } = {}
  ): Promise<CompressionResult> {
    const {
      maxWidth = 1920,
      maxHeight = 1080,
      quality = 80,
      format = 'jpeg'
    } = options;

    return this.compressImage(inputBuffer, {
      maxWidth,
      maxHeight,
      quality,
      format,
      progressive: true
    });
  }

  /**
   * Get compression presets
   */
  static getCompressionPresets() {
    return {
      // For thumbnails and previews
      thumbnail: {
        quality: 75,
        maxWidth: 400,
        maxHeight: 400,
        format: 'webp' as const,
        progressive: true
      },
      
      // For web display
      web: {
        quality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
        format: 'jpeg' as const,
        progressive: true
      },
      
      // For high quality storage
      archive: {
        quality: 90,
        maxWidth: 2560,
        maxHeight: 1920,
        format: 'jpeg' as const,
        progressive: true
      },
      
      // For mobile optimization
      mobile: {
        quality: 75,
        maxWidth: 1080,
        maxHeight: 1920,
        format: 'webp' as const,
        progressive: true
      }
    };
  }
}
