import { Textarea } from "@/components/ui/textarea";
import { Label } from "@radix-ui/react-label";

interface LifeStorySectionProps {
  description: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
}

export default function LifeStorySection({
  description,
  onChange
}: LifeStorySectionProps) {
  return (
    <div>
      <Label htmlFor="description" className="text-sm sm:text-base text-memorial-200 mb-1 block">
        <PERSON><PERSON><PERSON>dup
      </Label>
      <Textarea
        id="description"
        name="description"
        value={description}
        onChange={onChange}
        className="bg-memorial-800 border-memorial-700 text-memorial-50 min-h-[120px] sm:min-h-[150px] text-sm sm:text-base"
        placeholder="Ceritakan kisah hidup almarhum/almarhumah..."
      />
    </div>
  );
}
