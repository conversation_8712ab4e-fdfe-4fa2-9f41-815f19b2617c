# Religion Input Fix

## Problem Description

Input agama di BioDataStep kadang tidak sesuai dengan data session yang tersimpan. Masalah ini disebabkan oleh beberapa faktor:

### Issues Identified

1. **Default Value Problem**: `religionId: 0` sebagai default value
2. **Type Inconsistency**: Konversi bolak-balik antara string dan number
3. **Session Handling**: Race condition antara loading religions dan initial data
4. **Validation Logic**: Validasi yang tidak konsisten untuk religionId

## Root Causes

### 1. **Invalid Default Value**
```typescript
// ❌ Before (Problematic)
religionId: 0  // 0 is not a valid religion ID

// ✅ After (Fixed)
religionId: null  // null indicates no selection
```

### 2. **String/Number Conversion Issues**
```typescript
// ❌ Before (Problematic)
value={bioData.religionId.toString()}  // Crashes if religionId is null

// ✅ After (Fixed)
value={bioData.religionId ? bioData.religionId.toString() : ''}
```

### 3. **Session Data Restoration**
```typescript
// ❌ Before (Problematic)
const hasContent = Object.values(initialData).some(value => {
  // Doesn't handle numbers properly
  return String(value).trim() !== "";
});

// ✅ After (Fixed)
const hasContent = Object.values(initialData).some(value => {
  if (typeof value === 'number') return value > 0; // Handle religionId properly
  return String(value).trim() !== "";
});
```

## Solution Implementation

### 1. **Updated Interface**
```typescript
export interface BioData {
  // ... other fields
  religionId: number | null;  // Allow null to indicate no selection
  // ... other fields
}
```

### 2. **Fixed Default Values**
```typescript
const defaultData = {
  name: "",
  birthPlace: "",
  birthDate: "",
  deathDate: "",
  religionId: null,  // Use null instead of 0
  submittedBy: "",
  description: "",
  images: [],
  evidenceName: "",
  evidenceImage: null
};
```

### 3. **Improved handleSelectChange**
```typescript
const handleSelectChange = (name: string, value: string) => {
  if (name === 'religionId') {
    // Convert to number, handle empty string as null
    const numericValue = value === '' ? null : parseInt(value);
    setBioData(prev => ({ ...prev, [name]: numericValue }));
  } else {
    setBioData(prev => ({ ...prev, [name]: value }));
  }
};
```

### 4. **Fixed Select Component Value**
```typescript
<Select
  options={religions.map(religion => ({ value: religion.id.toString(), label: religion.name }))}
  value={bioData.religionId ? bioData.religionId.toString() : ''}  // Handle null safely
  onChange={(value) => handleSelectChange('religionId', value)}
  placeholder="Pilih Agama"
  className="bg-memorial-800 border-memorial-700 text-memorial-50 text-sm sm:text-base"
/>
```

### 5. **Updated Validation**
```typescript
if (!bioData.religionId || bioData.religionId === null) {
  ErrorHandler.handle("Agama harus dipilih", "Form Validation");
  return;
}
```

### 6. **Enhanced Session Restoration**
```typescript
if (hasContent) {
  setBioData({
    ...initialData,
    // Reset file arrays since they can't be persisted
    images: [],
    evidenceImage: null,
    // Ensure religionId is properly handled
    religionId: initialData.religionId || null
  });
}
```

## Testing Results

### Test Scenarios Covered

1. **Default Data Structure**
   - ✅ Default religionId is null
   - ✅ Type safety maintained

2. **Session Data Handling**
   - ✅ Empty session → religionId: null
   - ✅ Valid religionId (1, 2, etc.) → preserved correctly
   - ✅ Invalid religionId (0) → converted to null
   - ✅ Null religionId → remains null

3. **Select Component Value Conversion**
   - ✅ null → empty string ""
   - ✅ Valid ID → string representation
   - ✅ Zero handling improved

4. **handleSelectChange Logic**
   - ✅ Empty string → null
   - ✅ Valid string ID → number
   - ✅ Proper type conversion

5. **Validation Logic**
   - ✅ null religionId → validation fails
   - ✅ Zero religionId → validation fails
   - ✅ Valid religionId → validation passes

## Benefits of the Fix

### 1. **Data Consistency**
- Session data properly restored
- No more religionId mismatches
- Consistent type handling

### 2. **User Experience**
- Form remembers selected religion
- No unexpected resets
- Clear validation messages

### 3. **Type Safety**
- Proper TypeScript types
- Null handling throughout
- No runtime type errors

### 4. **Maintainability**
- Clear intent with null vs 0
- Consistent validation logic
- Better error handling

## Usage Examples

### Correct Religion Selection Flow

```typescript
// 1. User selects religion
handleSelectChange('religionId', '1');  // Islam selected

// 2. Data is stored correctly
bioData.religionId = 1;  // number type

// 3. Session preserves the selection
sessionData = { ...bioData, religionId: 1 };

// 4. Form restoration works correctly
setBioData({
  ...sessionData,
  religionId: sessionData.religionId || null  // 1 is preserved
});

// 5. Select component displays correctly
value={bioData.religionId ? bioData.religionId.toString() : ''}  // "1"
```

### Edge Cases Handled

```typescript
// Empty selection
religionId: null → Select value: "" → Validation: fails

// Invalid selection (should not happen but handled)
religionId: 0 → Select value: "" → Validation: fails

// Valid selection
religionId: 1 → Select value: "1" → Validation: passes
```

## Commands for Testing

```bash
# Test religion handling logic
npm run test:religion

# Test full purchase flow
npm run test:purchase:e2e

# Manual testing
npm run dev
# Visit: http://localhost:3000/purchase
```

## Migration Notes

### Breaking Changes
- `religionId` type changed from `number` to `number | null`
- Default value changed from `0` to `null`

### Backward Compatibility
- Existing session data with `religionId: 0` will be converted to `null`
- Valid religion IDs (1, 2, 3, etc.) are preserved
- No database schema changes required

## Future Improvements

1. **Enhanced Validation**
   - Validate against available religions list
   - Better error messages for invalid selections

2. **Performance Optimization**
   - Cache religion data
   - Reduce re-renders during selection

3. **Accessibility**
   - Better screen reader support
   - Keyboard navigation improvements

This fix ensures that religion input in BioDataStep works consistently with session data and provides a better user experience.
