import { db } from './index';
import { memorials, locations } from './schema';
import { eq } from 'drizzle-orm';

// Memorial queries
export const getMemorials = async () => {
  return await db.select().from(memorials);
};

export const getMemorialById = async (id: string) => {
  return await db.select().from(memorials).where(eq(memorials.id, id));
};

// Location queries
export const getLocations = async () => {
  return await db.select().from(locations);
};
