#!/usr/bin/env tsx

/**
 * Reset database and seed with initial data
 * Uses Drizzle push to sync schema directly
 */

import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { religions } from '@/db/schema';
import { env, logEnvironmentStatus } from '@/lib/env';
import { generateReligionSlug } from '@/lib/utils/slug';
import { sql } from 'drizzle-orm';
import '@/lib/forceEnv'; // Force load environment

// Log environment status
const validation = logEnvironmentStatus();

if (!validation.isValid) {
  console.error('❌ Environment validation failed. Cannot proceed.');
  process.exit(1);
}

// Database connection
const connectionString = env.DATABASE_URL;

if (!connectionString) {
  console.error('❌ DATABASE_URL not found in environment');
  process.exit(1);
}

console.log('🔗 Connection details:');
if (connectionString.includes('supabase.co')) {
  console.log('  Type: Supabase Cloud PostgreSQL');
  const maskedUrl = connectionString.replace(/:([^:@]+)@/, ':***@');
  console.log(`  URL: ${maskedUrl}`);
} else if (connectionString.includes('localhost')) {
  console.log('  Type: Local PostgreSQL');
  console.log(`  URL: ${connectionString.replace(/:([^:@]+)@/, ':***@')}`);
} else {
  console.log('  Type: External PostgreSQL');
  console.log(`  URL: ${connectionString.replace(/:([^:@]+)@/, ':***@')}`);
}
console.log('');

async function main() {
  console.log('🚀 Starting database reset and seed...\n');

  // Create connection
  const client = postgres(connectionString, { max: 1 });
  const db = drizzle(client);

  try {
    // Step 1: Enable UUID extension
    console.log('🔧 Enabling UUID extension...');
    await db.execute(sql`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`);
    console.log('✅ UUID extension enabled\n');

    // Step 2: Drop all tables in correct order (respecting foreign keys)
    console.log('🗑️  Dropping existing tables...');
    
    const dropQueries = [
      'DROP TABLE IF EXISTS memorial_images CASCADE',
      'DROP TABLE IF EXISTS orders CASCADE', 
      'DROP TABLE IF EXISTS memorials CASCADE',
      'DROP TABLE IF EXISTS locations CASCADE',
      'DROP TABLE IF EXISTS religions CASCADE',
      'DROP TABLE IF EXISTS drizzle.__drizzle_migrations CASCADE',
      'DROP SCHEMA IF EXISTS drizzle CASCADE'
    ];

    for (const query of dropQueries) {
      try {
        await db.execute(sql.raw(query));
      } catch (error: any) {
        // Ignore "does not exist" errors
        if (!error.message?.includes('does not exist')) {
          console.warn(`Warning dropping table: ${error.message}`);
        }
      }
    }
    
    console.log('✅ All tables dropped successfully\n');

    // Step 3: Create tables with UUID schema
    console.log('⚡ Creating tables with UUID schema...');
    
    // Create religions table
    await db.execute(sql`
      CREATE TABLE religions (
        id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
        slug varchar(100) NOT NULL UNIQUE,
        name varchar(100) NOT NULL UNIQUE
      )
    `);

    // Create locations table  
    await db.execute(sql`
      CREATE TABLE locations (
        id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
        slug varchar(255) NOT NULL UNIQUE,
        name varchar(255) NOT NULL,
        address_detail text NOT NULL,
        province varchar(100) NOT NULL,
        city varchar(100) NOT NULL,
        district varchar(100) NOT NULL,
        sub_district varchar(100) NOT NULL,
        latitude varchar(50) NOT NULL,
        longitude varchar(50) NOT NULL,
        place_id varchar(255),
        created_at timestamp DEFAULT now() NOT NULL
      )
    `);

    // Create memorials table
    await db.execute(sql`
      CREATE TABLE memorials (
        id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
        slug varchar(255) NOT NULL UNIQUE,
        name varchar(255) NOT NULL,
        birth_place varchar(255) NOT NULL,
        birth_date date NOT NULL,
        death_date date NOT NULL,
        religion_id uuid REFERENCES religions(id),
        location_id uuid REFERENCES locations(id),
        description text,
        submitted_by varchar(255) NOT NULL,
        evidence_name varchar(255) NOT NULL,
        evidence_image_url varchar(255),
        created_at timestamp DEFAULT now() NOT NULL,
        updated_at timestamp DEFAULT now() NOT NULL
      )
    `);

    // Create memorial_images table
    await db.execute(sql`
      CREATE TABLE memorial_images (
        id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
        memorial_id uuid NOT NULL REFERENCES memorials(id) ON DELETE CASCADE,
        image_url varchar(255) NOT NULL,
        caption text,
        created_at timestamp DEFAULT now() NOT NULL
      )
    `);

    // Create orders table
    await db.execute(sql`
      CREATE TABLE orders (
        id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
        memorial_id uuid NOT NULL REFERENCES memorials(id) ON DELETE CASCADE,
        package_id varchar(50) NOT NULL,
        package_name varchar(100) NOT NULL,
        package_price integer DEFAULT 0 NOT NULL,
        package_duration varchar(50) NOT NULL,
        package_features text NOT NULL,
        admin_fee integer DEFAULT 0 NOT NULL,
        total_price integer DEFAULT 0 NOT NULL,
        payment_method varchar(50),
        payment_status varchar(20) DEFAULT 'pending' NOT NULL,
        created_at timestamp DEFAULT now() NOT NULL,
        updated_at timestamp DEFAULT now() NOT NULL
      )
    `);

    console.log('✅ Tables created successfully\n');

    // Step 4: Create indexes
    console.log('📋 Creating indexes...');
    
    const indexes = [
      'CREATE INDEX location_slug_idx ON locations(slug)',
      'CREATE INDEX location_name_idx ON locations(name)',
      'CREATE INDEX location_province_idx ON locations(province)',
      'CREATE INDEX location_city_idx ON locations(city)',
      'CREATE INDEX memorial_slug_idx ON memorials(slug)',
      'CREATE INDEX memorial_name_idx ON memorials(name)',
      'CREATE INDEX memorial_religion_idx ON memorials(religion_id)',
      'CREATE INDEX memorial_location_idx ON memorials(location_id)',
      'CREATE INDEX memorial_birth_date_idx ON memorials(birth_date)',
      'CREATE INDEX memorial_death_date_idx ON memorials(death_date)',
      'CREATE INDEX memorial_images_memorial_idx ON memorial_images(memorial_id)',
      'CREATE INDEX order_memorial_idx ON orders(memorial_id)',
      'CREATE INDEX order_package_idx ON orders(package_id)',
      'CREATE INDEX order_status_idx ON orders(payment_status)'
    ];

    for (const indexQuery of indexes) {
      await db.execute(sql.raw(indexQuery));
    }

    console.log('✅ Indexes created successfully\n');

    // Step 5: Seed religions data
    console.log('🌱 Seeding religions data...');
    const religionData = [
      { name: 'Islam', slug: generateReligionSlug('Islam') },
      { name: 'Kristen', slug: generateReligionSlug('Kristen') },
      { name: 'Katolik', slug: generateReligionSlug('Katolik') },
      { name: 'Hindu', slug: generateReligionSlug('Hindu') },
      { name: 'Buddha', slug: generateReligionSlug('Buddha') },
      { name: 'Konghucu', slug: generateReligionSlug('Konghucu') },
    ];
    
    await db.insert(religions).values(religionData);
    console.log('✅ Religions data seeded successfully\n');

    // Step 6: Verify setup
    console.log('🔍 Verifying database setup...');
    
    const tables = await db.execute(sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('memorials', 'locations', 'orders', 'memorial_images', 'religions')
      ORDER BY table_name
    `);
    
    console.log('📋 Created tables:');
    tables.forEach((table: any) => {
      console.log(`  ✅ ${table.table_name}`);
    });

    // Check religions count
    const religionCount = await db.execute(sql`SELECT COUNT(*) as count FROM religions`);
    console.log(`📊 Religions seeded: ${(religionCount[0] as any).count} records\n`);

    console.log('🎉 Database reset and seed completed successfully!');
    console.log('💡 Database is now ready with UUID primary keys and slug fields.');
    console.log('🚀 You can now test the purchase flow in your application.');

  } catch (error) {
    console.error('❌ Database reset failed:', error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

// Run if this file is executed directly
if (require.main === module) {
  main();
}

export { main as resetAndSeed };
