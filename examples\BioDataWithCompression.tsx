/**
 * Example: How to integrate compressed image upload into BioData form
 * This shows how to replace regular image upload with compressed upload
 */

"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import CompressedImageUpload from '@/components/ui/CompressedImageUpload';
import { CompressedImageResult } from '@/hooks/useImageCompression';
import { ErrorHandler } from '@/lib/errorHandler';

interface BioDataFormData {
  name: string;
  birthDate: string;
  deathDate: string;
  memorialImages: CompressedImageResult[];
  evidenceImage: CompressedImageResult | null;
}

export default function BioDataWithCompression() {
  const [formData, setFormData] = useState<BioDataFormData>({
    name: '',
    birthDate: '',
    deathDate: '',
    memorialImages: [],
    evidenceImage: null
  });

  const [loading, setLoading] = useState(false);

  // Handle memorial images (max 2)
  const handleMemorialImagesChange = (compressedImages: CompressedImageResult[]) => {
    setFormData(prev => ({
      ...prev,
      memorialImages: compressedImages
    }));
    
    console.log('Memorial images updated:', compressedImages.map(img => ({
      size: img.size,
      originalSize: img.originalSize,
      compressionRatio: img.compressionRatio
    })));
  };

  // Handle evidence image (max 1)
  const handleEvidenceImageChange = (compressedImages: CompressedImageResult[]) => {
    setFormData(prev => ({
      ...prev,
      evidenceImage: compressedImages.length > 0 ? compressedImages[0] : null
    }));
    
    if (compressedImages.length > 0) {
      console.log('Evidence image updated:', {
        size: compressedImages[0].size,
        originalSize: compressedImages[0].originalSize,
        compressionRatio: compressedImages[0].compressionRatio
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate required fields
    if (!formData.name || !formData.birthDate || !formData.deathDate) {
      ErrorHandler.handle("Please fill in all required fields", "Form Validation");
      return;
    }

    if (formData.memorialImages.length === 0) {
      ErrorHandler.handle("Please upload at least one memorial image", "Form Validation");
      return;
    }

    if (!formData.evidenceImage) {
      ErrorHandler.handle("Please upload evidence image", "Form Validation");
      return;
    }

    setLoading(true);

    try {
      // Prepare data for API
      const submitData = {
        name: formData.name,
        birthDate: formData.birthDate,
        deathDate: formData.deathDate,
        memorialImages: formData.memorialImages.map(img => ({
          base64: img.base64,
          size: img.size,
          format: img.format
        })),
        evidenceImage: {
          base64: formData.evidenceImage.base64,
          size: formData.evidenceImage.size,
          format: formData.evidenceImage.format
        }
      };

      console.log('Submitting compressed data:', {
        memorialImagesCount: submitData.memorialImages.length,
        memorialImagesSizes: submitData.memorialImages.map(img => img.size),
        evidenceImageSize: submitData.evidenceImage.size,
        totalSize: [...submitData.memorialImages, submitData.evidenceImage]
          .reduce((sum, img) => sum + img.size, 0)
      });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      ErrorHandler.handleSuccess("BioData berhasil disimpan dengan gambar terkompresi!");
      
      // Reset form
      setFormData({
        name: '',
        birthDate: '',
        deathDate: '',
        memorialImages: [],
        evidenceImage: null
      });

    } catch (error) {
      ErrorHandler.handle(error, "Form Submission");
    } finally {
      setLoading(false);
    }
  };

  const getTotalCompressedSize = () => {
    const memorialSize = formData.memorialImages.reduce((sum, img) => sum + img.size, 0);
    const evidenceSize = formData.evidenceImage?.size || 0;
    return memorialSize + evidenceSize;
  };

  const getTotalOriginalSize = () => {
    const memorialSize = formData.memorialImages.reduce((sum, img) => sum + img.originalSize, 0);
    const evidenceSize = formData.evidenceImage?.originalSize || 0;
    return memorialSize + evidenceSize;
  };

  const getAverageCompressionRatio = () => {
    const allImages = [...formData.memorialImages];
    if (formData.evidenceImage) {
      allImages.push(formData.evidenceImage);
    }
    
    if (allImages.length === 0) return 0;
    
    const totalRatio = allImages.reduce((sum, img) => sum + img.compressionRatio, 0);
    return totalRatio / allImages.length;
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card className="bg-memorial-800 border-memorial-700">
        <CardHeader>
          <CardTitle className="text-memorial-50">
            BioData Form with Image Compression
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Info */}
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-memorial-300 mb-2">
                  Full Name *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 bg-memorial-700 border border-memorial-600 rounded-lg text-memorial-50 focus:outline-none focus:ring-2 focus:ring-candle-500"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-memorial-300 mb-2">
                  Birth Date *
                </label>
                <input
                  type="date"
                  value={formData.birthDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, birthDate: e.target.value }))}
                  className="w-full px-3 py-2 bg-memorial-700 border border-memorial-600 rounded-lg text-memorial-50 focus:outline-none focus:ring-2 focus:ring-candle-500"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-memorial-300 mb-2">
                  Death Date *
                </label>
                <input
                  type="date"
                  value={formData.deathDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, deathDate: e.target.value }))}
                  className="w-full px-3 py-2 bg-memorial-700 border border-memorial-600 rounded-lg text-memorial-50 focus:outline-none focus:ring-2 focus:ring-candle-500"
                  required
                />
              </div>
            </div>

            {/* Memorial Images */}
            <div>
              <label className="block text-sm font-medium text-memorial-300 mb-2">
                Memorial Images * (Maximum 2)
              </label>
              <CompressedImageUpload
                maxImages={2}
                onImagesChange={handleMemorialImagesChange}
                showCompressionStats={true}
              />
            </div>

            {/* Evidence Image */}
            <div>
              <label className="block text-sm font-medium text-memorial-300 mb-2">
                Evidence Image * (Death Certificate, etc.)
              </label>
              <CompressedImageUpload
                maxImages={1}
                onImagesChange={handleEvidenceImageChange}
                showCompressionStats={true}
              />
            </div>

            {/* Compression Summary */}
            {(formData.memorialImages.length > 0 || formData.evidenceImage) && (
              <Card className="bg-memorial-700 border-memorial-600">
                <CardContent className="p-4">
                  <h4 className="font-medium text-memorial-50 mb-3">Compression Summary</h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
                    <div className="text-center">
                      <div className="text-lg font-bold text-candle-500">
                        {getAverageCompressionRatio().toFixed(1)}%
                      </div>
                      <div className="text-memorial-400">Avg. Reduction</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-green-500">
                        {((getTotalOriginalSize() - getTotalCompressedSize()) / 1024 / 1024).toFixed(2)}MB
                      </div>
                      <div className="text-memorial-400">Size Saved</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-blue-500">
                        {(getTotalCompressedSize() / 1024 / 1024).toFixed(2)}MB
                      </div>
                      <div className="text-memorial-400">Final Size</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-purple-500">
                        {formData.memorialImages.length + (formData.evidenceImage ? 1 : 0)}
                      </div>
                      <div className="text-memorial-400">Images</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Submit Button */}
            <div className="flex justify-end">
              <Button
                type="submit"
                disabled={loading}
                className="bg-candle-500 hover:bg-candle-400 text-memorial-950 px-8"
              >
                {loading ? 'Saving...' : 'Save BioData'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
