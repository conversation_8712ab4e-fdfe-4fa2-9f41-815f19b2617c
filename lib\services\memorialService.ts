import { db } from '@/db';
import { memorials, locations, religions, memorialImages, orders } from '@/db/schema';
import { eq, desc, sql, like, or, ilike, inArray } from 'drizzle-orm';
import { ApiError } from '@/utils/ApiError';
import { BaseService, PaginationOptions, PaginatedResponse, handleServiceError } from '@/lib/services/base/BaseService';
import { MemorialApiResponse, PaginationInfo } from '@/types';

export class MemorialService extends BaseService {
  constructor() {
    super('MemorialService', {
      enableCache: true,
      cacheTimeout: 5 * 60 * 1000, // 5 minutes
      enableLogging: true,
    });
  }
  /**
   * Get memorial by ID with related data
   */
  async getById(id: string) {
    try {
      this.validateId(id, 'Memorial ID');

      const memorial = await db.query.memorials.findFirst({
        where: (memorials, { eq }) => eq(memorials.id, id),
        with: {
          memorialImages: true,
          location: true,
          religion: true,
        }
      });

      if (!memorial) {
        throw new ApiError('Memorial not found', 404, 'MEMORIAL_NOT_FOUND');
      }

      return memorial;
    } catch (error) {
      handleServiceError(error, 'getMemorialById', this.serviceName);
    }
  }

  // Legacy method for backward compatibility
  async getMemorialById(id: string) {
    return this.getById(id);
  }

  /**
   * Get memorial by slug with related data
   */
  async getMemorialBySlug(slug: string) {
    try {
      const memorial = await db.query.memorials.findFirst({
        where: (memorials, { eq }) => eq(memorials.slug, slug),
        with: {
          memorialImages: true,
          location: true,
          religion: true,
          orders: true, // Include orders to get package info
        }
      });

      if (!memorial) {
        return null; // Return null instead of throwing error for slug lookup
      }

      // Add package info from the latest order
      const latestOrder = memorial.orders?.[0]; // Assuming orders are ordered by creation date
      const memorialWithPackage = {
        ...memorial,
        packageId: latestOrder?.packageId || 'free',
        packageName: latestOrder?.packageName || 'Paket Free',
      };

      return memorialWithPackage;

    } catch (error) {
      return this.handleError(error, 'getMemorialBySlug');
    }
  }

  /**
   * Get all memorials with pagination and filters
   */
  async getAll(options: PaginationOptions & { search?: string } = { page: 1, limit: 10 }) {
    this.validatePagination(options);

    return this.executeOperation(
      async () => {
        const { page, limit, search } = options;
        const offset = (page - 1) * limit;

        // Get total count for pagination
        const [totalCountResult] = await db
          .select({ count: sql<number>`count(*)` })
          .from(memorials);

        const totalItems = totalCountResult.count;
        const totalPages = Math.ceil(totalItems / limit);

        // Build main query with package information
        let query = db
          .select({
            id: memorials.id,
            slug: memorials.slug,
            name: memorials.name,
            birthPlace: memorials.birthPlace,
            birthDate: memorials.birthDate,
            deathDate: memorials.deathDate,
            description: memorials.description,
            submittedBy: memorials.submittedBy,
            evidenceName: memorials.evidenceName,
            evidenceImageUrl: memorials.evidenceImageUrl,
            createdAt: memorials.createdAt,
            updatedAt: memorials.updatedAt,
            // Location data
            locationName: locations.name,
            locationAddress: locations.addressDetail,
            locationProvince: locations.province,
            locationCity: locations.city,
            locationDistrict: locations.district,
            locationSubDistrict: locations.subDistrict,
            locationLatitude: locations.latitude,
            locationLongitude: locations.longitude,
            locationPlaceId: locations.placeId,
            // Religion data
            religionName: religions.name,
            // Package/Order data
            packageId: orders.packageId,
            packageName: orders.packageName,
            packagePrice: orders.packagePrice,
            packageDuration: orders.packageDuration,
            packageFeatures: orders.packageFeatures,
            paymentStatus: orders.paymentStatus,
          })
          .from(memorials)
          .leftJoin(locations, eq(memorials.locationId, locations.id))
          .leftJoin(religions, eq(memorials.religionId, religions.id))
          .leftJoin(orders, eq(memorials.id, orders.memorialId))
          .orderBy(desc(memorials.createdAt))
          .limit(limit)
          .offset(offset);

        // Add search functionality if search term provided
        if (search && search.trim()) {
          query = query.where(
            or(
              ilike(memorials.name, `%${search.trim()}%`),
              ilike(memorials.birthPlace, `%${search.trim()}%`),
              ilike(memorials.submittedBy, `%${search.trim()}%`),
              ilike(locations.name, `%${search.trim()}%`)
            )
          );
        }

        const results = await query;

        // Optimize: Get all images in one query instead of N+1
        const memorialIds = results.map(m => m.id);
        const allImages = memorialIds.length > 0 ? await db
          .select({
            id: memorialImages.id,
            memorialId: memorialImages.memorialId,
            url: memorialImages.imageUrl,
            caption: memorialImages.caption,
          })
          .from(memorialImages)
          .where(inArray(memorialImages.memorialId, memorialIds)) : [];

        // Group images by memorial ID
        const imagesByMemorialId = allImages.reduce((acc, image) => {
          if (!acc[image.memorialId]) {
            acc[image.memorialId] = [];
          }
          acc[image.memorialId].push({
            id: image.id,
            url: image.url,
            caption: image.caption,
          });
          return acc;
        }, {} as Record<string, Array<{ id: string; url: string; caption: string | null }>>);

        // Build final results with all data including package info
        const memorialsWithAllData = results.map((memorial) => ({
          ...memorial,
          images: imagesByMemorialId[memorial.id] || [],
          birthYear: new Date(memorial.birthDate).getFullYear(),
          deathYear: new Date(memorial.deathDate).getFullYear(),
          cemeteryName: memorial.locationName || 'Unknown Cemetery',
          location: memorial.locationName ? {
            name: memorial.locationName,
            address: memorial.locationAddress,
            province: memorial.locationProvince,
            city: memorial.locationCity,
            district: memorial.locationDistrict,
            subDistrict: memorial.locationSubDistrict,
            latitude: memorial.locationLatitude,
            longitude: memorial.locationLongitude,
            place_id: memorial.locationPlaceId,
          } : null,
          // Package information with defaults
          packageId: memorial.packageId || 'free',
          packageName: memorial.packageName || 'Paket Free',
          packagePrice: memorial.packagePrice || 0,
          packageDuration: memorial.packageDuration || '3 bulan',
          packageFeatures: memorial.packageFeatures || JSON.stringify(['Memorial digital dasar', '1 foto', 'Informasi dasar almarhum/almarhumah', 'Masa aktif 3 bulan']),
          paymentStatus: memorial.paymentStatus || 'paid',
        }));

        const pagination: PaginationInfo = {
          currentPage: page,
          totalPages,
          totalItems,
          itemsPerPage: limit,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
          nextPage: page < totalPages ? page + 1 : null,
          prevPage: page > 1 ? page - 1 : null,
        };

        return this.createPaginatedResponse(memorialsWithAllData, pagination);
      },
      'getAllMemorials',
      `memorials:page:${options.page}:limit:${options.limit}${options.search ? `:search:${options.search}` : ''}`
    );
  }

  // Legacy method for backward compatibility
  async getAllMemorials(options?: {
    page?: number;
    limit?: number;
    search?: string;
  }) {
    return this.getAll({
      page: options?.page || 1,
      limit: options?.limit || 10,
      search: options?.search,
    });
  }

  /**
   * Search memorials by name or other criteria
   * Enhanced with better search functionality
   */
  async searchMemorials(searchTerm: string, options?: {
    page?: number;
    limit?: number;
  }) {
    // Use the improved getAll method with search functionality
    return this.getAll({
      page: options?.page || 1,
      limit: options?.limit || 10,
      search: searchTerm,
    });
  }

  /**
   * Get memorials by package type
   */
  async getMemorialsByPackage(packageId: string, options?: {
    page?: number;
    limit?: number;
  }) {
    return this.executeOperation(
      async () => {
        const page = options?.page || 1;
        const limit = options?.limit || 10;
        const offset = (page - 1) * limit;

        // Get total count for pagination
        const [totalCountResult] = await db
          .select({ count: sql<number>`count(*)` })
          .from(memorials)
          .leftJoin(orders, eq(memorials.id, orders.memorialId))
          .where(eq(orders.packageId, packageId));

        const totalItems = totalCountResult.count;
        const totalPages = Math.ceil(totalItems / limit);

        const results = await db
          .select({
            id: memorials.id,
            slug: memorials.slug,
            name: memorials.name,
            birthPlace: memorials.birthPlace,
            birthDate: memorials.birthDate,
            deathDate: memorials.deathDate,
            packageId: orders.packageId,
            packageName: orders.packageName,
            packagePrice: orders.packagePrice,
            paymentStatus: orders.paymentStatus,
            createdAt: memorials.createdAt,
          })
          .from(memorials)
          .leftJoin(orders, eq(memorials.id, orders.memorialId))
          .where(eq(orders.packageId, packageId))
          .orderBy(desc(memorials.createdAt))
          .limit(limit)
          .offset(offset);

        const pagination: PaginationInfo = {
          currentPage: page,
          totalPages,
          totalItems,
          itemsPerPage: limit,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
          nextPage: page < totalPages ? page + 1 : null,
          prevPage: page > 1 ? page - 1 : null,
        };

        return this.createPaginatedResponse(results, pagination);
      },
      'getMemorialsByPackage',
      `memorials:package:${packageId}:page:${options?.page || 1}:limit:${options?.limit || 10}`
    );
  }

  /**
   * Get package statistics
   */
  async getPackageStats() {
    return this.executeOperation(
      async () => {
        const stats = await db
          .select({
            packageId: orders.packageId,
            packageName: orders.packageName,
            count: sql<number>`count(*)`,
            totalRevenue: sql<number>`sum(${orders.packagePrice})`,
          })
          .from(orders)
          .groupBy(orders.packageId, orders.packageName)
          .orderBy(sql`count(*) desc`);

        return {
          success: true,
          data: stats,
        };
      },
      'getPackageStats',
      'package:stats'
    );
  }
  /**
   * Get order information for a memorial
   */
  async getMemorialOrder(memorialId: string) {
    return this.executeOperation(
      async () => {
        const result = await db
          .select({
            id: orders.id,
            memorialId: orders.memorialId,
            packageId: orders.packageId,
            packageName: orders.packageName,
            packagePrice: orders.packagePrice,
            packageDuration: orders.packageDuration,
            packageFeatures: orders.packageFeatures,
            adminFee: orders.adminFee,
            totalPrice: orders.totalPrice,
            paymentMethod: orders.paymentMethod,
            paymentStatus: orders.paymentStatus,
            createdAt: orders.createdAt,
            updatedAt: orders.updatedAt,
          })
          .from(orders)
          .where(eq(orders.memorialId, memorialId))
          .orderBy(desc(orders.createdAt))
          .limit(1);

        return {
          success: true,
          data: result[0] || null,
        };
      },
      'getMemorialOrder',
      `memorial:${memorialId}:order`
    );
  }

  /**
   * Update order payment status
   */
  async updateOrderPaymentStatus(orderId: string, paymentStatus: string) {
    return this.executeOperation(
      async () => {
        const result = await db
          .update(orders)
          .set({
            paymentStatus,
            updatedAt: new Date(),
          })
          .where(eq(orders.id, orderId))
          .returning();

        if (result.length === 0) {
          throw new ApiError('Order not found', 404);
        }

        return {
          success: true,
          data: result[0],
        };
      },
      'updateOrderPaymentStatus'
      // No cache key for updates
    );
  }
}

export const memorialService = new MemorialService();
