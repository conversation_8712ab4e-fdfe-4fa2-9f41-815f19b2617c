"use client";

import { Modal } from "@/components/ui/modal";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useSearchStore } from "@/store/useSearchStore";
import { Search, User, Calendar } from "lucide-react";
import { useState } from "react";

interface SearchModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function SearchModal({ isOpen, onClose }: SearchModalProps) {
  const { searchQuery, setSearchQuery } = useSearchStore();
  const [name, setName] = useState("");
  const [birthDate, setBirthDate] = useState("");
  const [deathDate, setDeathDate] = useState("");

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Implement search functionality
    console.log("Searching for:", { name, birthDate, deathDate });
    // You could navigate to search results page or filter content
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Cari Pemakaman"
    >
      <form onSubmit={handleSearch} className="space-y-4">
        <div className="relative">
          <Input
            type="text"
            placeholder="Nama almarhum/almarhumah..."
            className="bg-memorial-800 border-memorial-700 text-memorial-50 pl-10 pr-4 py-3"
            value={name}
            onChange={(e) => setName(e.target.value)}
          />
          <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-memorial-400" />
        </div>
        
        <div className="flex flex-col md:flex-row w-full items-start md:items-center space-y-4 md:space-y-0 md:space-x-4">
          <div className="w-full space-y-2">
            <label className="text-sm text-memorial-300 block">Tanggal Lahir</label>
            <div className="relative">
              <Input
                type="date"
                className="bg-memorial-800 border-memorial-700 text-memorial-50 pl-10 pr-4 py-3"
                value={birthDate}
                onChange={(e) => setBirthDate(e.target.value)}
              />
              <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-memorial-400" />
            </div>
          </div>
          
          <div className="w-full space-y-2">
            <label className="text-sm text-memorial-300 block">Tanggal Meninggal</label>
            <div className="relative">
              <Input
                type="date"
                className="bg-memorial-800 border-memorial-700 text-memorial-50 pl-10 pr-4 py-3"
                value={deathDate}
                onChange={(e) => setDeathDate(e.target.value)}
              />
              <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-memorial-400" />
            </div>
          </div>
        </div>
       
        
        <Button 
          type="submit"
          className="w-full bg-candle-500 text-memorial-950 hover:bg-candle-400"
        >
          <Search className="h-5 w-5 mr-2" />
          Cari
        </Button>
      </form>
    </Modal>
  );
}
