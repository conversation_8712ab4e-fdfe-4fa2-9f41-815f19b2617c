import { Input } from "@/components/ui/input";
import { Label } from "@radix-ui/react-label";
import { Calendar } from "lucide-react";
import { DateHelpers } from "@/lib/utils/dateHelpers";

interface BasicInfoSectionProps {
  name: string;
  birthPlace: string;
  birthDate: string;
  deathDate: string;
  submittedBy: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export default function BasicInfoSection({
  name,
  birthPlace,
  birthDate,
  deathDate,
  submittedBy,
  onChange
}: BasicInfoSectionProps) {
  const dateInputAttributes = DateHelpers.getDateInputAttributes();

  return (
    <div className="space-y-3 sm:space-y-4">
      <div>
        <Label htmlFor="name" className="text-sm sm:text-base text-memorial-200 mb-1 block">
          Nama <PERSON>gkap
        </Label>
        <Input
          id="name"
          name="name"
          value={name}
          onChange={onChange}
          className="bg-memorial-800 border-memorial-700 text-memorial-50 text-sm sm:text-base"
          placeholder="Masukkan nama lengkap almarhum/almarhumah"
        />
      </div>
      
      <div>
        <Label htmlFor="birthPlace" className="text-sm sm:text-base text-memorial-200 mb-1 block">
          Tempat Lahir
        </Label>
        <Input
          id="birthPlace"
          name="birthPlace"
          value={birthPlace}
          onChange={onChange}
          className="bg-memorial-800 border-memorial-700 text-memorial-50 text-sm sm:text-base"
          placeholder="Masukkan tempat lahir"
        />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
        <div>
          <Label htmlFor="birthDate" className="text-sm sm:text-base text-memorial-200 mb-1 block">
            Tanggal Lahir
          </Label>
          <div className="relative">
            <Input
              id="birthDate"
              name="birthDate"
              type="date"
              value={birthDate}
              onChange={onChange}
              min={dateInputAttributes.min}
              max={dateInputAttributes.max}
              className="bg-memorial-800 border-memorial-700 text-memorial-50 pl-10 text-sm sm:text-base"
            />
            <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 sm:h-5 sm:w-5 text-memorial-400" />
          </div>
        </div>
        
        <div>
          <Label htmlFor="deathDate" className="text-sm sm:text-base text-memorial-200 mb-1 block">
            Tanggal Meninggal
          </Label>
          <div className="relative">
            <Input
              id="deathDate"
              name="deathDate"
              type="date"
              value={deathDate}
              onChange={onChange}
              min={dateInputAttributes.min}
              max={dateInputAttributes.max}
              className="bg-memorial-800 border-memorial-700 text-memorial-50 pl-10 text-sm sm:text-base"
            />
            <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 sm:h-5 sm:w-5 text-memorial-400" />
          </div>
        </div>
      </div>
      
      <div>
        <Label htmlFor="submittedBy" className="text-sm sm:text-base text-memorial-200 mb-1 block">
          Diajukan Oleh
        </Label>
        <Input
          id="submittedBy"
          name="submittedBy"
          value={submittedBy}
          onChange={onChange}
          className="bg-memorial-800 border-memorial-700 text-memorial-50 text-sm sm:text-base"
          placeholder="Nama pengaju/keluarga"
        />
      </div>
    </div>
  );
}
