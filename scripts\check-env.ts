#!/usr/bin/env tsx

/**
 * Script to check environment variables configuration
 * Useful for debugging environment issues
 */

import { config } from 'dotenv';
import { resolve } from 'path';

function checkEnvironment() {
  console.log('🔍 Checking Environment Configuration...\n');

  // Determine which env file to load based on NODE_ENV
  const nodeEnv = process.env.NODE_ENV || 'development';
  console.log(`📋 NODE_ENV: ${nodeEnv}`);

  // Load appropriate .env file
  let envFile = '.env.local';
  if (nodeEnv === 'production') {
    envFile = '.env.production';
  } else if (nodeEnv === 'development') {
    envFile = '.env.local';
  }

  console.log(`📁 Loading environment from: ${envFile}`);

  // Load environment variables
  const envPath = resolve(process.cwd(), envFile);
  const result = config({ path: envPath });

  if (result.error) {
    console.log(`⚠️  Could not load ${envFile}: ${result.error.message}`);
    console.log('   Falling back to system environment variables');
  } else {
    console.log(`✅ Successfully loaded ${envFile}`);
  }

  console.log('\n🔧 Environment Variables Status:');

  // Check database configuration
  console.log('\n📊 Database Configuration:');
  const dbUrl = process.env.DATABASE_URL;
  if (dbUrl) {
    // Mask password for security
    const maskedUrl = dbUrl.replace(/:([^:@]+)@/, ':***@');
    console.log(`  ✅ DATABASE_URL: ${maskedUrl}`);
  } else {
    console.log('  ❌ DATABASE_URL: Not set');
  }

  // Check Google Maps API
  console.log('\n🗺️  Google Maps Configuration:');
  const mapsKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
  if (mapsKey) {
    const maskedKey = mapsKey.substring(0, 8) + '***' + mapsKey.substring(mapsKey.length - 4);
    console.log(`  ✅ NEXT_PUBLIC_GOOGLE_MAPS_API_KEY: ${maskedKey}`);
  } else {
    console.log('  ❌ NEXT_PUBLIC_GOOGLE_MAPS_API_KEY: Not set');
  }

  // Check Supabase configuration
  console.log('\n☁️  Supabase Configuration:');
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (supabaseUrl) {
    console.log(`  ✅ NEXT_PUBLIC_SUPABASE_URL: ${supabaseUrl}`);
  } else {
    console.log('  ⚠️  NEXT_PUBLIC_SUPABASE_URL: Not set (will use placeholder images)');
  }

  if (supabaseKey) {
    const maskedKey = supabaseKey.substring(0, 12) + '***' + supabaseKey.substring(supabaseKey.length - 4);
    console.log(`  ✅ NEXT_PUBLIC_SUPABASE_ANON_KEY: ${maskedKey}`);
  } else {
    console.log('  ⚠️  NEXT_PUBLIC_SUPABASE_ANON_KEY: Not set (will use placeholder images)');
  }

  // Environment-specific recommendations
  console.log('\n💡 Recommendations:');
  if (nodeEnv === 'production') {
    console.log('  📦 Production Environment:');
    console.log('    - Ensure all production secrets are set');
    console.log('    - Use Supabase Cloud for database and storage');
    console.log('    - Verify Google Maps API key has production quotas');
    
    if (!supabaseUrl || !supabaseKey) {
      console.log('    ⚠️  Supabase not configured - app will use placeholder images');
    }
  } else {
    console.log('  🛠️  Development Environment:');
    console.log('    - Local PostgreSQL recommended for database');
    console.log('    - Supabase configuration is optional');
    console.log('    - App will gracefully fallback to placeholder images');
  }

  console.log('\n✨ Environment check completed!');
}

// Run the check
if (require.main === module) {
  checkEnvironment();
}

export { checkEnvironment };
