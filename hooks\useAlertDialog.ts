import { useState } from "react";

interface AlertDialogConfig {
  title: string;
  description: string;
  confirmText?: string;
  cancelText?: string;
  variant?: "default" | "destructive";
  onConfirm: () => void;
}

export function useAlertDialog() {
  const [isOpen, setIsOpen] = useState(false);
  const [config, setConfig] = useState<AlertDialogConfig | null>(null);

  const showAlert = (alertConfig: AlertDialogConfig) => {
    setConfig(alertConfig);
    setIsOpen(true);
  };

  const hideAlert = () => {
    setIsOpen(false);
    setConfig(null);
  };

  const handleConfirm = () => {
    if (config?.onConfirm) {
      config.onConfirm();
    }
    hideAlert();
  };

  return {
    isOpen,
    config,
    showAlert,
    hideAlert,
    handleConfirm,
  };
}
