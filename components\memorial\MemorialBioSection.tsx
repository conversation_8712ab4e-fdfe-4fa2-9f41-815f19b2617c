import { Deceased } from "@/data/deceased";
import { BioItem, BioItemWithLocation } from "./BioItem";
import { formatDate } from "@/lib/utils/memorialUtils";

interface MemorialBioSectionProps {
  deceased: Deceased;
  packageId?: string; 
}

export function MemorialBioSection({ deceased, packageId }: MemorialBioSectionProps) {
  return (
    <div className="w-full space-y-2 sm:space-y-3">
      <BioItem label="Nama" value={deceased.name} />
      <BioItem label="Tempat Lahir" value={deceased.birthPlace} />
      <BioItem label="Tanggal Lahir" value={formatDate(deceased.birthDate)} />
      <BioItem label="Tanggal Meninggal" value={formatDate(deceased.deathDate)} />
      <BioItemWithLocation
        label="Tempat Pemakaman"
        value={deceased.cemeteryName}
        packageId={packageId}
        location={deceased.location ? {
          placeId: deceased.location.place_id,
          name: deceased.cemeteryName,
        } : null}
      />
      <BioItem label="Di<PERSON><PERSON><PERSON>" value={deceased.submittedBy} />
    </div>
  );
}
