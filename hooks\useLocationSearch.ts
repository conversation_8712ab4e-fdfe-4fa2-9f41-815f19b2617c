import { useState, useEffect } from "react";
import { api<PERSON><PERSON><PERSON>, <PERSON>rrorHand<PERSON> } from "@/lib/errorHandler";
import { LocationSearchResult, LocationFormData } from "@/types/location";
import { useCompleteAddress } from "@/hooks/useRegions";

interface UseLocationSearchProps {
  initialData?: LocationSearchResult;
}

export function useLocationSearch({ initialData }: UseLocationSearchProps) {
  const [result, setResult] = useState<LocationSearchResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { buildAddress } = useCompleteAddress();

  // Auto-load result if initialData contains location coordinates
  useEffect(() => {
    if (initialData && initialData.lokasi && initialData.lokasi.latitude && initialData.lokasi.longitude) {
      setResult(initialData);
    }
  }, [initialData]);

  // Reset result when initialData is null (reset)
  useEffect(() => {
    if (!initialData) {
      setResult(null);
      setError(null);
    }
  }, [initialData]);

  const searchLocation = async (formData: LocationFormData): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      // Build complete address using hook
      const addressData = await buildAddress({
        provinceId: formData.provinsi,
        regencyId: formData.kabupaten_kota,
        districtId: formData.kecamatan,
        villageId: formData.kelurahan,
        detailAddress: formData.alamat_detail,
      });

      if (!addressData) {
        setError("Gagal membangun alamat lengkap");
        return false;
      }

      const data = await apiRequest<{data: any}>(
        '/api/location',
        {
          method: 'POST',
          body: JSON.stringify(addressData),
        },
        "Location Search"
      );

      if (data?.data) {
        setResult(data.data);
        ErrorHandler.handleSuccess("Lokasi berhasil ditemukan!");
        return true;
      } else {
        setError("Gagal mencari lokasi");
        return false;
      }
    } catch (error) {
      ErrorHandler.handle(error, "Location Search");
      setError("Gagal mencari lokasi");
      return false;
    } finally {
      setLoading(false);
    }
  };

  const updateResult = (newResult: LocationSearchResult) => {
    setResult(newResult);
  };

  const clearResult = () => {
    setResult(null);
    setError(null);
  };

  return {
    result,
    loading,
    error,
    searchLocation,
    updateResult,
    clearResult,
    setResult
  };
}
