interface LoadingProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export default function Loading({ 
  message = "Memuat...", 
  size = 'md',
  className = "" 
}: LoadingProps) {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-8 w-8", 
    lg: "h-12 w-12"
  };

  return (
    <div className={`flex justify-center items-center py-12 ${className}`}>
      <div className={`animate-spin rounded-full border-b-2 border-candle-500 ${sizeClasses[size]}`}></div>
      <span className="ml-3 text-memorial-300">{message}</span>
    </div>
  );
}

// Variant untuk loading dalam container/card
export function LoadingCard({ 
  title = "Loading", 
  message = "Memuat data...",
  className = ""
}: {
  title?: string;
  message?: string;
  className?: string;
}) {
  return (
    <div className={`bg-memorial-900 p-6 rounded-lg border border-memorial-800 ${className}`}>
      <h2 className="text-xl font-semibold mb-4">{title}</h2>
      <Loading message={message} />
    </div>
  );
}
