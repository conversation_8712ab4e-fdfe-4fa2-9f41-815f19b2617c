import { NextResponse } from 'next/server';
import { handleApiError } from '@/utils/errorHandler';
import { db } from '@/db';
import { religions } from '@/db/schema';

export async function GET() {
  try {
    const religionData = await db.select().from(religions);

    return NextResponse.json({
      success: true,
      data: religionData
    });
  } catch (error) {
    return handleApiError(error);
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
