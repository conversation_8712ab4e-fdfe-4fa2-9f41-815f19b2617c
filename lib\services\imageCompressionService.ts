import { ImageHelpers, CompressionOptions, CompressionResult } from '@/lib/utils/imageHelpers';
import { ServerImageHelpers } from '@/lib/utils/serverImageHelpers';
import { ApiError } from '@/utils/ApiError';

export interface ProcessedImage {
  original: {
    size: number;
    format: string;
  };
  compressed: {
    buffer: Buffer;
    size: number;
    format: string;
    base64: string;
  };
  metadata: {
    compressionRatio: number;
    sizeSaved: number;
    processingTime: number;
  };
}

export class ImageCompressionService {
  private static readonly MAX_ORIGINAL_SIZE = 5 * 1024 * 1024; // 5MB
  private static readonly TARGET_SIZE = 500 * 1024; // 500KB target
  private static readonly MIN_QUALITY = 60;
  private static readonly MAX_QUALITY = 95;

  /**
   * Process and compress image with automatic optimization
   */
  static async processImage(
    input: Buffer | string,
    options: Partial<CompressionOptions> = {}
  ): Promise<ProcessedImage> {
    const startTime = Date.now();

    try {
      // Convert input to buffer
      let inputBuffer: Buffer;
      if (typeof input === 'string') {
        inputBuffer = ImageHelpers.base64ToBuffer(input);
      } else {
        inputBuffer = input;
      }

      // Validate input size
      if (inputBuffer.length > this.MAX_ORIGINAL_SIZE) {
        throw new ApiError(
          `File too large. Maximum size is ${ImageHelpers.formatFileSize(this.MAX_ORIGINAL_SIZE)}`,
          400,
          'FILE_TOO_LARGE'
        );
      }

      const originalSize = inputBuffer.length;
      
      // Use smart compression if no specific options provided
      let compressionResult: CompressionResult;

      if (Object.keys(options).length === 0) {
        compressionResult = await ServerImageHelpers.smartCompress(inputBuffer);
      } else {
        compressionResult = await ServerImageHelpers.compressImage(inputBuffer, options);
      }

      // If still too large, apply progressive compression
      if (compressionResult.compressedSize > this.TARGET_SIZE) {
        compressionResult = await this.progressiveCompress(inputBuffer);
      }

      const processingTime = Date.now() - startTime;
      const sizeSaved = originalSize - compressionResult.compressedSize;

      return {
        original: {
          size: originalSize,
          format: 'unknown' // We could detect this with sharp metadata
        },
        compressed: {
          buffer: compressionResult.buffer,
          size: compressionResult.compressedSize,
          format: compressionResult.format,
          base64: ImageHelpers.bufferToBase64(
            compressionResult.buffer, 
            `image/${compressionResult.format}`
          )
        },
        metadata: {
          compressionRatio: compressionResult.compressionRatio,
          sizeSaved,
          processingTime
        }
      };

    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      
      console.error('Image compression error:', error);
      throw new ApiError(
        'Failed to process image',
        500,
        'COMPRESSION_ERROR'
      );
    }
  }

  /**
   * Progressive compression - reduces quality until target size is reached
   */
  private static async progressiveCompress(inputBuffer: Buffer): Promise<CompressionResult> {
    let quality = this.MAX_QUALITY;
    let bestResult: CompressionResult | null = null;

    // Try different quality levels
    while (quality >= this.MIN_QUALITY) {
      try {
        const result = await ServerImageHelpers.compressImage(inputBuffer, {
          quality,
          format: 'webp', // WebP generally provides better compression
          progressive: true
        });

        bestResult = result;

        // If we've reached target size, use this result
        if (result.compressedSize <= this.TARGET_SIZE) {
          break;
        }

        // Reduce quality for next iteration
        quality -= 10;

      } catch (error) {
        console.warn(`Compression failed at quality ${quality}:`, error);
        quality -= 10;
      }
    }

    // If progressive compression failed, try JPEG as fallback
    if (!bestResult || bestResult.compressedSize > this.TARGET_SIZE) {
      try {
        bestResult = await ServerImageHelpers.compressImage(inputBuffer, {
          quality: 70,
          format: 'jpeg',
          progressive: true,
          maxWidth: 1600,
          maxHeight: 1200
        });
      } catch (error) {
        console.error('Fallback JPEG compression failed:', error);
      }
    }

    if (!bestResult) {
      throw new Error('All compression attempts failed');
    }

    return bestResult;
  }

  /**
   * Batch process multiple images
   */
  static async processMultipleImages(
    inputs: (Buffer | string)[],
    options: Partial<CompressionOptions> = {}
  ): Promise<ProcessedImage[]> {
    const results: ProcessedImage[] = [];
    
    for (let i = 0; i < inputs.length; i++) {
      try {
        const result = await this.processImage(inputs[i], options);
        results.push(result);
        
        console.log(`✅ Image ${i + 1}/${inputs.length} processed: ${ImageHelpers.formatFileSize(result.original.size)} → ${ImageHelpers.formatFileSize(result.compressed.size)} (${result.metadata.compressionRatio.toFixed(1)}% reduction)`);
        
      } catch (error) {
        console.error(`❌ Failed to process image ${i + 1}:`, error);
        throw error;
      }
    }

    return results;
  }

  /**
   * Get compression statistics
   */
  static getCompressionStats(results: ProcessedImage[]): {
    totalOriginalSize: number;
    totalCompressedSize: number;
    totalSizeSaved: number;
    averageCompressionRatio: number;
    totalProcessingTime: number;
  } {
    const stats = results.reduce((acc, result) => ({
      totalOriginalSize: acc.totalOriginalSize + result.original.size,
      totalCompressedSize: acc.totalCompressedSize + result.compressed.size,
      totalSizeSaved: acc.totalSizeSaved + result.metadata.sizeSaved,
      totalProcessingTime: acc.totalProcessingTime + result.metadata.processingTime,
      totalCompressionRatio: acc.totalCompressionRatio + result.metadata.compressionRatio
    }), {
      totalOriginalSize: 0,
      totalCompressedSize: 0,
      totalSizeSaved: 0,
      totalProcessingTime: 0,
      totalCompressionRatio: 0
    });

    return {
      ...stats,
      averageCompressionRatio: stats.totalCompressionRatio / results.length
    };
  }

  /**
   * Validate and prepare image for compression
   */
  static validateImageInput(input: Buffer | string): { isValid: boolean; error?: string } {
    try {
      let size: number;
      
      if (typeof input === 'string') {
        // Base64 string
        if (!input.startsWith('data:image/')) {
          return { isValid: false, error: 'Invalid base64 image format' };
        }
        const buffer = ImageHelpers.base64ToBuffer(input);
        size = buffer.length;
      } else {
        // Buffer
        size = input.length;
      }

      if (size === 0) {
        return { isValid: false, error: 'Empty image data' };
      }

      if (size > this.MAX_ORIGINAL_SIZE) {
        return { 
          isValid: false, 
          error: `File too large. Maximum size is ${ImageHelpers.formatFileSize(this.MAX_ORIGINAL_SIZE)}` 
        };
      }

      return { isValid: true };

    } catch (error) {
      return { 
        isValid: false, 
        error: `Invalid image data: ${error instanceof Error ? error.message : 'Unknown error'}` 
      };
    }
  }
}
