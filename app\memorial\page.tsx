"use client";
import { useState, useEffect, useCallback } from "react";
import DeceasedList from "@/components/memorial/DeceasedList";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Search, Filter, Loader2, ChevronDown } from "lucide-react";
import { Deceased } from "@/data/deceased";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/errorHandler";
import { MemorialApiResponse, PaginationInfo, PaginatedApiResponse } from "@/types";

// Type alias for memorial pagination response
type MemorialPaginatedResponse = PaginatedApiResponse<MemorialApiResponse>;

// Convert API response to Deceased format
const convertToDeceased = (apiData: MemorialApiResponse): Deceased => {
  return {
    id: apiData.id,
    name: apiData.name,
    birthPlace: apiData.birthPlace,
    birthYear: apiData.birthYear,
    deathYear: apiData.deathYear,
    birthDate: apiData.birthDate,
    deathDate: apiData.deathDate,
    cemeteryName: apiData.cemeteryName,
    submittedBy: apiData.submittedBy,
    description: apiData.description,
    images: apiData.images.map(img => img.url), // Convert to string array
    location: apiData.location, // Include location data with place_id
    // Package information
    packageId: apiData.packageId,
    packageName: apiData.packageName,
    packagePrice: apiData.packagePrice,
    packageDuration: apiData.packageDuration,
    packageFeatures: apiData.packageFeatures ? JSON.parse(apiData.packageFeatures) : undefined,
    paymentStatus: apiData.paymentStatus,
  };
};

export default function MemorialPage() {
  const [memorials, setMemorials] = useState<Deceased[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredMemorials, setFilteredMemorials] = useState<Deceased[]>([]);
  const [pagination, setPagination] = useState<PaginationInfo | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasSearched, setHasSearched] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState<string>("all");

  // Fetch memorials from database with pagination
  const fetchMemorials = useCallback(async (page: number = 1, append: boolean = false) => {
    try {
      if (append) {
        setLoadingMore(true);
      } else {
        setLoading(true);
        setMemorials([]);
      }

      const response = await fetch(`/api/memorials?page=${page}&limit=10`);

      if (!response.ok) {
        throw new Error('Failed to fetch memorials');
      }

      const data: MemorialPaginatedResponse = await response.json();

      if (data.success) {
        // Convert API data to Deceased format
        const convertedData = data.data.map((item: MemorialApiResponse) => convertToDeceased(item));

        if (append) {
          // Append to existing data for infinite scroll
          setMemorials(prev => [...prev, ...convertedData]);
        } else {
          // Replace data for initial load or refresh
          setMemorials(convertedData);
        }

        setPagination(data.pagination);
        setCurrentPage(page);
      } else {
        throw new Error(data.message || 'Failed to load memorials');
      }
    } catch (error) {
      console.error('Error fetching memorials:', error);
      ErrorHandler.handle('Gagal memuat data memorial', 'Memorial Page');
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, []);

  // Load more memorials (infinite scroll)
  const loadMoreMemorials = useCallback(() => {
    if (pagination && pagination.hasNextPage && !loadingMore) {
      fetchMemorials(currentPage + 1, true);
    }
  }, [pagination, currentPage, loadingMore, fetchMemorials]);

  // Initial load
  useEffect(() => {
    fetchMemorials(1, false);
  }, [fetchMemorials]);

  // Filter memorials based on search term and package
  useEffect(() => {
    let filtered = memorials;

    // Apply search filter
    if (searchTerm.trim()) {
      filtered = filtered.filter(memorial =>
        memorial.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        memorial.birthPlace?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        memorial.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setHasSearched(true);
    } else {
      setHasSearched(false);
    }

    // Apply package filter
    if (selectedPackage !== "all") {
      filtered = filtered.filter(memorial => memorial.packageId === selectedPackage);
    }

    setFilteredMemorials(filtered);
  }, [searchTerm, selectedPackage, memorials]);

  return (
    <main className="min-h-screen pt-32 md:pt-56 pb-16 px-4 md:px-8 bg-memorial-950 text-memorial-50">
      <div className="container mx-auto">
        <div className="flex flex-col items-center mb-8">
          <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold">Memorial Digital</h1>
          
          {/* Pagination Info */}
          {pagination && !hasSearched && (
            <div className="mt-2 text-memorial-400 text-sm">
              Menampilkan {memorials.length} dari {pagination.totalItems} memorial
              {pagination.totalPages > 1 && (
                <span className="ml-2">
                  (Halaman {pagination.currentPage} dari {pagination.totalPages})
                </span>
              )}
            </div>
          )}

          {/* Search Results Info */}
          {(hasSearched || selectedPackage !== "all") && (
            <div className="mt-2 text-memorial-400 text-sm">
              Ditemukan {filteredMemorials.length} memorial
              {hasSearched && ` untuk "${searchTerm}"`}
              {selectedPackage !== "all" && ` dengan ${
                selectedPackage === "free" ? "Paket Free" :
                selectedPackage === "standard" ? "Paket Standar" :
                selectedPackage === "premium" ? "Paket Premium" : selectedPackage
              }`}
            </div>
          )}
        </div>
        <div className="md:ml-auto flex flex-col md:flex-row gap-4 w-full md:w-auto mb-4">
          {/* Search Input */}
          <div className="w-full md:w-64 relative">
            <input
              type="text"
              placeholder="Cari nama, tempat lahir..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-memorial-800 border border-memorial-700 rounded-md text-memorial-50 placeholder-memorial-400 focus:outline-none focus:ring-2 focus:ring-memorial-600"
            />
            <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-memorial-400" />
          </div>

          {/* Package Filter */}
          <div className="w-full md:w-48 relative">
            <select
              value={selectedPackage}
              onChange={(e) => setSelectedPackage(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-memorial-800 border border-memorial-700 rounded-md text-memorial-50 focus:outline-none focus:ring-2 focus:ring-memorial-600 appearance-none"
            >
              <option value="all">Semua Paket</option>
              <option value="free">Paket Free</option>
              <option value="standard">Paket Standar</option>
              <option value="premium">Paket Premium</option>
            </select>
            <Filter className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-memorial-400" />
            <ChevronDown className="h-4 w-4 absolute right-3 top-1/2 transform -translate-y-1/2 text-memorial-400 pointer-events-none" />
          </div>
        </div>
        {loading ? (
          <div className="flex justify-center items-center py-16">
            <Loader2 className="h-8 w-8 animate-spin text-memorial-400" />
            <span className="ml-2 text-memorial-400">Memuat data memorial...</span>
          </div>
        ) : (
          <>
            {/* Memorial List */}
            <DeceasedList
              deceasedList={filteredMemorials}
              showStats={!hasSearched && selectedPackage === "all"}
            />

            {/* Load More Button - Only show if not searching and has more pages */}
            {!hasSearched && pagination && pagination.hasNextPage && (
              <div className="flex justify-center mt-8">
                <Button
                  onClick={loadMoreMemorials}
                  disabled={loadingMore}
                  variant="outline"
                  className="border-memorial-600 text-memorial-300 hover:bg-memorial-800 hover:text-memorial-50"
                >
                  {loadingMore ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Memuat...
                    </>
                  ) : (
                    <>
                      <ChevronDown className="h-4 w-4 mr-2" />
                      Muat Lebih Banyak ({pagination.totalItems - memorials.length} tersisa)
                    </>
                  )}
                </Button>
              </div>
            )}

            {/* No More Data Message */}
            {!hasSearched && pagination && !pagination.hasNextPage && memorials.length > 0 && (
              <div className="text-center mt-8 text-memorial-400 text-sm">
                Semua memorial telah dimuat ({pagination.totalItems} total)
              </div>
            )}

            {/* No Results Message */}
            {filteredMemorials.length === 0 && !loading && (
              <div className="text-center py-16">
                <div className="text-memorial-400 text-lg mb-2">
                  {hasSearched ? 'Tidak ada memorial yang ditemukan' : 'Belum ada memorial'}
                </div>
                <div className="text-memorial-500 text-sm">
                  {hasSearched
                    ? 'Coba gunakan kata kunci yang berbeda'
                    : 'Memorial akan muncul di sini setelah ada yang menambahkan'
                  }
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </main>
  );
}