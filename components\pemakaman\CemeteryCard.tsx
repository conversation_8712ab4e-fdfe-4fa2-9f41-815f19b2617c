import { Button } from "@/components/ui/button";
import Image from "next/image";

interface Cemetery {
  id: number;
  name: string;
  location: string;
  religion: string;
  image: string;
  availableSpots: number;
}

interface CemeteryCardProps {
  cemetery: Cemetery;
}

export default function CemeteryCard({ cemetery }: CemeteryCardProps) {
  return (
    <div className="bg-memorial-900 border border-memorial-800 rounded-lg overflow-hidden h-full">
      <div className="relative aspect-square w-full">
        <Image 
          src={cemetery.image} 
          alt={cemetery.name}
          fill
          className="object-cover"
        />
      </div>
      <div className="p-4">
        <h3 className="text-xl font-semibold text-memorial-50">{cemetery.name}</h3>
        <p className="text-memorial-300 mb-2">{cemetery.location}</p>
        <div className="flex justify-between items-center">
          <span className="text-sm text-memorial-400">
            {cemetery.religion}
          </span>
          <span className="text-sm text-candle-500">
            {cemetery.availableSpots} slot tersedia
          </span>
        </div>
        <Button 
          className="w-full mt-4 bg-candle-500 text-memorial-950 hover:bg-candle-400"
          size="sm"
        >
          Lihat Detail
        </Button>
      </div>
    </div>
  );
}