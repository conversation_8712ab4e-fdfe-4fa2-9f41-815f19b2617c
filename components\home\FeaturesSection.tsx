import { Search, Database, Clock, Users, Shield, Map } from "lucide-react";

interface Feature {
  icon: React.ReactNode;
  title: string;
  description: string;
}

export default function FeaturesSection() {
  const features: Feature[] = [
    {
      icon: <Search className="h-8 w-8 text-candle-500" />,
      title: "Pencarian Mudah",
      description: "Temukan lokasi pemakaman berdasarkan nama, lokasi, atau agama dengan cepat dan akurat."
    },
    // {
    //   icon: <Database className="h-8 w-8 text-candle-500" />,
    //   title: "Informasi Lengkap",
    //   description: "Dapatkan detail tentang ketersediaan lahan, biaya, dan fasilitas di setiap lokasi pemakaman."
    // },
    // {
    //   icon: <Clock className="h-8 w-8 text-candle-500" />,
    //   title: "Reservasi Online",
    //   description: "Pesan lahan pemakaman secara online dengan proses yang cepat dan transparan."
    // },
    {
      icon: <Users className="h-8 w-8 text-candle-500" />,
      title: "Memorial Digital",
      description: "Abadikan kenangan orang tercinta dengan memorial digital yang dapat diakses kapan saja."
    },
    {
      icon: <Shield className="h-8 w-8 text-candle-500" />,
      title: "Keamanan Data",
      description: "Data pemakaman dan informasi pribadi dilindungi dengan sistem keamanan tingkat tinggi."
    },
    // {
    //   icon: <Map className="h-8 w-8 text-candle-500" />,
    //   title: "Pemetaan Digital",
    //   description: "Visualisasi lokasi pemakaman dengan peta digital yang interaktif dan mudah digunakan."
    // }
  ];

  return (
    <section className="py-12">
      <h2 className="text-2xl md:text-3xl font-bold mb-8 text-center">Fitur Utama</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {features.map((feature, index) => (
          <div key={index} className="bg-memorial-900 p-6 rounded-lg border border-memorial-800 hover:border-candle-500 transition-all hover:scale-105 hover:-translate-x-4 hover:-translate-y-2 duration-300 origin-top-left">
            <div className="flex justify-center mb-4">
              {feature.icon}
            </div>
            <h3 className="text-xl font-semibold mb-3 text-center text-memorial-50">{feature.title}</h3>
            <p className="text-memorial-300 text-center">{feature.description}</p>
          </div>
        ))}
      </div>
    </section>
  );
}