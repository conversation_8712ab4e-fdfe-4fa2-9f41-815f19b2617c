import { Select } from "@/components/ui/select";
import { Label } from "@radix-ui/react-label";

interface Religion {
  id: string;
  slug: string;
  name: string;
}

interface ReligionSelectorProps {
  religions: Religion[];
  selectedReligionId: string | null;
  onReligionChange: (value: string) => void;
  loading?: boolean;
}

export default function ReligionSelector({
  religions,
  selectedReligionId,
  onReligionChange,
  loading = false
}: ReligionSelectorProps) {
  const religionOptions = religions.map(religion => ({
    value: religion.id,
    label: religion.name
  }));

  return (
    <div>
      <Label htmlFor="religionId" className="text-sm sm:text-base text-memorial-200 mb-1 block">
        Agama
      </Label>
      <Select
        options={religionOptions}
        value={selectedReligionId ? selectedReligionId.toString() : ''}
        onChange={onReligionChange}
        placeholder={loading ? "Memuat agama..." : "Pilih Agama"}
        className="bg-memorial-800 border-memorial-700 text-memorial-50 text-sm sm:text-base"
        disabled={loading}
      />
    </div>
  );
}
