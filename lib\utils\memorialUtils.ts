import { Deceased } from "@/data/deceased";

/**
 * Format date to Indonesian format (DD MMMM YYYY)
 * @param dateString - ISO date string or date string
 * @returns Formatted date in Indonesian locale
 */
export const formatDate = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  } catch (error) {
    return dateString; // Fallback to original string if parsing fails
  }
};

/**
 * Generate Google Maps URL using place_id if available
 * @param deceased - Deceased person data
 * @returns Google Maps URL or null if no location data
 */
export const getLocationUrl = (deceased: Deceased): string | null => {
  // Check if deceased has location data with place_id
  if (deceased.location?.place_id) {
    return `https://www.google.com/maps/place/?q=place_id:${deceased.location.place_id}`;
  }

  // Fallback to search by cemetery name
  if (deceased.cemeteryName && deceased.cemeteryName !== 'Lokasi tidak diketahui') {
    const encodedName = encodeURIComponent(deceased.cemeteryName);
    return `https://www.google.com/maps/search/${encodedName}`;
  }

  return null;
};

/**
 * Check if device is mobile based on window width
 * @param width - Window width (default: window.innerWidth)
 * @returns Boolean indicating if device is mobile
 */
export const isMobileDevice = (width?: number): boolean => {
  const windowWidth = width ?? (typeof window !== 'undefined' ? window.innerWidth : 768);
  return windowWidth < 768;
};
