"use client";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { useReligionStore } from "@/store/useReligionStore";
import PageWithoutFooter from "@/components/layouts/PageWithoutFooter";
import FeaturesSection from "@/components/home/<USER>";
import HeroSection from "@/components/home/<USER>";
import Testimonials from "@/components/home/<USER>";
import CallToAction from "@/components/home/<USER>";
import MemorialStack from "@/components/home/<USER>";

export default function LandingPage() {
  const { selectedReligion } = useReligionStore();
  const router = useRouter();

  return (
    <PageWithoutFooter>
      <main className="min-h-screen pt-32 md:pt-56 pb-16 px-4 md:px-8 bg-memorial-950 text-memorial-50">
        <div className="container mx-auto">
          {/* Hero Section */}
          <HeroSection />
          
          {/* Divider */}
          <div className="h-px bg-memorial-800 my-16"></div>
          
          {/* Memorial Stack with Description */}
          <MemorialStack />
          
          {/* Divider */}
          <div className="h-px bg-memorial-800 my-16"></div>
          
          {/* Features Section */}
          <FeaturesSection />
          
          {/* Divider */}
          <div className="h-px bg-memorial-800 my-16"></div>
          
          {/* Testimonials */}
          <Testimonials />
          
          {/* Divider */}
          <div className="h-px bg-memorial-800 my-16"></div>
          
          {/* Call to Action */}
          <CallToAction />
        </div>
      </main>
    </PageWithoutFooter>
  );
}
