import { Label } from "@radix-ui/react-label";
import { Upload } from "lucide-react";
import { ImageHelpers } from "@/lib/utils/imageHelpers";

interface ImageUploadSectionProps {
  images: File[];
  maxImages: number;
  onImageUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onRemoveImage: (index: number) => void;
}

export default function ImageUploadSection({
  images,
  maxImages,
  onImageUpload,
  onRemoveImage
}: ImageUploadSectionProps) {
  return (
    <div>
      <Label className="text-sm sm:text-base text-memorial-200 block mb-1 sm:mb-2">
        Foto (Maksimal {maxImages} foto{maxImages > 1 ? '' : ''})
      </Label>
      <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
        {/* Image preview area */}
        <div className="flex gap-3 sm:gap-4 flex-wrap">
          {images.map((image, index) => (
            <div key={index} className="relative w-24 h-32 sm:w-32 sm:h-40 bg-memorial-800 rounded-md overflow-hidden">
              <img
                src={ImageHelpers.createPreviewUrl(image)}
                alt={`Preview ${index + 1}`}
                className="w-full h-full object-cover"
              />
              <button
                type="button"
                onClick={() => onRemoveImage(index)}
                className="absolute top-1 right-1 bg-memorial-950/80 text-memorial-50 rounded-full p-1 hover:bg-memorial-950 transition-colors"
                aria-label={`Remove image ${index + 1}`}
              >
                ✕
              </button>
            </div>
          ))}

          {images.length < maxImages && (
            <label className="flex items-center justify-center w-24 h-32 sm:w-32 sm:h-40 bg-memorial-800 border border-dashed border-memorial-600 rounded-md cursor-pointer hover:bg-memorial-700/50 transition-colors">
              <div className="flex flex-col items-center">
                <Upload className="h-5 w-5 sm:h-6 sm:w-6 text-memorial-400 mb-1 sm:mb-2" />
                <span className="text-xs sm:text-sm text-memorial-400">Upload Foto</span>
              </div>
              <input
                type="file"
                accept={ImageHelpers.getAcceptedFormats()}
                className="hidden"
                onChange={onImageUpload}
                multiple={maxImages > 1}
              />
            </label>
          )}
        </div>
      </div>
      <p className="text-xs sm:text-sm text-memorial-400 mt-1 sm:mt-2">
        Format: {ImageHelpers.getAcceptedExtensions()}. Ukuran maksimal: {ImageHelpers.formatFileSize(ImageHelpers.getMaxFileSize())} per foto.
      </p>
    </div>
  );
}
