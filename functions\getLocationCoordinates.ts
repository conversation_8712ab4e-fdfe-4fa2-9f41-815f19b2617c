import { ApiError } from '@/utils/ApiError';

/**
 * Function to get coordinates for an Indonesian administrative area
 * Uses Google Maps Geocoding API
 * 
 * @param location - Object containing Indonesian administrative divisions
 * @returns Response object with location data or error information
 */
export async function getLocationCoordinates({
  provinsi,
  kabupaten_kota,
  kecamatan,
  kelurahan,
  alamat_detail
}: {
  provinsi: string;
  kabupaten_kota: string;
  kecamatan: string;
  kelurahan: string;
  alamat_detail: string;
}) {
  try {
    const apiKey = validateApiKey();
    const searchQuery = buildSearchQuery(provinsi, kabupaten_kota, kecamatan, kelurahan, alamat_detail);
    const geocodingResult = await fetchGeocodingData(searchQuery, apiKey);
    
    return formatSuccessResponse(geocodingResult, provinsi, kabupaten_kota, kecamatan, kelurahan, alamat_detail);
  } catch (error) {
    return handleGeocodingError(error);
  }
}

/**
 * Validates that the Google Maps API key is configured
 * @throws ApiError if API key is missing
 */
function validateApiKey(): string {
  const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
  
  if (!apiKey) {
    throw new ApiError("Google Maps API key is not configured", 500, 'CONFIG_ERROR');
  }
  
  return apiKey;
}

/**
 * Builds a search query string from administrative divisions
 */
function buildSearchQuery(
  provinsi: string,
  kabupaten_kota: string,
  kecamatan: string,
  kelurahan: string,
  alamat_detail: string
): string {
  const searchQuery = `${alamat_detail}, ${kelurahan}, ${kecamatan}, ${kabupaten_kota}, ${provinsi}, Indonesia`;
  return encodeURIComponent(searchQuery);
}

/**
 * Fetches geocoding data from Google Maps API
 * @throws ApiError if the API request fails
 */
async function fetchGeocodingData(encodedQuery: string, apiKey: string) {
  const apiUrl = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodedQuery}&key=${apiKey}`;
  
  const response = await fetch(apiUrl);
  
  if (!response.ok) {
    throw new ApiError(`Google Maps API error: ${response.status}`, 502, 'EXTERNAL_API_ERROR');
  }
  
  const data = await response.json();
  
  if (data.status !== 'OK' || !data.results || data.results.length === 0) {
    throw new ApiError("Lokasi tidak ditemukan", 404, 'LOCATION_NOT_FOUND');
  }
  
  return data.results[0];
}

/**
 * Formats successful geocoding result into a standardized response
 */
function formatSuccessResponse(
  result: any,
  provinsi: string,
  kabupaten_kota: string,
  kecamatan: string,
  kelurahan: string,
  alamat_detail: string
) {
  const location = result.geometry.location;

  return {
    success: true,
    data: {
      provinsi,
      kabupaten_kota,
      kecamatan,
      kelurahan,
      nama_lengkap: `${alamat_detail}, ${kelurahan}, ${kecamatan}, ${kabupaten_kota}, ${provinsi}`,
      lokasi: {
        latitude: String(location.lat),  // Convert to string for consistency
        longitude: String(location.lng)  // Convert to string for consistency
      },
      formatted_address: result.formatted_address,
      place_id: result.place_id
    }
  };
}

/**
 * Handles errors that occur during geocoding
 */
function handleGeocodingError(error: unknown) {
  console.error("Error fetching location coordinates:", error);
  
  // Server-side logging
  if (typeof window === 'undefined') {
    const { handleApiError } = require('@/utils/errorHandler');
    handleApiError(error);
  }
  
  if (error instanceof ApiError) {
    return {
      success: false,
      message: error.message,
      code: error.code,
      data: null
    };
  }
  
  return {
    success: false,
    message: "Terjadi kesalahan saat mencari koordinat lokasi",
    code: 'GEOCODING_ERROR',
    data: null
  };
}

