import { NextRequest, NextResponse } from 'next/server';
import { handleApiError } from '@/utils/errorHandler';
import { validateLocationRequest } from '@/validators/locationValidator';
import { getCoordinatesForLocation } from '@/services/locationService';

/**
 * Handler untuk endpoint API lokasi
 * Mendapatkan koordinat untuk lokasi administratif di Indonesia
 */
export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();

    // Validasi request
    const locationData = validateLocationRequest(body);
    
    // Dapatkan koordinat lokasi
    const result = await getCoordinatesForLocation(locationData);
    
    // Return response sesuai hasil
    if (!result.success) {
      return NextResponse.json(result, { status: 404 });
    }
    
    return NextResponse.json(result);
    
  } catch (error) {
    // Gunakan handleApiError untuk penanganan error yang konsisten
    return handleApiError(error);
  }
}

