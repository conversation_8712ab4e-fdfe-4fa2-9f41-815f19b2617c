import {
  pgTable,
  uuid,
  text,
  timestamp,
  varchar,
  integer,
  date,
  index,
} from 'drizzle-orm/pg-core';
import { relations, sql } from 'drizzle-orm';

// Table: memorials (data almarhum)
export const memorials = pgTable('memorials', {
  id: uuid('id').primaryKey().default(sql`gen_random_uuid()`),
  slug: varchar('slug', { length: 255 }).notNull().unique(),
  name: varchar('name', { length: 255 }).notNull(),
  birthPlace: varchar('birth_place', { length: 255 }).notNull(),
  birthDate: date('birth_date').notNull(),
  deathDate: date('death_date').notNull(),
  religionId: uuid('religion_id').references(() => religions.id),
  locationId: uuid('location_id').references(() => locations.id),
  description: text('description'),
  submittedBy: varchar('submitted_by', { length: 255 }).notNull(),
  // <PERSON>bahan untuk bukti kematian
  evidenceName: varchar('evidence_name', { length: 255 }).notNull(),
  evidenceImageUrl: varchar('evidence_image_url', { length: 255 }),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
}, (table) => {
  return {
    slugIdx: index('memorial_slug_idx').on(table.slug),
    nameIdx: index('memorial_name_idx').on(table.name),
    religionIdx: index('memorial_religion_idx').on(table.religionId),
    locationIdx: index('memorial_location_idx').on(table.locationId),
    birthDateIdx: index('memorial_birth_date_idx').on(table.birthDate),
    deathDateIdx: index('memorial_death_date_idx').on(table.deathDate),
    // Add individual indexes for flexible search combinations
    nameAndBirthIdx: index('memorial_name_birth_idx').on(table.name, table.birthDate),
    nameAndDeathIdx: index('memorial_name_death_idx').on(table.name, table.deathDate),
    birthAndDeathIdx: index('memorial_birth_death_idx').on(table.birthDate, table.deathDate),
    allFieldsIdx: index('memorial_all_fields_idx').on(table.name, table.birthDate, table.deathDate),
  };
});

// Table: religions (data agama)
export const religions = pgTable('religions', {
  id: uuid('id').primaryKey().default(sql`gen_random_uuid()`),
  slug: varchar('slug', { length: 100 }).notNull().unique(),
  name: varchar('name', { length: 100 }).notNull().unique(),
});

// Table: memorial_images (galeri foto memorial)
export const memorialImages = pgTable('memorial_images', {
  id: uuid('id').primaryKey().default(sql`gen_random_uuid()`),
  memorialId: uuid('memorial_id')
    .references(() => memorials.id, { onDelete: 'cascade' }).notNull(),
  imageUrl: varchar('image_url', { length: 255 }).notNull(),
  caption: text('caption'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
}, (table) => {
  return {
    memorialIdx: index('memorial_images_memorial_idx').on(table.memorialId),
  };
});

// Table: locations (data lokasi pemakaman berdasarkan Google Maps)
export const locations = pgTable('locations', {
  id: uuid('id').primaryKey().default(sql`gen_random_uuid()`),
  slug: varchar('slug', { length: 255 }).notNull().unique(),
  name: varchar('name', { length: 255 }).notNull(),
  addressDetail: text('address_detail').notNull(),          // Alamat rinci (ex: Jl. Karet Pasar Baru Barat...)

  province: varchar('province', { length: 100 }).notNull(),     // Provinsi (ex: DKI Jakarta)
  city: varchar('city', { length: 100 }).notNull(),             // Kota/Kabupaten (ex: Jakarta Pusat)
  district: varchar('district', { length: 100 }).notNull(),     // Kecamatan (ex: Tanah Abang)
  subDistrict: varchar('sub_district', { length: 100 }).notNull(), // Kelurahan/Desa (ex: Karet Tengsin)

  latitude: varchar('latitude', { length: 50 }).notNull(),  // Koordinat dari Google Maps
  longitude: varchar('longitude', { length: 50 }).notNull(),
  placeId: varchar('place_id', { length: 255 }),            // Optional: Google Place ID

  createdAt: timestamp('created_at').defaultNow().notNull(),
}, (table) => {
  return {
    slugIdx: index('location_slug_idx').on(table.slug),
    nameIdx: index('location_name_idx').on(table.name),
    provinceIdx: index('location_province_idx').on(table.province),
    cityIdx: index('location_city_idx').on(table.city),
    districtIdx: index('location_district_idx').on(table.district),
  };
});

// Table: orders (data pemesanan memorial)
export const orders = pgTable('orders', {
  id: uuid('id').primaryKey().default(sql`gen_random_uuid()`),
  memorialId: uuid('memorial_id').references(() => memorials.id, { onDelete: 'cascade' }).notNull(),

  // Package information (stored directly in order)
  packageId: varchar('package_id', { length: 50 }).notNull(), // 'free', 'standard', 'premium'
  packageName: varchar('package_name', { length: 100 }).notNull(),
  packagePrice: integer('package_price').notNull().default(0),
  packageDuration: varchar('package_duration', { length: 50 }).notNull(),
  packageFeatures: text('package_features').notNull(), // JSON string array

  // Pricing
  adminFee: integer('admin_fee').notNull().default(0),
  totalPrice: integer('total_price').notNull().default(0),

  // Payment
  paymentMethod: varchar('payment_method', { length: 50 }), // 'bank', 'ewallet', null for free
  paymentStatus: varchar('payment_status', { length: 20 }).notNull().default('pending'), // 'pending', 'paid', 'failed'

  // Timestamps
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
}, (table) => {
  return {
    memorialIdx: index('order_memorial_idx').on(table.memorialId),
    packageIdx: index('order_package_idx').on(table.packageId),
    statusIdx: index('order_status_idx').on(table.paymentStatus),
    createdAtIdx: index('order_created_at_idx').on(table.createdAt),
  };
});

// Relations
export const memorialsRelations = relations(memorials, ({ one, many }) => ({
  religion: one(religions, {
    fields: [memorials.religionId],
    references: [religions.id],
  }),
  location: one(locations, {
    fields: [memorials.locationId],
    references: [locations.id],
  }),
  memorialImages: many(memorialImages),
  orders: many(orders),
}));

export const religionsRelations = relations(religions, ({ many }) => ({
  memorials: many(memorials),
}));

export const locationsRelations = relations(locations, ({ many }) => ({
  memorials: many(memorials),
}));

export const memorialImagesRelations = relations(memorialImages, ({ one }) => ({
  memorial: one(memorials, {
    fields: [memorialImages.memorialId],
    references: [memorials.id],
  }),
}));

export const ordersRelations = relations(orders, ({ one }) => ({
  memorial: one(memorials, {
    fields: [orders.memorialId],
    references: [memorials.id],
  }),
}));