import { But<PERSON> } from "@/components/ui/button";
import { MapPin, ShoppingBag } from "lucide-react";
import { useRouter } from "next/navigation";
import Image from "next/image";

export default function HeroSection() {
  const router = useRouter();
  
  return (
    <section className="pt-8 pb-16">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        <div>
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
            Pemakaman Digital <span className="text-candle-500">Indonesia</span>
          </h1>
          
          <p className="text-memorial-300 text-lg mb-8">
            Platform modern untuk mengelola dan mencari informasi pemakaman di Indonesia.
            Temukan lokasi pemakaman dengan mudah dan abadikan kenangan orang tercinta.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4">
            <Button 
              className="bg-candle-500 text-memorial-950 hover:bg-candle-400 px-6"
              onClick={() => router.push('/purchase')}
            >
              <ShoppingBag className="h-5 w-5 mr-2" />
              Beli Memorial
            </Button>
            
            <Button 
              className="bg-memorial-800 text-memorial-50 hover:bg-memorial-700 border border-memorial-700 px-6"
              variant="outline"
              onClick={() => router.push('/memorial')}
            >
              <MapPin className="h-5 w-5 mr-2" />
              Lihat Pemakaman
            </Button>
          </div>
        </div>
        
        <div className="relative h-[400px] rounded-lg overflow-hidden border-4 border-memorial-800">
          <Image 
            src="/images/hero-section-landing-page.webp" 
            alt="Pemakaman Digital"
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-memorial-950 to-transparent opacity-70"></div>
        </div>
      </div>
    </section>
  );
}