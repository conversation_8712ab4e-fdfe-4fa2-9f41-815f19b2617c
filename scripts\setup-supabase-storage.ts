#!/usr/bin/env tsx

/**
 * <PERSON>ript to setup Supabase Storage bucket and RLS policies
 * Run this script when setting up Supabase for the first time
 */

import { supabase, isUsingSupabase } from '../lib/supabase';
import { imageStorageService } from '../lib/services/imageStorageService';

async function setupSupabaseStorage() {
  console.log('🚀 Setting up Supabase Storage...\n');

  try {
    // Check if Supabase is configured
    if (!isUsingSupabase() || !supabase) {
      console.log('❌ Supabase not configured. Please set NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY');
      console.log('   For local development, this is optional - the app will use placeholder images.');
      return;
    }

    console.log('✅ Supabase client configured');

    // Test connection
    console.log('🔍 Testing Supabase connection...');
    const { data: testData, error: testError } = await supabase
      .from('religions')
      .select('count')
      .limit(1);

    if (testError) {
      console.error('❌ Supabase connection failed:', testError.message);
      console.log('   Please check your Supabase URL and API key');
      return;
    }

    console.log('✅ Supabase connection successful');

    // Setup bucket
    console.log('\n📦 Setting up storage bucket...');
    await imageStorageService.ensureBucketExists();

    // Setup policies
    console.log('\n🔐 Setting up RLS policies...');
    await imageStorageService.setupBucketPolicies();

    console.log('\n🎉 Supabase Storage setup completed!');
    console.log('\n📋 Manual steps required in Supabase Dashboard:');
    console.log('   1. Go to Storage > Policies');
    console.log('   2. Create policy for "memorial-images" bucket:');
    console.log('      - Policy name: "Public read access"');
    console.log('      - Operation: SELECT');
    console.log('      - Target roles: public');
    console.log('      - Policy definition: bucket_id = \'memorial-images\'');
    console.log('');
    console.log('   3. Create policy for uploads:');
    console.log('      - Policy name: "Public upload access"');
    console.log('      - Operation: INSERT');
    console.log('      - Target roles: public');
    console.log('      - Policy definition: bucket_id = \'memorial-images\'');
    console.log('');
    console.log('   4. Or disable RLS for the bucket if you want public access');
    console.log('');
    console.log('💡 Note: Until policies are set, the app will use placeholder images');

  } catch (error) {
    console.error('❌ Setup failed:', error);
    console.log('\n🔄 Fallback: The app will use placeholder images for development');
  }
}

// Run the setup
if (require.main === module) {
  setupSupabaseStorage()
    .then(() => {
      console.log('\n✨ Setup script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Setup script failed:', error);
      process.exit(1);
    });
}

export { setupSupabaseStorage };
