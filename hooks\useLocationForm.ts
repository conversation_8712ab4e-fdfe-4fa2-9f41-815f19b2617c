import { useState, useEffect } from "react";
import { LocationFormData, LocationSearchResult } from "@/types/location";

interface UseLocationFormProps {
  initialData?: LocationSearchResult;
}

export function useLocationForm({ initialData }: UseLocationFormProps) {
  const [formData, setFormData] = useState<LocationFormData>({
    provinsi: initialData?.provinsi_id || initialData?.provinsi || "",
    kabupaten_kota: initialData?.kabupaten_kota_id || initialData?.kabupaten_kota || "",
    kecamatan: initialData?.kecamatan_id || initialData?.kecamatan || "",
    kelurahan: initialData?.kelurahan_id || initialData?.kelurahan || "",
    alamat_detail: initialData?.alamat_detail || ""
  });

  // Reset form when initialData changes (e.g., when store is reset)
  useEffect(() => {
    setFormData({
      provinsi: initialData?.provinsi_id || initialData?.provinsi || "",
      kabupaten_kota: initialData?.kabupaten_kota_id || initialData?.kabupaten_kota || "",
      kecamatan: initialData?.kecamatan_id || initialData?.kecamatan || "",
      kelurahan: initialData?.kelurahan_id || initialData?.kelurahan || "",
      alamat_detail: initialData?.alamat_detail || ""
    });
  }, [initialData, JSON.stringify(initialData)]);

  const handleChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value,
      // Reset dependent fields
      ...(name === 'provinsi' && { kabupaten_kota: '', kecamatan: '', kelurahan: '' }),
      ...(name === 'kabupaten_kota' && { kecamatan: '', kelurahan: '' }),
      ...(name === 'kecamatan' && { kelurahan: '' }),
    }));
  };

  const resetForm = () => {
    setFormData({
      provinsi: "",
      kabupaten_kota: "",
      kecamatan: "",
      kelurahan: "",
      alamat_detail: ""
    });
  };

  return {
    formData,
    handleChange,
    resetForm,
    setFormData
  };
}
