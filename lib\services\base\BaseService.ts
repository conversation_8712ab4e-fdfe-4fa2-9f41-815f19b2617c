// Base Service Class for consistent service patterns
import { ApiError } from '@/utils/ApiError';

export interface ServiceOptions {
  enableCache?: boolean;
  cacheTimeout?: number;
  enableLogging?: boolean;
}

export interface PaginationOptions {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface ServiceResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ServiceResponse<T[]> {
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
    nextPage: number | null;
    prevPage: number | null;
  };
}

export abstract class BaseService {
  protected serviceName: string;
  protected options: ServiceOptions;
  protected cache: Map<string, { data: any; timestamp: number }>;

  constructor(serviceName: string, options: ServiceOptions = {}) {
    this.serviceName = serviceName;
    this.options = {
      enableCache: false,
      cacheTimeout: 5 * 60 * 1000, // 5 minutes
      enableLogging: true,
      ...options,
    };
    this.cache = new Map();
  }

  // Logging helper
  protected log(message: string, data?: any): void {
    if (this.options.enableLogging) {
      console.log(`[${this.serviceName}] ${message}`, data || '');
    }
  }

  // Error handling helper
  protected handleError(error: any, operation: string): never {
    return handleServiceError(error, operation, this.serviceName);
  }

  // Cache helpers
  protected getCacheKey(key: string): string {
    return `${this.serviceName}:${key}`;
  }

  protected getFromCache<T>(key: string): T | null {
    if (!this.options.enableCache) return null;

    const cacheKey = this.getCacheKey(key);
    const cached = this.cache.get(cacheKey);

    if (!cached) return null;

    const isExpired = Date.now() - cached.timestamp > this.options.cacheTimeout!;
    if (isExpired) {
      this.cache.delete(cacheKey);
      return null;
    }

    this.log(`Cache hit for key: ${key}`);
    return cached.data;
  }

  protected setCache<T>(key: string, data: T): void {
    if (!this.options.enableCache) return;

    const cacheKey = this.getCacheKey(key);
    this.cache.set(cacheKey, {
      data,
      timestamp: Date.now(),
    });

    this.log(`Cache set for key: ${key}`);
  }

  protected clearCache(key?: string): void {
    if (key) {
      const cacheKey = this.getCacheKey(key);
      this.cache.delete(cacheKey);
      this.log(`Cache cleared for key: ${key}`);
    } else {
      this.cache.clear();
      this.log('All cache cleared');
    }
  }

  // Validation helpers
  protected validateId(id: string, fieldName: string = 'id'): void {
    if (!id || typeof id !== 'string') {
      throw new ApiError(`${fieldName} is required and must be a string`, 400, 'VALIDATION_ERROR');
    }

    // UUID validation
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(id)) {
      throw new ApiError(`${fieldName} must be a valid UUID`, 400, 'VALIDATION_ERROR');
    }
  }

  protected validatePagination(options: PaginationOptions): void {
    if (options.page < 1) {
      throw new ApiError('Page must be >= 1', 400, 'VALIDATION_ERROR');
    }

    if (options.limit < 1 || options.limit > 100) {
      throw new ApiError('Limit must be between 1 and 100', 400, 'VALIDATION_ERROR');
    }

    if (options.sortOrder && !['asc', 'desc'].includes(options.sortOrder)) {
      throw new ApiError('Sort order must be "asc" or "desc"', 400, 'VALIDATION_ERROR');
    }
  }

  // Async operation wrapper
  protected async executeOperation<T>(
    operation: () => Promise<T>,
    operationName: string,
    cacheKey?: string
  ): Promise<T> {
    try {
      // Check cache first
      if (cacheKey) {
        const cached = this.getFromCache<T>(cacheKey);
        if (cached) return cached;
      }

      this.log(`Executing ${operationName}`);
      const result = await operation();

      // Cache result if cache key provided
      if (cacheKey) {
        this.setCache(cacheKey, result);
      }

      this.log(`Successfully completed ${operationName}`);
      return result;

    } catch (error) {
      this.handleError(error, operationName);
    }
  }

  // Standard response formatters
  protected createSuccessResponse<T>(data: T, message?: string): ServiceResponse<T> {
    return {
      success: true,
      data,
      message: message || 'Operation completed successfully',
    };
  }

  protected createErrorResponse(error: string, message?: string): ServiceResponse<never> {
    return {
      success: false,
      error,
      message: message || 'Operation failed',
    };
  }

  protected createPaginatedResponse<T>(
    data: T[],
    pagination: PaginatedResponse<T>['pagination'],
    message?: string
  ): PaginatedResponse<T> {
    return {
      success: true,
      data,
      pagination,
      message: message || 'Data retrieved successfully',
    };
  }

  // Abstract methods that services should implement
  abstract getById(id: string): Promise<any>;
  abstract getAll(options?: any): Promise<any>;
}

// Service factory for creating standardized services
export class ServiceFactory {
  private static services: Map<string, BaseService> = new Map();

  static registerService<T extends BaseService>(name: string, service: T): T {
    this.services.set(name, service);
    return service;
  }

  static getService<T extends BaseService>(name: string): T | null {
    return this.services.get(name) as T || null;
  }

  static getAllServices(): Map<string, BaseService> {
    return new Map(this.services);
  }
}

// Helper function for error handling
export function handleServiceError(error: any, operation: string, serviceName: string): never {
  console.error(`[${serviceName}] Error in ${operation}:`, error);

  if (error instanceof ApiError) {
    throw error;
  }

  throw new ApiError(
    `Failed to ${operation}`,
    500,
    `${serviceName.toUpperCase()}_${operation.toUpperCase()}_ERROR`
  );
}
