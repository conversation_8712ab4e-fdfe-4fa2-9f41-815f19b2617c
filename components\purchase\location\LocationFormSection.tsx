import { Select } from "@/components/ui/select";
import { LocationFormData } from "@/types/location";
import { useProvinces, useRegencies, useDistricts, useVillages } from "@/hooks/useRegions";

interface LocationFormSectionProps {
  formData: LocationFormData;
  onChange: (name: string, value: string) => void;
  loading?: boolean;
}

export default function LocationFormSection({ 
  formData, 
  onChange, 
  loading = false 
}: LocationFormSectionProps) {
  // Use region hooks
  const { provinces } = useProvinces();
  const { regencies } = useRegencies(formData.provinsi);
  const { districts } = useDistricts(formData.kabupaten_kota);
  const { villages } = useVillages(formData.kecamatan);

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm text-memorial-300 mb-1">Provinsi</label>
        <Select
          name="provinsi"
          value={formData.provinsi}
          onChange={(value) => onChange('provinsi', value)}
          options={provinces.map(p => ({ value: p.id, label: p.name }))}
          placeholder="Pilih Provinsi"
          className="bg-memorial-800 border-memorial-700 text-memorial-50"
          disabled={loading}
        />
      </div>
      
      <div>
        <label className="block text-sm text-memorial-300 mb-1">Kabupaten/Kota</label>
        <Select
          name="kabupaten_kota"
          value={formData.kabupaten_kota}
          onChange={(value) => onChange('kabupaten_kota', value)}
          options={regencies.map(r => ({ value: r.id, label: r.name }))}
          placeholder="Pilih Kabupaten/Kota"
          className="bg-memorial-800 border-memorial-700 text-memorial-50"
          disabled={!formData.provinsi || loading}
        />
      </div>
      
      <div>
        <label className="block text-sm text-memorial-300 mb-1">Kecamatan</label>
        <Select
          name="kecamatan"
          value={formData.kecamatan}
          onChange={(value) => onChange('kecamatan', value)}
          options={districts.map(d => ({ value: d.id, label: d.name }))}
          placeholder="Pilih Kecamatan"
          className="bg-memorial-800 border-memorial-700 text-memorial-50"
          disabled={!formData.kabupaten_kota || loading}
        />
      </div>
      
      <div>
        <label className="block text-sm text-memorial-300 mb-1">Kelurahan</label>
        <Select
          name="kelurahan"
          value={formData.kelurahan}
          onChange={(value) => onChange('kelurahan', value)}
          options={villages.map(v => ({ value: v.id, label: v.name }))}
          placeholder="Pilih Kelurahan"
          className="bg-memorial-800 border-memorial-700 text-memorial-50"
          disabled={!formData.kecamatan || loading}
        />
      </div>
      
      <div>
        <label className="block text-sm text-memorial-300 mb-1">Alamat Detail</label>
        <input
          type="text"
          name="alamat_detail"
          value={formData.alamat_detail}
          onChange={(e) => onChange('alamat_detail', e.target.value)}
          placeholder="contoh: Jl. KH Achmad Dahlan No. 123"
          className="w-full p-2 rounded bg-memorial-800 border border-memorial-700 text-memorial-50 placeholder:text-memorial-500"
          disabled={loading}
        />
      </div>
    </div>
  );
}
