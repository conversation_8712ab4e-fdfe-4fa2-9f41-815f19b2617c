# Production Environment Setup

Panduan untuk menjalankan aplikasi dalam mode production dengan konfigurasi Supabase Cloud.

## 🚀 Quick Start

### 1. Build Application
```bash
npm run build
```

### 2. Start Production Server
```bash
npm run start
```

Server akan be<PERSON><PERSON><PERSON> di `http://localhost:3000` dengan konfigurasi production.

## 🔧 Environment Configuration

### Development vs Production

| Environment | Database | Storage | Config File |
|-------------|----------|---------|-------------|
| **Development** | Local PostgreSQL | Placeholder Images | `.env.local` |
| **Production** | Supabase Cloud | Supabase Storage | `.env.production` |

### Environment Variables

#### Required for Production (.env.production):
```bash
# Production Environment
NODE_ENV=production

# Database (Supabase Cloud)
DATABASE_URL=postgresql://postgres:[PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres

# Google Maps API
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_production_api_key

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://[PROJECT-REF].supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
```

## 🛠️ Setup Commands

### Check Environment Configuration
```bash
# Check development environment
npm run check:env

# Check production environment
npm run check:env:prod

# Test database connection
npm run test:db:prod

# Check environment loading for start command
npm run start:check
```

### Setup Supabase Storage
```bash
npm run setup:supabase
```

### Database Operations
```bash
# Generate migrations
npm run db:generate

# Run migrations
npm run db:migrate

# Reset and fresh migrate
npm run db:fresh

# Open Drizzle Studio
npm run db:studio
```

## 📦 Production Deployment

### 1. Supabase Cloud Setup

1. **Create Supabase Project**
   - Go to [supabase.com](https://supabase.com)
   - Create new project
   - Note down Project URL and API Keys

2. **Configure Database**
   - Copy connection string from Settings > Database
   - Update `DATABASE_URL` in `.env.production`

3. **Setup Storage**
   - Run `npm run setup:supabase`
   - Follow instructions to configure RLS policies

### 2. Environment Variables

Update `.env.production` with your Supabase credentials:

```bash
# Get from Supabase Dashboard > Settings > API
NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# Get from Supabase Dashboard > Settings > Database  
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres
```

### 3. Build and Deploy

```bash
# Build for production
npm run build

# Start production server
npm run start
```

## 🔍 Troubleshooting

### Common Issues

#### 1. Supabase RLS Error
```
Error: new row violates row-level security policy
```

**Solution:**
- Configure RLS policies in Supabase Dashboard
- Or disable RLS for public access
- App will fallback to placeholder images if storage fails

#### 2. Database Connection Error
```
Error: Connection refused
```

**Solution:**
- Check `DATABASE_URL` in `.env.production`
- Verify Supabase project is active
- Check network connectivity

#### 3. Environment Variables Not Loaded
```
Error: Environment variables undefined
```

**Solution:**
- Ensure `.env.production` exists
- Check file permissions
- Run `npm run check:env:prod` to verify

### Debug Commands

```bash
# Check environment configuration
npm run check:env:prod

# Test database connection
npm run test:db

# Setup Supabase storage
npm run setup:supabase
```

## 🎯 Production Features

### Enabled in Production:
- ✅ Supabase Cloud Database
- ✅ Supabase Storage for images
- ✅ Google Maps integration
- ✅ Full memorial creation flow
- ✅ Image upload and storage
- ✅ Location search and mapping

### Fallback Behavior:
- 🔄 Storage errors → Placeholder images
- 🔄 Database errors → Graceful error handling
- 🔄 API errors → User-friendly messages

## 📊 Monitoring

### Health Checks:
- Database connectivity
- Supabase storage availability
- Google Maps API quota
- Environment variable validation

### Logs:
- Application logs in console
- Supabase logs in dashboard
- Error tracking and reporting

## 🔐 Security

### Production Security:
- Environment variables properly configured
- Supabase RLS policies enabled
- API keys secured
- Database access controlled

### Best Practices:
- Regular security updates
- Monitor API usage
- Backup database regularly
- Review access logs

---

## 📞 Support

For issues with production deployment:
1. Check troubleshooting section above
2. Run debug commands
3. Review Supabase dashboard logs
4. Check environment configuration
