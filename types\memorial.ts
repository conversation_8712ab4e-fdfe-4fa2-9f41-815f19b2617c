// Memorial-specific types
export interface MemorialApiResponse {
  id: string;
  name: string;
  birthPlace: string;
  birthYear: number;
  deathYear: number;
  birthDate: string;
  deathDate: string;
  religionId: string | null;
  religionName: string | null;
  locationId: string | null;
  description: string;
  submittedBy: string;
  evidenceName: string;
  evidenceImageUrl: string | null;
  createdAt: Date;
  updatedAt: Date;
  cemeteryName: string;
  location: {
    name?: string;
    address?: string;
    province?: string;
    city?: string;
    district?: string;
    subDistrict?: string;
    latitude?: string;
    longitude?: string;
    place_id?: string;
  };
  images: Array<{
    id: string;
    url: string;
    caption: string;
  }>;
  // Package information from orders
  packageId?: string;
  packageName?: string;
  packagePrice?: number;
  packageDuration?: string;
  packageFeatures?: string;
  paymentStatus?: string;
}

// Memorial display types
export interface MemorialDisplayData {
  id: string;
  name: string;
  birthPlace: string;
  birthYear: number;
  deathYear: number;
  birthDate: string;
  deathDate: string;
  cemeteryName: string;
  submittedBy: string;
  description: string;
  images: string[]; // Array of image URLs
  location?: {
    name?: string;
    address?: string;
    province?: string;
    city?: string;
    district?: string;
    subDistrict?: string;
    latitude?: string;
    longitude?: string;
    place_id?: string;
  };
}

// Memorial form data
export interface MemorialFormData {
  name: string;
  birthPlace: string;
  birthDate: string;
  deathDate: string;
  religionId: string | null;
  submittedBy: string;
  description: string;
  images: File[];
  evidenceName: string;
  evidenceImage: File | null;
  locationId?: string; // Always string UUID
}

// Memorial search and filter types
export interface MemorialSearchFilters {
  name?: string;
  birthPlace?: string;
  birthDateFrom?: string;
  birthDateTo?: string;
  deathDateFrom?: string;
  deathDateTo?: string;
  religionId?: string;
  locationId?: string;
}

// Memorial pagination response
export interface MemorialPaginatedResponse {
  success: boolean;
  message: string;
  data: MemorialApiResponse[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
    nextPage: number | null;
    prevPage: number | null;
  };
}
