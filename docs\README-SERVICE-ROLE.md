# Supabase Service Role Configuration

Panduan untuk mengkonfigurasi Supabase Service Role Key untuk bypass RLS policy setelah pembayaran.

## 🔐 Service Role Authentication Flow

### Payment-Based Authorization
```
1. User selects package → No upload permission
2. User completes payment → Gets payment token
3. User uploads files → Service role bypasses RLS
4. Files uploaded successfully → Memorial created
```

### Authorization Levels
| User Status | Supabase Client | Upload Permission |
|-------------|-----------------|-------------------|
| **No Payment** | Regular Client | ❌ Blocked by RLS |
| **After Payment** | Service Role | ✅ Bypass RLS |

## 🛠️ Setup Instructions

### 1. Get Service Role Key from Supabase

1. **Go to Supabase Dashboard**
   - Navigate to your project
   - Go to Settings > API

2. **Copy Service Role Key**
   - Find "Service Role" section
   - Copy the `service_role` key (starts with `eyJ...`)
   - ⚠️ **NEVER expose this key in frontend code**

3. **Add to Environment Variables**
   ```bash
   # .env.production
   SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   ```

### 2. Environment Configuration

#### Development (.env.local)
```bash
# Optional - for testing service role locally
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

#### Production (.env.production)
```bash
# Required for production uploads
SUPABASE_SERVICE_ROLE_KEY=your_production_service_role_key_here
```

## 🔧 How It Works

### Payment Token Validation
```typescript
// Payment token format: pay_timestamp_randomstring
const paymentToken = "pay_1703123456789_abc123def456";

// Validation checks:
1. Token format is correct
2. Token is not expired (24 hours)
3. Token timestamp is valid
```

### Service Role Client Selection
```typescript
// lib/supabaseServiceRole.ts
export function getSupabaseClient(paymentToken?: string) {
  if (hasPaymentAuthorization(paymentToken) && isServiceRoleAvailable()) {
    return supabaseServiceRole; // Bypass RLS
  }
  return null; // Fallback to placeholder
}
```

### Upload Authorization Flow
```typescript
// Before payment
imageStorageService.uploadBase64Image(image, filename); 
// → Uses regular client → RLS blocks → Fallback to placeholder

// After payment
imageStorageService.uploadBase64Image(image, filename, folder, paymentToken);
// → Uses service role → Bypasses RLS → Successful upload
```

## 🔍 Security Features

### Payment Token Security
- ✅ **Time-limited**: Tokens expire after 24 hours
- ✅ **Format validation**: Strict token format checking
- ✅ **Server-side only**: Service role key never exposed to client
- ✅ **Payment verification**: Only valid payment tokens work

### Service Role Protection
- ✅ **Environment-based**: Only loaded from server environment
- ✅ **Conditional usage**: Only used after payment verification
- ✅ **Fallback strategy**: Graceful degradation if not available
- ✅ **Audit logging**: All service role usage is logged

## 🧪 Testing

### Test Payment Authorization
```bash
# Check environment configuration
npm run check:env:prod

# Test with valid payment token
curl -X POST http://localhost:3000/api/purchase \
  -H "Content-Type: application/json" \
  -d '{"paymentToken": "pay_1703123456789_abc123"}'
```

### Test Upload Flow
```typescript
// Valid payment token
const token = "pay_1703123456789_abc123def456";
const result = await imageStorageService.uploadBase64Image(
  base64Image, 
  "test.jpg", 
  "memorial", 
  token
);
// Expected: Successful upload to Supabase

// Invalid/missing token
const result = await imageStorageService.uploadBase64Image(
  base64Image, 
  "test.jpg", 
  "memorial"
);
// Expected: Fallback to placeholder URL
```

## 🚨 Troubleshooting

### Common Issues

#### 1. Service Role Key Not Working
```
Error: Invalid JWT
```
**Solution:**
- Verify service role key is correct
- Check environment variable name: `SUPABASE_SERVICE_ROLE_KEY`
- Ensure key is not expired

#### 2. RLS Still Blocking Uploads
```
Error: new row violates row-level security policy
```
**Solution:**
- Verify payment token is valid and not expired
- Check service role key is properly configured
- Ensure `hasPaymentAuthorization()` returns true

#### 3. Service Role Not Available
```
Warning: Using regular client (RLS applies)
```
**Solution:**
- Add `SUPABASE_SERVICE_ROLE_KEY` to environment
- Restart application after adding environment variable
- Check environment loading with `npm run check:env:prod`

### Debug Commands

```bash
# Check environment variables
npm run check:env:prod

# Test service role availability
node -e "
const { isServiceRoleAvailable } = require('./lib/supabaseServiceRole.ts');
console.log('Service role available:', isServiceRoleAvailable());
"

# Test payment token validation
node -e "
const { validatePaymentToken } = require('./lib/supabaseServiceRole.ts');
const token = 'pay_1703123456789_abc123';
console.log('Token validation:', validatePaymentToken(token));
"
```

## 📊 Monitoring

### Upload Success Metrics
- Monitor upload success rate before/after payment
- Track fallback to placeholder usage
- Log service role authentication attempts

### Security Monitoring
- Monitor service role key usage
- Track payment token validation failures
- Alert on suspicious upload patterns

## 🔒 Production Security Checklist

- [ ] Service role key stored securely in environment
- [ ] Service role key not exposed in client-side code
- [ ] Payment token validation working correctly
- [ ] Upload authorization flow tested
- [ ] Fallback to placeholder working
- [ ] Environment variables properly configured
- [ ] Monitoring and logging in place

---

## 📞 Support

For service role configuration issues:
1. Check troubleshooting section above
2. Verify environment configuration
3. Test payment token validation
4. Check Supabase dashboard for RLS policies
