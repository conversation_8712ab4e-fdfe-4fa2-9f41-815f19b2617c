interface ChartData {
  packageId: string;
  packageName: string;
  count: number;
  percentage: number;
  color: string;
}

interface ChartLegendProps {
  data: ChartData[];
}

export default function ChartLegend({ data }: ChartLegendProps) {
  return (
    <div className="mt-6 space-y-3">
      {data.map((item) => (
        <div key={item.packageId} className="flex items-center gap-4 p-2 rounded-lg bg-memorial-800/50">
          <div
            className="w-5 h-5 rounded-full flex-shrink-0 border-2 border-white/20"
            style={{ backgroundColor: item.color }}
          ></div>
          <span className="text-memorial-100 text-base font-medium">{item.packageName}</span>
          <span className="text-memorial-200 text-base font-semibold ml-auto">
            {item.count} ({item.percentage.toFixed(1)}%)
          </span>
        </div>
      ))}
    </div>
  );
}
