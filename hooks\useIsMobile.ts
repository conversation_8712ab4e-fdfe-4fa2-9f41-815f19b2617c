import { useState, useEffect } from "react";
import { isMobileDevice } from "@/lib/utils/memorialUtils";

/**
 * Custom hook to detect if the current device is mobile
 * @returns Boolean indicating if device is mobile
 */
export function useIsMobile(): boolean {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(isMobileDevice());
    };

    // Set initial value
    checkIfMobile();

    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile);

    // Cleanup event listener
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  return isMobile;
}
