import Image from "next/image";
import { Quote } from "lucide-react";

interface Testimonial {
  quote: string;
  author: string;
  role: string;
  avatar: string;
}

export default function Testimonials() {
  const testimonials: Testimonial[] = [
    {
      quote: "Pemakaman Digital membantu keluarga kami menemukan lokasi pemakaman yang sesuai dengan kebutuhan kami. Proses reservasi sangat mudah dan transparan.",
      author: "<PERSON><PERSON>",
      role: "Jakarta",
      avatar: "https://placehold.co/100x100/nature/white?text=BS"
    },
    {
      quote: "Sebagai pengelola TPU, aplikasi ini sangat membantu kami dalam mengelola data dan administrasi pemakaman secara digital.",
      author: "Siti Rahayu",
      role: "Pengelola TPU Bandung",
      avatar: "https://placehold.co/100x100/nature/white?text=SR"
    },
    {
      quote: "Fitur pencarian lokasi sangat membantu saya menemukan makam kerabat yang sudah lama tidak dikunjungi.",
      author: "<PERSON>",
      role: "<PERSON><PERSON><PERSON>",
      avatar: "https://placehold.co/100x100/nature/white?text=AR"
    }
  ];

  return (
    <section className="py-12">
      <h2 className="text-2xl md:text-3xl font-bold mb-8 text-center">Apa Kata Mereka</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {testimonials.map((testimonial, index) => (
          <div key={index} className="relative bg-memorial-900 p-6 rounded-lg border border-memorial-800 hover:border-candle-500 transition-all hover:scale-105 hover:-translate-x-4 hover:-translate-y-2 duration-300 origin-top-left">
            <Quote className="h-8 w-8 text-candle-500/30 absolute top-4 right-4" />
            
            <p className="text-memorial-300 mb-6 mr-4 italic">"{testimonial.quote}"</p>
            
            <div className="flex items-center">
              <div className="relative w-12 h-12 rounded-full overflow-hidden mr-4">
                <Image 
                  src={testimonial.avatar} 
                  alt={testimonial.author}
                  fill
                  className="object-cover"
                />
              </div>
              <div>
                <h4 className="font-medium text-memorial-50">{testimonial.author}</h4>
                <p className="text-sm text-memorial-400">{testimonial.role}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}