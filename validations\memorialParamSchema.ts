import { z } from 'zod';

// Schema validasi untuk parameter ID
export const memorialIdSchema = z.object({
  id: z.string().uuid("ID harus berupa UUID yang valid")
});

// Schema validasi untuk update memorial
export const updateMemorialSchema = z.object({
  name: z.string().min(1, "Nama tidak boleh kosong").optional(),
  birthPlace: z.string().min(1, "Tempat lahir tidak boleh kosong").optional(),
  birthDate: z.string().min(1, "Tanggal lahir tidak boleh kosong").optional(),
  deathDate: z.string().min(1, "Tanggal meninggal tidak boleh kosong").optional(),
  description: z.string().optional(),  // Changed from lifeStory to description
  religionId: z.string().uuid().optional(),
  locationId: z.string().uuid().optional()
});

// Tipe data untuk parameter ID
export type MemorialIdParam = z.infer<typeof memorialIdSchema>;

// Tipe data untuk update memorial
export type UpdateMemorialRequest = z.infer<typeof updateMemorialSchema>;