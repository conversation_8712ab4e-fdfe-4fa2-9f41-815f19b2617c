import { ApiError } from '@/utils/ApiError';

export interface Province {
  id: string;
  name: string;
}

export interface Regency {
  id: string;
  name: string;
  province_id: string;
}

export interface District {
  id: string;
  name: string;
  regency_id: string;
}

export interface Village {
  id: string;
  name: string;
  district_id: string;
}

class RegionService {
  private baseUrl = 'https://www.emsifa.com/api-wilayah-indonesia/api';
  private cache = new Map<string, any>();
  private cacheExpiry = 24 * 60 * 60 * 1000; // 24 hours

  private async fetchWithCache<T>(url: string, cacheKey: string): Promise<T> {
    // Check cache first
    const cached = this.cache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.data;
    }

    try {
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      // Cache the result
      this.cache.set(cacheKey, {
        data,
        timestamp: Date.now(),
      });

      return data;
    } catch (error) {
      console.error(`Error fetching ${cacheKey}:`, error);
      throw new ApiError(`Gagal memuat data ${cacheKey}`, 500, 'REGION_FETCH_ERROR');
    }
  }

  async getProvinces(): Promise<Province[]> {
    return this.fetchWithCache<Province[]>(
      `${this.baseUrl}/provinces.json`,
      'provinces'
    );
  }

  async getRegencies(provinceId: string): Promise<Regency[]> {
    if (!provinceId) {
      throw new ApiError('Province ID is required', 400, 'VALIDATION_ERROR');
    }

    return this.fetchWithCache<Regency[]>(
      `${this.baseUrl}/regencies/${provinceId}.json`,
      `regencies-${provinceId}`
    );
  }

  async getDistricts(regencyId: string): Promise<District[]> {
    if (!regencyId) {
      throw new ApiError('Regency ID is required', 400, 'VALIDATION_ERROR');
    }

    return this.fetchWithCache<District[]>(
      `${this.baseUrl}/districts/${regencyId}.json`,
      `districts-${regencyId}`
    );
  }

  async getVillages(districtId: string): Promise<Village[]> {
    if (!districtId) {
      throw new ApiError('District ID is required', 400, 'VALIDATION_ERROR');
    }

    return this.fetchWithCache<Village[]>(
      `${this.baseUrl}/villages/${districtId}.json`,
      `villages-${districtId}`
    );
  }

  // Helper method to get region name by ID
  async getRegionName(type: 'province' | 'regency' | 'district' | 'village', id: string, parentId?: string): Promise<string> {
    try {
      switch (type) {
        case 'province': {
          const provinces = await this.getProvinces();
          return provinces.find(p => p.id === id)?.name || '';
        }
        case 'regency': {
          if (!parentId) throw new ApiError('Parent province ID required for regency', 400, 'VALIDATION_ERROR');
          const regencies = await this.getRegencies(parentId);
          return regencies.find(r => r.id === id)?.name || '';
        }
        case 'district': {
          if (!parentId) throw new ApiError('Parent regency ID required for district', 400, 'VALIDATION_ERROR');
          const districts = await this.getDistricts(parentId);
          return districts.find(d => d.id === id)?.name || '';
        }
        case 'village': {
          if (!parentId) throw new ApiError('Parent district ID required for village', 400, 'VALIDATION_ERROR');
          const villages = await this.getVillages(parentId);
          return villages.find(v => v.id === id)?.name || '';
        }
        default:
          return '';
      }
    } catch (error) {
      console.error(`Error getting ${type} name:`, error);
      return '';
    }
  }

  // Method to build complete address from IDs
  async buildCompleteAddress(regionIds: {
    provinceId: string;
    regencyId: string;
    districtId: string;
    villageId: string;
    detailAddress?: string;
  }): Promise<{
    provinsi: string;
    kabupaten_kota: string;
    kecamatan: string;
    kelurahan: string;
    alamat_lengkap: string;
  }> {
    try {
      const [provinsi, kabupaten_kota, kecamatan, kelurahan] = await Promise.all([
        this.getRegionName('province', regionIds.provinceId),
        this.getRegionName('regency', regionIds.regencyId, regionIds.provinceId),
        this.getRegionName('district', regionIds.districtId, regionIds.regencyId),
        this.getRegionName('village', regionIds.villageId, regionIds.districtId),
      ]);

      const alamat_lengkap = [
        regionIds.detailAddress,
        kelurahan,
        kecamatan,
        kabupaten_kota,
        provinsi
      ].filter(Boolean).join(', ');

      return {
        provinsi,
        kabupaten_kota,
        kecamatan,
        kelurahan,
        alamat_lengkap,
      };
    } catch (error) {
      console.error('Error building complete address:', error);
      throw new ApiError('Gagal membangun alamat lengkap', 500, 'ADDRESS_BUILD_ERROR');
    }
  }

  // Clear cache method
  clearCache(): void {
    this.cache.clear();
  }

  // Get cache size for debugging
  getCacheInfo(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    };
  }
}

// Export singleton instance
export const regionService = new RegionService();
export default regionService;
