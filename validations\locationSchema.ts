import { z } from 'zod';

// Schema validasi untuk request lokasi menggunakan Zod
export const locationRequestSchema = z.object({
  provinsi: z.string().min(1, "Provinsi tidak boleh kosong"),
  kabupaten_kota: z.string().min(1, "Kabupaten/Kota tidak boleh kosong"),
  kecamatan: z.string().min(1, "Kecamatan tidak boleh kosong"),
  kelurahan: z.string().min(1, "Kelurahan tidak boleh kosong"),
  alamat_detail: z.string().optional()
});

// Tipe data untuk request lokasi
export type LocationRequest = z.infer<typeof locationRequestSchema>;