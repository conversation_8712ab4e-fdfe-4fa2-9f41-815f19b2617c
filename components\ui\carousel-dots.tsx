"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { type CarouselApi } from "@/components/ui/carousel"

interface CarouselDotsProps extends React.HTMLAttributes<HTMLDivElement> {
  api?: CarouselApi
  count: number
}

export function CarouselDots({
  api,
  count,
  className,
  ...props
}: CarouselDotsProps) {
  const [selectedIndex, setSelectedIndex] = React.useState(0)

  React.useEffect(() => {
    if (!api) {
      return
    }

    const onSelect = () => {
      setSelectedIndex(api.selectedScrollSnap())
    }

    api.on("select", onSelect)
    api.on("reInit", onSelect)

    return () => {
      api.off("select", onSelect)
      api.off("reInit", onSelect)
    }
  }, [api])

  const handleDotClick = React.useCallback(
    (index: number) => {
      api?.scrollTo(index)
    },
    [api]
  )

  return (
    <div
      className={cn("flex justify-center gap-2 mt-4", className)}
      {...props}
    >
      {Array.from({ length: count }).map((_, index) => (
        <button
          key={index}
          className={cn(
            "h-2 w-2 rounded-full transition-colors",
            selectedIndex === index ? "bg-candle-500" : "bg-memorial-700"
          )}
          onClick={() => handleDotClick(index)}
          aria-label={`Go to slide ${index + 1}`}
        />
      ))}
    </div>
  )
}