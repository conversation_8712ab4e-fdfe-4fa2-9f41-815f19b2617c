import { useState } from "react";
import { ChevronDown, ChevronUp } from "lucide-react";

interface FAQItem {
  question: string;
  answer: string;
}

export default function AboutFAQ() {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const faqItems: FAQItem[] = [
    {
      question: "Apa itu Pemakaman Digital?",
      answer: "Pemakaman Digital adalah platform modern untuk mengelola dan mencari informasi pemakaman di Indonesia. Kami menyediakan solusi digital untuk kebutuhan pengelolaan pemakaman, termasuk pencarian lokasi, informasi ketersediaan, dan layanan administrasi digital."
    },
    {
      question: "Bagaimana cara mencari orang yang saya kenali?",
      answer: "Anda dapat menggunakan fitur pencarian di halaman utama atau halaman Pemakaman. Cukup masukkan kriteria seperti nama, tanggal lahir, atau tanggal kematian, dan sistem kami akan menampilkan hasil yang sesuai dengan kebutuhan <PERSON>a."
    },
    {
      question: "Apakah layanan ini tersedia di seluruh Indonesia?",
      answer: "Saat ini kami fokus melayani area Bandung, namun kami terus memperluas jangkauan ke kota-kota besar lainnya di Indonesia. Silakan hubungi kami untuk informasi ketersediaan di area Anda."
    },
    {
      question: "Bagaimana cara mendaftar orang yang sudah meninggal?",
      answer: "Anda dapat langsung melakukan pembelian memorial untuk orang yang sudah meninggal lalu isi biodata almarhum/almarhumah. Silakan hubungi kami untuk informasi lebih lanjut."
    },
    {
      question: "Apakah ada biaya untuk menggunakan layanan ini?",
      answer: "Layanan pencarian dan informasi dasar tersedia gratis untuk umum. Untuk fitur premium seperti reservasi lahan dan layanan pengelolaan digital, kami menerapkan biaya berlangganan yang kompetitif."
    }
  ];

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section className="mb-16">
      <h2 className="text-2xl md:text-3xl font-bold mb-8 text-center">Pertanyaan Umum</h2>
      
      <div className="space-y-4 max-w-4xl mx-auto">
        {faqItems.map((item, index) => (
          <div 
            key={index} 
            className="bg-memorial-900 border border-memorial-800 rounded-lg overflow-hidden"
          >
            <button
              className="w-full p-4 text-left flex justify-between items-center"
              onClick={() => toggleFAQ(index)}
            >
              <h3 className="text-lg font-medium text-memorial-50 mr-8">{item.question}</h3>
              {openIndex === index ? (
                <ChevronUp className="h-5 w-5 text-candle-500" />
              ) : (
                <ChevronDown className="h-5 w-5 text-candle-500" />
              )}
            </button>
            
            {openIndex === index && (
              <div className="p-4 pt-0 text-memorial-300 border-t border-memorial-800">
                <p>{item.answer}</p>
              </div>
            )}
          </div>
        ))}
      </div>
    </section>
  );
}