/**
 * Utility functions for generating and handling slugs
 */

/**
 * Generate a URL-friendly slug from text
 */
export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .trim()
    // Replace Indonesian characters
    .replace(/[à<PERSON><PERSON>ãäå]/g, 'a')
    .replace(/[èéêë]/g, 'e')
    .replace(/[ìíîï]/g, 'i')
    .replace(/[òóôõö]/g, 'o')
    .replace(/[ùúûü]/g, 'u')
    .replace(/[ñ]/g, 'n')
    .replace(/[ç]/g, 'c')
    // Remove special characters except hyphens and spaces
    .replace(/[^a-z0-9\s-]/g, '')
    // Replace spaces and multiple hyphens with single hyphen
    .replace(/[\s-]+/g, '-')
    // Remove leading/trailing hyphens
    .replace(/^-+|-+$/g, '');
}

/**
 * Generate a unique slug for memorials
 */
export function generateMemorialSlug(name: string, birthDate: string, deathDate: string): string {
  const nameSlug = generateSlug(name);
  const birthYear = new Date(birthDate).getFullYear();
  const deathYear = new Date(deathDate).getFullYear();
  
  return `${nameSlug}-${birthYear}-${deathYear}`;
}

/**
 * Generate a unique slug for locations
 */
export function generateLocationSlug(name: string, city: string, province: string): string {
  const nameSlug = generateSlug(name);
  const citySlug = generateSlug(city);
  const provinceSlug = generateSlug(province);
  
  return `${nameSlug}-${citySlug}-${provinceSlug}`;
}

/**
 * Generate a unique slug for religions
 */
export function generateReligionSlug(name: string): string {
  return generateSlug(name);
}

/**
 * Add timestamp suffix to make slug unique if needed
 */
export function makeSlugUnique(baseSlug: string, timestamp?: Date): string {
  const time = timestamp || new Date();
  const suffix = time.getTime().toString().slice(-6); // Last 6 digits of timestamp
  return `${baseSlug}-${suffix}`;
}

/**
 * Validate slug format
 */
export function isValidSlug(slug: string): boolean {
  const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
  return slugRegex.test(slug) && slug.length >= 3 && slug.length <= 255;
}

/**
 * Extract information from memorial slug
 */
export function parseMemorialSlug(slug: string): {
  name: string;
  birthYear: number | null;
  deathYear: number | null;
} {
  const parts = slug.split('-');
  
  if (parts.length < 3) {
    return { name: slug, birthYear: null, deathYear: null };
  }
  
  const deathYear = parseInt(parts[parts.length - 1]);
  const birthYear = parseInt(parts[parts.length - 2]);
  const nameParts = parts.slice(0, -2);
  
  return {
    name: nameParts.join('-'),
    birthYear: isNaN(birthYear) ? null : birthYear,
    deathYear: isNaN(deathYear) ? null : deathYear,
  };
}
