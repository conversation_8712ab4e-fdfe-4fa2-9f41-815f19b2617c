import { LocationFormData } from "@/types/location";

export class LocationValidators {
  static validateProvinsi(provinsi: string): { isValid: boolean; message?: string } {
    if (!provinsi || provinsi.trim() === '') {
      return { isValid: false, message: "Provinsi harus dipilih" };
    }
    return { isValid: true };
  }

  static validateKabupatenKota(kabupatenKota: string): { isValid: boolean; message?: string } {
    if (!kabupatenKota || kabupatenKota.trim() === '') {
      return { isValid: false, message: "Kabupaten/Kota harus dipilih" };
    }
    return { isValid: true };
  }

  static validateKecamatan(kecamatan: string): { isValid: boolean; message?: string } {
    if (!kecamatan || kecamatan.trim() === '') {
      return { isValid: false, message: "Kecamatan harus dipilih" };
    }
    return { isValid: true };
  }

  static validateKel<PERSON>han(kelurahan: string): { isValid: boolean; message?: string } {
    if (!kelurahan || kelurahan.trim() === '') {
      return { isValid: false, message: "Kelurahan harus dipilih" };
    }
    return { isValid: true };
  }

  static validateAlamatDetail(alamatDetail: string): { isValid: boolean; message?: string } {
    // Alamat detail is optional, so always valid
    return { isValid: true };
  }

  static validateLocationForm(formData: LocationFormData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    const provinsiValidation = this.validateProvinsi(formData.provinsi);
    if (!provinsiValidation.isValid && provinsiValidation.message) {
      errors.push(provinsiValidation.message);
    }

    const kabupatenKotaValidation = this.validateKabupatenKota(formData.kabupaten_kota);
    if (!kabupatenKotaValidation.isValid && kabupatenKotaValidation.message) {
      errors.push(kabupatenKotaValidation.message);
    }

    const kecamatanValidation = this.validateKecamatan(formData.kecamatan);
    if (!kecamatanValidation.isValid && kecamatanValidation.message) {
      errors.push(kecamatanValidation.message);
    }

    const kelurahanValidation = this.validateKelurahan(formData.kelurahan);
    if (!kelurahanValidation.isValid && kelurahanValidation.message) {
      errors.push(kelurahanValidation.message);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
