import React from "react";
import Link from "next/link";

interface NavItemProps {
  icon: React.ReactNode;
  label: string;
  href?: string;
  onClick?: () => void;
}

export default function NavItem({ icon, label, href = "#", onClick }: NavItemProps) {
  return (
    <Link
      href={href}
      onClick={onClick}
      className="flex items-center gap-2 md:gap-3 p-3 md:p-0 rounded-lg md:rounded-none text-sm md:text-sm lg:text-base xl:text-lg text-memorial-300 hover:text-candle-300 hover:bg-memorial-800 md:hover:bg-transparent hover:scale-105 md:hover:underline transition-all duration-200 group"
    >
      <span className="text-memorial-400 group-hover:text-candle-500 transition-colors">{icon}</span>
      <span className="font-medium md:font-normal">{label}</span>
    </Link>
  );
}




