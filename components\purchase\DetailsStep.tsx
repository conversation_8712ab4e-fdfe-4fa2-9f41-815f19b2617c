import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { BioData } from "@/components/purchase/BioDataStep";
import { Badge } from "../ui/badge";
import { MapPin } from "lucide-react";
import religionService from "@/lib/services/religionService";
import { PackageDetails, LocationSearchResult } from "@/types";
import { withErrorHandling } from "@/lib/errorHandler";
import { packageService } from "@/lib/services/packageService";
import { useToast } from "@/hooks/useToast";

interface DetailsStepProps {
  selectedLocation: LocationSearchResult;
  bioData: BioData;
  initialPackage?: string;
  onProceedToPayment: (packageDetails: PackageDetails) => void;
  onReset?: () => void;
}

export default function DetailsStep({
  selectedLocation,
  bioData,
  initialPackage,
  onProceedToPayment,
}: DetailsStepProps) {
  const [selectedPackage, setSelectedPackage] = useState<string>(initialPackage || "free");
  const [religionName, setReligionName] = useState<string>("Tidak diketahui");
  const [packages, setPackages] = useState<Record<string, PackageDetails>>({});
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  // Reset selectedPackage when initialPackage changes (e.g., when store is reset)
  useEffect(() => {
    setSelectedPackage(initialPackage || "free");
  }, [initialPackage]);

  // Load packages from database
  useEffect(() => {
    const loadPackages = async () => {
      try {
        setLoading(true);
        const packagesData = await packageService.getPackagesAsDetails();
        setPackages(packagesData);
      } catch (error) {
        console.error('❌ Error loading packages:', error);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Gagal memuat data paket. Menggunakan data default.",
        });

        // Fallback to hardcoded packages
        setPackages(getDefaultPackages());
      } finally {
        setLoading(false);
      }
    };

    loadPackages();
  }, [toast]);

  // Load religion name when bioData changes
  useEffect(() => {
    const loadReligionName = async () => {
      if (bioData?.religionId && typeof bioData.religionId === 'string') {
        const religion = await withErrorHandling(
          () => religionService.getReligionById(bioData.religionId as string),
          'Load Religion Name'
        );
        setReligionName(religion?.name || "Tidak diketahui");
      } else {
        setReligionName("Tidak diketahui");
      }
    };
    loadReligionName();
  }, [bioData?.religionId]);


  // Function to get religion name (now handled by useEffect above)
  const getReligionName = () => {
    return religionName;
  };
  
  // Default packages as fallback
    const getDefaultPackages = (): Record<string, PackageDetails> => ({
    free: {
      id: "free",
      name: "Paket Free",
      price: 0,
      duration: "3 bulan",
      features: [
        "Memorial digital dasar",
        "1 foto",
        "Informasi dasar almarhum/almarhumah",
        "Masa aktif 3 bulan"
      ]
    },
    standard: {
      id: "standard",
      name: "Paket Standar",
      price: 150000,
      duration: "1 tahun",
      features: [
        "2 foto memorial",
        "Profil lengkap almarhum",
        "Link lokasi pemakaman",
        "Tanpa iklan",
        "Dukungan prioritas",
        "Masa aktif 1 tahun"
      ]
    },
    premium: {
      id: "premium",
      name: "Paket Premium",
      price: 970000,
      duration: "Selamanya",
      features: [
        "Semua fitur Paket Standar",
        "Tanpa iklan",
        "Dukungan prioritas",
        "Backup data", 
        "Coming Soon: Layanan ziarah virtual", 
        "Coming Soon: Generate kisah hidup almarhum/almarhumah with AI", 
        "Coming Soon: Generate QR code untuk akses langsung ke memorial", ]
    }
  });

  const handlePackageSelect = (packageId: string) => {
    setSelectedPackage(packageId);
  };

  const handleProceedToPayment = () => {
    onProceedToPayment(packages[selectedPackage]);
  };

  if (loading) {
    return (
      <div className="bg-memorial-900 p-6 rounded-lg border border-memorial-800">
        <h2 className="text-xl font-semibold mb-4">Detail Pemesanan</h2>
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-candle-500"></div>
          <span className="ml-3 text-memorial-300">Memuat data paket...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-memorial-900 p-6 rounded-lg border border-memorial-800">
      <h2 className="text-xl font-semibold mb-4">Detail Pemesanan</h2>
      
      {/* Biodata Summary */}
      <div className="mb-6">
        <h3 className="text-lg font-medium mb-2">Data Almarhum/Almarhumah</h3>
        <div className="bg-memorial-800 p-4 rounded-lg">
          {/* Foto - Di atas biodata */}
          <div className="mb-6">
            <h4 className="text-memorial-200 text-sm font-medium mb-3">Foto</h4>
            <div className="flex gap-3  flex-wrap">
              {bioData.images && bioData.images.length > 0 ? (
                bioData.images.map((image, index) => (
                  <div key={index} className="w-32 h-32 sm:w-40 sm:h-40 bg-memorial-700 rounded-md overflow-hidden">
                    {typeof image === 'string' ? (
                      <div className="w-full h-full bg-memorial-600 flex items-center justify-center">
                        <span className="text-memorial-400 text-xs">Foto {index + 1}</span>
                      </div>
                    ) : (
                      <img
                        src={URL.createObjectURL(image)}
                        alt={`Foto ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    )}
                  </div>
                ))
              ) : (
                <div className="w-32 h-32 sm:w-40 sm:h-40 bg-memorial-700 rounded-md flex items-center justify-center">
                  <span className="text-memorial-400 text-xs">Tidak ada foto</span>
                </div>
              )}
            </div>
          </div>

          {/* Data Biodata */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
            <div>
              <p className="text-memorial-400 text-sm">Nama Lengkap</p>
              <p className="text-memorial-50">{bioData.name}</p>
            </div>
            <div>
              <p className="text-memorial-400 text-sm">Tempat Lahir</p>
              <p className="text-memorial-50">{bioData.birthPlace}</p>
            </div>
            <div>
              <p className="text-memorial-400 text-sm">Tanggal Lahir</p>
              <p className="text-memorial-50">{bioData.birthDate}</p>
            </div>
            <div>
              <p className="text-memorial-400 text-sm">Tanggal Meninggal</p>
              <p className="text-memorial-50">{bioData.deathDate}</p>
            </div>
            <div>
              <p className="text-memorial-400 text-sm">Agama</p>
              <p className="text-memorial-50">{getReligionName()}</p>
            </div>
            <div>
              <p className="text-memorial-400 text-sm">Diajukan Oleh</p>
              <p className="text-memorial-50">{bioData.submittedBy}</p>
            </div>
          </div>

          {/* Life Story */}
          {bioData.description && (
            <div>
              <p className="text-memorial-400 text-sm mb-2">Kisah Hidup</p>
              <p className="text-memorial-50 text-sm leading-relaxed">{bioData.description}</p>
            </div>
          )}
        </div>
      </div>
      
      {/* Location */}
      <div className="mb-6">
        <h3 className="text-lg font-medium mb-2">Lokasi Pemakaman Terpilih</h3>
        <div className="bg-memorial-800 p-4 rounded-lg">
          <div className="flex gap-3 items-center">
            <MapPin className="h-5 w-5 text-candle-500 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-sm text-memorial-400 mt-1">{selectedLocation.formatted_address || ''}</p>
            </div>
          </div>
        </div>
      </div>
      
      {/* Package Selection */}
      <div className="mb-6">
        <h3 className="text-lg font-medium mb-2 text-center">Pilihan Paket</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
          {Object.values(packages).filter(pkg => pkg.id !== 'standard').map((pkg) => (
            <div
              key={pkg.id}
              className={`bg-memorial-800 p-6 rounded-lg border cursor-pointer transition-all ${
                selectedPackage === pkg.id
                  ? "border-candle-500 ring-1 ring-candle-500"
                  : "border-memorial-700 hover:border-memorial-600"
              }`}
              onClick={() => handlePackageSelect(pkg.id)}
            >
              <div className="flex justify-between items-start mb-2">
                <h4 className={`font-medium ${selectedPackage === pkg.id ? "text-candle-500" : ""}`}>
                  {pkg.name}
                </h4>
                <Badge variant="outline" className="bg-memorial-700 text-memorial-200">
                  {pkg.duration}
                </Badge>
              </div>
              <p className="text-sm text-memorial-300 mb-3">
                {pkg.id === 'free' && 'Memorial digital gratis dengan masa aktif terbatas'}
                {pkg.id === 'premium' && 'Memorial digital premium dengan semua fitur'}
              </p>
              <p className={`text-lg font-bold mb-3 ${pkg.price === 0 ? 'text-green-400' : 'text-candle-500'}`}>
                {packageService.formatPrice(pkg.price)}
              </p>
              <div className="text-sm text-memorial-400">
                <ul className="space-y-1">
                  {pkg.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <span className="mr-2">•</span>
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      <div className="flex flex-col sm:flex-row gap-3">
        <Button
          className="flex-1 bg-candle-500 text-memorial-950 hover:bg-candle-400"
          onClick={handleProceedToPayment}
          disabled={!packages[selectedPackage]}
        >
          Lanjut ke Pembayaran - {packages[selectedPackage] ? packageService.formatPrice(packages[selectedPackage].price) : 'Loading...'}
        </Button>
      </div>
    </div>
  );
}


