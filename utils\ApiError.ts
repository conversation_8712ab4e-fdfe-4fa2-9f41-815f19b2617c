export class ApiError extends Error {
  statusCode: number;
  code: string;
  
  constructor(message: string, statusCode: number = 500, code: string = 'INTERNAL_SERVER_ERROR') {
    super(message);
    this.name = 'ApiError';
    this.statusCode = statusCode;
    this.code = code;
  }
  
  static badRequest(message: string, code: string = 'BAD_REQUEST') {
    return new ApiError(message, 400, code);
  }
  
  static unauthorized(message: string = 'Unauthorized', code: string = 'UNAUTHORIZED') {
    return new ApiError(message, 401, code);
  }
  
  static forbidden(message: string = 'Forbidden', code: string = 'FORBIDDEN') {
    return new ApiError(message, 403, code);
  }
  
  static notFound(message: string = 'Resource not found', code: string = 'NOT_FOUND') {
    return new ApiError(message, 404, code);
  }
  
  static conflict(message: string, code: string = 'CONFLICT') {
    return new ApiError(message, 409, code);
  }
  
  static internal(message: string = 'Internal server error', code: string = 'INTERNAL_SERVER_ERROR') {
    return new ApiError(message, 500, code);
  }
}