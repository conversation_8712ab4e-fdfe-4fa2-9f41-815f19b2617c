import { NextRequest, NextResponse } from 'next/server';
import { memorialService } from '@/lib/services/memorialService';
import { handleApiError } from '@/utils/errorHandler';

/**
 * GET handler untuk mendapatkan memorial berdasarkan slug
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const slug = params.slug;
    
    if (!slug) {
      return NextResponse.json(
        { success: false, error: 'Slug is required' },
        { status: 400 }
      );
    }
    
    // Dapatkan memorial dari service berdasarkan slug
    const memorial = await memorialService.getMemorialBySlug(slug);
    
    if (!memorial) {
      return NextResponse.json(
        { success: false, error: 'Memorial not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ 
      success: true, 
      data: memorial 
    });
    
  } catch (error) {
    console.error('Error fetching memorial by slug:', error);
    return handleApiError(error);
  }
}
