import { getLocationCoordinates } from '@/functions/getLocationCoordinates';
import { LocationRequest } from '@/validations/locationSchema';

/**
 * Service untuk menangani operasi terkait lokasi
 */
export async function getCoordinatesForLocation(locationData: LocationRequest) {
  // Get coordinates from Google Maps API
  return await getLocationCoordinates({
    provinsi: locationData.provinsi,
    kabupaten_kota: locationData.kabupaten_kota,
    kecamatan: locationData.kecamatan,
    kelurahan: locationData.kelurahan,
    alamat_detail: locationData.alamat_detail || ''
  });
}