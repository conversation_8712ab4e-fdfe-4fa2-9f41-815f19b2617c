import { Home } from "lucide-react";
import { Facebook, Instagram, Twitter, Linkedin, Youtube } from "lucide-react";

export default function Footer() {
  return (
    <footer className="bg-memorial-900 border-t border-memorial-800 py-12 px-4 md:px-8">
      <div className="container mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Logo and company info */}
          <div className="flex flex-col">
            <div className="flex items-center gap-2 text-memorial-50 mb-4">
              <Home className="h-6 w-6 text-candle-500" />
              <span className="font-bold text-xl">Pemakaman Digital</span>
            </div>
            <p className="text-memorial-300 mb-4">
              Platform modern untuk mengelola dan mencari informasi pemakaman di Indonesia.
            </p>
            <p className="text-memorial-400 text-sm">
              © 2023 Pemakaman Digital. Hak Cipta Dilindungi.
            </p>
          </div>
          
          {/* Quick links */}
          <div>
            <h3 className="text-lg font-semibold mb-4 text-memorial-50">Tautan</h3>
            <ul className="space-y-2 text-memorial-300">
              <li>
                <a href="/" className="hover:text-candle-500 transition-colors">
                  Beranda
                </a>
              </li>
              <li>
                <a href="/pemakaman" className="hover:text-candle-500 transition-colors">
                  Pemakaman
                </a>
              </li>
              <li>
                <a href="/tentang-kami" className="hover:text-candle-500 transition-colors">
                  Tentang Kami
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-candle-500 transition-colors">
                  Kebijakan Privasi
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-candle-500 transition-colors">
                  Syarat & Ketentuan
                </a>
              </li>
            </ul>
          </div>
          
          {/* Social media */}
          <div>
            <h3 className="text-lg font-semibold mb-4 text-memorial-50">Ikuti Kami</h3>
            <div className="flex space-x-4 mb-6">
              <a 
                href="#" 
                className="bg-memorial-800 hover:bg-memorial-700 p-2 rounded-full transition-colors"
                aria-label="Facebook"
              >
                <Facebook className="h-5 w-5 text-candle-500" />
              </a>
              <a 
                href="#" 
                className="bg-memorial-800 hover:bg-memorial-700 p-2 rounded-full transition-colors"
                aria-label="Instagram"
              >
                <Instagram className="h-5 w-5 text-candle-500" />
              </a>
              <a 
                href="#" 
                className="bg-memorial-800 hover:bg-memorial-700 p-2 rounded-full transition-colors"
                aria-label="Twitter"
              >
                <Twitter className="h-5 w-5 text-candle-500" />
              </a>
              <a 
                href="#" 
                className="bg-memorial-800 hover:bg-memorial-700 p-2 rounded-full transition-colors"
                aria-label="LinkedIn"
              >
                <Linkedin className="h-5 w-5 text-candle-500" />
              </a>
              <a 
                href="#" 
                className="bg-memorial-800 hover:bg-memorial-700 p-2 rounded-full transition-colors"
                aria-label="YouTube"
              >
                <Youtube className="h-5 w-5 text-candle-500" />
              </a>
            </div>
            
            <h3 className="text-lg font-semibold mb-4 text-memorial-50">Kontak</h3>
            <p className="text-memorial-300"><EMAIL></p>
            <p className="text-memorial-300">+62 89 529 307 135</p>
          </div>
        </div>
      </div>
    </footer>
  );
}