import { NextResponse } from 'next/server';
import type { NextRequest, NextFetchEvent } from 'next/server';

export function middleware(request: NextRequest, event: NextFetchEvent) {
  try {
    // Middleware logic here
    // For example, you can add logging, authentication checks, etc.
    
    // Continue to the next middleware or route handler
    return NextResponse.next();
  } catch (error) {
    console.error('Middleware error:', error);
    
    // Log the error for monitoring
    // event.waitUntil(
    //   fetch(process.env.ERROR_LOGGING_ENDPOINT || 'https://log.example.com', {
    //     method: 'POST',
    //     headers: { 'Content-Type': 'application/json' },
    //     body: JSON.stringify({
    //       path: request.nextUrl.pathname,
    //       method: request.method,
    //       error: error instanceof Error ? error.message : 'Unknown error',
    //       timestamp: new Date().toISOString(),
    //     }),
    //   }).catch(err => console.error('Failed to log error:', err))
    // );
    
    // Return a JSON error response
    return NextResponse.json(
      { 
        success: false, 
        error: 'An unexpected error occurred',
        code: 'INTERNAL_SERVER_ERROR'
      },
      { status: 500 }
    );
  }
}

// Configure which paths this middleware applies to
export const config = {
  matcher: '/api/:path*',
};