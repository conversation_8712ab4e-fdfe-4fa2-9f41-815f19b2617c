// Note: Sharp compression functions moved to server-side only
// This file now contains only client-side safe utilities

export interface CompressionOptions {
  quality?: number;
  maxWidth?: number;
  maxHeight?: number;
  format?: 'jpeg' | 'png' | 'webp';
  progressive?: boolean;
}

export interface CompressionResult {
  buffer: Buffer;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  format: string;
}

export class ImageHelpers {
  /**
   * Get maximum file size in bytes (5MB)
   */
  static getMaxFileSize(): number {
    return 5 * 1024 * 1024; // 5MB
  }

  /**
   * Get accepted image formats
   */
  static getAcceptedFormats(): string {
    return 'image/*';
  }

  /**
   * Get accepted image extensions for display
   */
  static getAcceptedExtensions(): string {
    return 'JPG, PNG';
  }

  /**
   * Validate image file
   */
  static validateImageFile(file: File): { isValid: boolean; error?: string } {
    // Check file type
    if (!file.type.startsWith('image/')) {
      return { isValid: false, error: 'File harus berupa gambar' };
    }

    // Check file size
    if (file.size > this.getMaxFileSize()) {
      return { isValid: false, error: 'Ukuran file maksimal 5MB' };
    }

    return { isValid: true };
  }

  /**
   * Create object URL for image preview
   */
  static createPreviewUrl(file: File): string {
    return URL.createObjectURL(file);
  }

  /**
   * Revoke object URL to free memory
   */
  static revokePreviewUrl(url: string): void {
    URL.revokeObjectURL(url);
  }

  /**
   * Limit images array to maximum count
   */
  static limitImages(existingImages: File[], newImages: File[], maxCount: number): File[] {
    return [...existingImages, ...newImages].slice(0, maxCount);
  }

  /**
   * Remove image from array by index
   */
  static removeImageByIndex(images: File[], index: number): File[] {
    return images.filter((_, i) => i !== index);
  }

  /**
   * Get file size in human readable format
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Check if browser supports file API
   */
  static supportsFileAPI(): boolean {
    return (
      typeof window.File !== 'undefined' &&
      typeof window.FileReader !== 'undefined' &&
      typeof window.FileList !== 'undefined' &&
      typeof window.Blob !== 'undefined'
    );
  }

  /**
   * Get image dimensions
   */
  static getImageDimensions(file: File): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const url = this.createPreviewUrl(file);

      img.onload = () => {
        this.revokePreviewUrl(url);
        resolve({ width: img.width, height: img.height });
      };

      img.onerror = () => {
        this.revokePreviewUrl(url);
        reject(new Error('Failed to load image'));
      };

      img.src = url;
    });
  }

  // Note: Sharp compression functions moved to imageCompressionService.ts (server-side only)
  // These functions are not available in client-side code due to Node.js dependencies

  /**
   * Convert File to Buffer (for server-side processing)
   */
  static async fileToBuffer(file: File): Promise<Buffer> {
    const arrayBuffer = await file.arrayBuffer();
    return Buffer.from(arrayBuffer);
  }

  /**
   * Convert base64 to Buffer
   */
  static base64ToBuffer(base64: string): Buffer {
    // Remove data URL prefix if present
    const base64Data = base64.replace(/^data:image\/[a-z]+;base64,/, '');
    return Buffer.from(base64Data, 'base64');
  }

  /**
   * Convert Buffer to base64
   */
  static bufferToBase64(buffer: Buffer, mimeType: string = 'image/jpeg'): string {
    const base64 = buffer.toString('base64');
    return `data:${mimeType};base64,${base64}`;
  }

  /**
   * Get optimal compression settings for different use cases
   */
  static getCompressionPresets() {
    return {
      // For thumbnails and previews
      thumbnail: {
        quality: 75,
        maxWidth: 400,
        maxHeight: 400,
        format: 'webp' as const,
        progressive: true
      },

      // For web display
      web: {
        quality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
        format: 'jpeg' as const,
        progressive: true
      },

      // For high quality storage
      archive: {
        quality: 90,
        maxWidth: 2560,
        maxHeight: 1920,
        format: 'jpeg' as const,
        progressive: true
      },

      // For mobile optimization
      mobile: {
        quality: 75,
        maxWidth: 1080,
        maxHeight: 1920,
        format: 'webp' as const,
        progressive: true
      }
    };
  }
}
