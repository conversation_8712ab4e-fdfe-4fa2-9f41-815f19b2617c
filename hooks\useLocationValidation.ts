import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/errorHandler";
import { LocationFormData } from "@/types/location";

export function useLocationValidation() {
  const validateLocationForm = (formData: LocationFormData): boolean => {
    try {
      // Custom validation with specific error messages
      if (!formData.provinsi) {
        ErrorHandler.handle("Provinsi harus dipilih", "Form Validation");
        return false;
      }

      if (!formData.kabupaten_kota) {
        ErrorHandler.handle("Kabupaten/Kota harus dipilih", "Form Validation");
        return false;
      }

      if (!formData.kecamatan) {
        ErrorHandler.handle("Kecamatan harus dipilih", "Form Validation");
        return false;
      }

      if (!formData.kelurahan) {
        ErrorHandler.handle("Kelurahan harus dipilih", "Form Validation");
        return false;
      }

      return true;
    } catch (error) {
      ErrorHandler.handle(error, "Location Form Validation");
      return false;
    }
  };

  return {
    validateLocationForm
  };
}
