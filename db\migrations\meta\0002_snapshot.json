{"version": "5", "dialect": "pg", "id": "373241c0-5fba-4943-8168-d1fdbf118a88", "prevId": "e8bd7ff5-2c64-4cf8-ab55-b490089821ed", "tables": {"locations": {"name": "locations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "address_detail": {"name": "address_detail", "type": "text", "primaryKey": false, "notNull": true}, "province": {"name": "province", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "district": {"name": "district", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "sub_district": {"name": "sub_district", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "latitude": {"name": "latitude", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "longitude": {"name": "longitude", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "place_id": {"name": "place_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"location_slug_idx": {"name": "location_slug_idx", "columns": ["slug"], "isUnique": false}, "location_name_idx": {"name": "location_name_idx", "columns": ["name"], "isUnique": false}, "location_province_idx": {"name": "location_province_idx", "columns": ["province"], "isUnique": false}, "location_city_idx": {"name": "location_city_idx", "columns": ["city"], "isUnique": false}, "location_district_idx": {"name": "location_district_idx", "columns": ["district"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"locations_slug_unique": {"name": "locations_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}}, "memorial_images": {"name": "memorial_images", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "memorial_id": {"name": "memorial_id", "type": "uuid", "primaryKey": false, "notNull": true}, "image_url": {"name": "image_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "caption": {"name": "caption", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"memorial_images_memorial_idx": {"name": "memorial_images_memorial_idx", "columns": ["memorial_id"], "isUnique": false}}, "foreignKeys": {"memorial_images_memorial_id_memorials_id_fk": {"name": "memorial_images_memorial_id_memorials_id_fk", "tableFrom": "memorial_images", "tableTo": "memorials", "columnsFrom": ["memorial_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "memorials": {"name": "memorials", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "birth_place": {"name": "birth_place", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "birth_date": {"name": "birth_date", "type": "date", "primaryKey": false, "notNull": true}, "death_date": {"name": "death_date", "type": "date", "primaryKey": false, "notNull": true}, "religion_id": {"name": "religion_id", "type": "uuid", "primaryKey": false, "notNull": false}, "location_id": {"name": "location_id", "type": "uuid", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "submitted_by": {"name": "submitted_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "evidence_name": {"name": "evidence_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "evidence_image_url": {"name": "evidence_image_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"memorial_slug_idx": {"name": "memorial_slug_idx", "columns": ["slug"], "isUnique": false}, "memorial_name_idx": {"name": "memorial_name_idx", "columns": ["name"], "isUnique": false}, "memorial_religion_idx": {"name": "memorial_religion_idx", "columns": ["religion_id"], "isUnique": false}, "memorial_location_idx": {"name": "memorial_location_idx", "columns": ["location_id"], "isUnique": false}, "memorial_birth_date_idx": {"name": "memorial_birth_date_idx", "columns": ["birth_date"], "isUnique": false}, "memorial_death_date_idx": {"name": "memorial_death_date_idx", "columns": ["death_date"], "isUnique": false}, "memorial_name_birth_idx": {"name": "memorial_name_birth_idx", "columns": ["name", "birth_date"], "isUnique": false}, "memorial_name_death_idx": {"name": "memorial_name_death_idx", "columns": ["name", "death_date"], "isUnique": false}, "memorial_birth_death_idx": {"name": "memorial_birth_death_idx", "columns": ["birth_date", "death_date"], "isUnique": false}, "memorial_all_fields_idx": {"name": "memorial_all_fields_idx", "columns": ["name", "birth_date", "death_date"], "isUnique": false}}, "foreignKeys": {"memorials_religion_id_religions_id_fk": {"name": "memorials_religion_id_religions_id_fk", "tableFrom": "memorials", "tableTo": "religions", "columnsFrom": ["religion_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "memorials_location_id_locations_id_fk": {"name": "memorials_location_id_locations_id_fk", "tableFrom": "memorials", "tableTo": "locations", "columnsFrom": ["location_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"memorials_slug_unique": {"name": "memorials_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}}, "orders": {"name": "orders", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "memorial_id": {"name": "memorial_id", "type": "uuid", "primaryKey": false, "notNull": true}, "package_id": {"name": "package_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "package_name": {"name": "package_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "package_price": {"name": "package_price", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "package_duration": {"name": "package_duration", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "package_features": {"name": "package_features", "type": "text", "primaryKey": false, "notNull": true}, "admin_fee": {"name": "admin_fee", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "total_price": {"name": "total_price", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "payment_method": {"name": "payment_method", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "payment_status": {"name": "payment_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'pending'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"order_memorial_idx": {"name": "order_memorial_idx", "columns": ["memorial_id"], "isUnique": false}, "order_package_idx": {"name": "order_package_idx", "columns": ["package_id"], "isUnique": false}, "order_status_idx": {"name": "order_status_idx", "columns": ["payment_status"], "isUnique": false}, "order_created_at_idx": {"name": "order_created_at_idx", "columns": ["created_at"], "isUnique": false}}, "foreignKeys": {"orders_memorial_id_memorials_id_fk": {"name": "orders_memorial_id_memorials_id_fk", "tableFrom": "orders", "tableTo": "memorials", "columnsFrom": ["memorial_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "orders_package_id_packages_id_fk": {"name": "orders_package_id_packages_id_fk", "tableFrom": "orders", "tableTo": "packages", "columnsFrom": ["package_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "packages": {"name": "packages", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "price": {"name": "price", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "duration": {"name": "duration", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "features": {"name": "features", "type": "text", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"package_active_idx": {"name": "package_active_idx", "columns": ["is_active"], "isUnique": false}, "package_sort_idx": {"name": "package_sort_idx", "columns": ["sort_order"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "religions": {"name": "religions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"religions_slug_unique": {"name": "religions_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}, "religions_name_unique": {"name": "religions_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}}}, "enums": {}, "schemas": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}}