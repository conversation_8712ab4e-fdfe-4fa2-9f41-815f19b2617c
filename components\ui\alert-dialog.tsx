import React from "react";
import { AlertTriangle, X } from "lucide-react";
import { But<PERSON> } from "./button";

interface AlertDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  description: string;
  confirmText?: string;
  cancelText?: string;
  variant?: "default" | "destructive";
}

export function AlertDialog({ 
  isOpen, 
  onClose, 
  onConfirm, 
  title, 
  description,
  confirmText = "Konfirmasi",
  cancelText = "Batal",
  variant = "default"
}: AlertDialogProps) {
  if (!isOpen) return null;
  
  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
      <div className="bg-memorial-900 border border-memorial-700 rounded-lg shadow-xl w-full max-w-md p-6 relative">
        <button 
          onClick={onClose}
          className="absolute top-4 right-4 text-memorial-400 hover:text-memorial-200"
        >
          <X className="h-5 w-5" />
        </button>
        
        <div className="flex items-center gap-3 mb-4">
          <div className={`p-2 rounded-full ${
            variant === "destructive" 
              ? "bg-red-900/30 text-red-400" 
              : "bg-amber-900/30 text-amber-400"
          }`}>
            <AlertTriangle className="h-5 w-5" />
          </div>
          <h2 className="text-lg font-semibold text-memorial-50">{title}</h2>
        </div>
        
        <p className="text-memorial-300 mb-6 leading-relaxed">
          {description}
        </p>
        
        <div className="flex flex-col sm:flex-row gap-3 sm:justify-end">
          <Button
            variant="outline"
            onClick={onClose}
            className="border-memorial-600 text-memorial-300 hover:bg-memorial-800 hover:text-memorial-50"
          >
            {cancelText}
          </Button>
          <Button
            onClick={handleConfirm}
            variant={variant === "destructive" ? "destructive" : "default"}
            className={variant === "destructive" 
              ? "bg-red-600 text-white hover:bg-red-700" 
              : "bg-candle-500 text-memorial-950 hover:bg-candle-400"
            }
          >
            {confirmText}
          </Button>
        </div>
      </div>
    </div>
  );
}
