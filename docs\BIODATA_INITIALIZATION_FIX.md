# BioDataStep Initialization Fix

## Problem Description

```
Unhandled Runtime Error
ReferenceError: Cannot access 'religions' before initialization

Source: components\purchase\BioDataStep.tsx (104:67)
```

## Root Cause

The error occurred because the `religions` state variable was being referenced in a useEffect dependency array before it was declared.

### Problematic Code Structure

```typescript
// ❌ BEFORE (Problematic)
export default function BioDataStep({ initialData, onProceedToLocation, onReset }: BioDataStepProps) {
  const { selectedReligion } = useReligionStore();
  
  const [bioData, setBioData] = useState<BioData>(...);

  // Reset bioData when initialData changes
  useEffect(() => {
    // ... logic ...
  }, [initialData, JSON.stringify(initialData), selectedReligion, religions]); // ❌ religions used here
  
  // State untuk data agama
  const [religions, setReligions] = useState<{id: number, name: string}[]>([]); // ❌ but declared here
}
```

### Issue Analysis

1. **Variable Hoisting**: JavaScript hoists variable declarations but not initializations
2. **Temporal Dead Zone**: `religions` exists but cannot be accessed before its declaration
3. **useEffect Dependencies**: The dependency array is evaluated during component initialization
4. **React Hooks Order**: Hooks must be called in the same order every time

## Solution Implementation

### Fixed Code Structure

```typescript
// ✅ AFTER (Fixed)
export default function BioDataStep({ initialData, onProceedToLocation, onReset }: BioDataStepProps) {
  const { selectedReligion } = useReligionStore();
  
  // ✅ State declarations moved to the top
  const [religions, setReligions] = useState<{id: number, name: string}[]>([]);
  
  const [bioData, setBioData] = useState<BioData>(...);

  // Reset bioData when initialData changes
  useEffect(() => {
    // ... logic ...
  }, [initialData, JSON.stringify(initialData), selectedReligion, religions]); // ✅ religions now accessible
}
```

### Changes Made

1. **Moved State Declaration**: `religions` state moved before its first usage
2. **Removed Duplicate**: Eliminated duplicate `religions` declaration
3. **Maintained Order**: Preserved React hooks order rules
4. **Preserved Functionality**: All existing logic remains intact

## Technical Details

### React Hooks Rules

1. **Always call hooks at the top level**: Don't call hooks inside loops, conditions, or nested functions
2. **Call hooks in the same order**: Ensure hooks are called in the same order every time
3. **Declare before use**: Variables must be declared before being referenced

### JavaScript Temporal Dead Zone

```typescript
// Temporal Dead Zone example
console.log(myVar); // ❌ ReferenceError: Cannot access 'myVar' before initialization
const myVar = 'Hello';
console.log(myVar); // ✅ Works fine
```

### useEffect Dependencies

```typescript
// Dependencies are evaluated during component initialization
useEffect(() => {
  // Effect logic
}, [dep1, dep2, dep3]); // All dependencies must be accessible here
```

## Testing Results

### Initialization Test

```bash
npm run test:biodata:init

# Results:
✅ Variable declaration order fixed
✅ useEffect dependencies properly defined  
✅ Religion mapping function working
✅ Component initialization logic correct
```

### Test Coverage

1. **Variable Declaration Order**
   - ✅ `religions` declared before usage
   - ✅ No reference errors

2. **useEffect Dependencies**
   - ✅ All dependencies properly defined
   - ✅ No undefined references

3. **Religion Mapping Function**
   - ✅ Function works correctly
   - ✅ Proper mapping logic

4. **Component Initialization**
   - ✅ State initialization works
   - ✅ Logic flow preserved

## Before vs After Comparison

### Before (Problematic)

```typescript
export default function BioDataStep() {
  const { selectedReligion } = useReligionStore();
  const [bioData, setBioData] = useState(...);
  
  useEffect(() => {
    // logic
  }, [..., religions]); // ❌ Error: Cannot access 'religions' before initialization
  
  const [religions, setReligions] = useState([]); // Declared after usage
}
```

### After (Fixed)

```typescript
export default function BioDataStep() {
  const { selectedReligion } = useReligionStore();
  const [religions, setReligions] = useState([]); // ✅ Declared first
  const [bioData, setBioData] = useState(...);
  
  useEffect(() => {
    // logic
  }, [..., religions]); // ✅ Works fine
}
```

## Impact Assessment

### What Was Fixed

1. **Runtime Error**: Eliminated "Cannot access before initialization" error
2. **Component Loading**: BioDataStep now loads without errors
3. **User Experience**: Purchase form works correctly
4. **Development**: No more console errors during development

### What Was Preserved

1. **Functionality**: All religion store integration features
2. **Logic**: Priority logic and mapping functions intact
3. **Performance**: No performance impact
4. **Type Safety**: TypeScript types maintained

## Prevention Guidelines

### Best Practices

1. **Declare Early**: Declare state variables at the top of components
2. **Group Related**: Group related state declarations together
3. **Document Dependencies**: Comment complex dependency arrays
4. **Test Thoroughly**: Test component initialization scenarios

### Code Review Checklist

- [ ] All state variables declared before usage
- [ ] useEffect dependencies are accessible
- [ ] No duplicate state declarations
- [ ] Hooks called in consistent order
- [ ] No temporal dead zone issues

## Commands

```bash
# Test the fix
npm run test:biodata:init

# Test full religion integration
npm run test:religion:store

# Manual testing
npm run dev
# Visit: http://localhost:3000/purchase
```

## Related Documentation

- [Religion Store Integration](./RELIGION_STORE_INTEGRATION.md)
- [Religion Input Fix](./RELIGION_INPUT_FIX.md)
- [Component Structure](./COMPONENT_STRUCTURE.md)

This fix ensures that BioDataStep initializes correctly without reference errors while maintaining all the religion store integration functionality.
