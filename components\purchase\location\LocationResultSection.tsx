import { RefObject } from "react";
import { Button } from "@/components/ui/button";
import { MapPin } from "lucide-react";
import { LocationSearchResult } from "@/types/location";
import GoogleMapSection from "./GoogleMapSection";
import { GoogleMapsHelpers } from "@/lib/utils/googleMapsHelpers";

interface LocationResultSectionProps {
  result: LocationSearchResult;
  mapRef: RefObject<HTMLDivElement>;
  onSelectLocation: () => void;
  initialData?: LocationSearchResult;
}

export default function LocationResultSection({ 
  result, 
  mapRef, 
  onSelectLocation,
  initialData 
}: LocationResultSectionProps) {
  return (
    <div className="mt-6 p-4 bg-memorial-800 rounded-lg border border-memorial-700">
      <h3 className="font-medium text-memorial-50 mb-2">Hasil Pencarian:</h3>
      
      {/* Show notice if location is already loaded */}
      {initialData && initialData.lokasi && (
        <div className="mb-4 p-3 bg-green-900/30 border border-green-800 rounded-lg">
          <p className="text-sm text-green-200">
            ✅ <strong>Lokasi sudah dipilih sebelumnya.</strong> Anda dapat mengubah koordinat dengan drag marker atau cari lokasi baru.
          </p>
        </div>
      )}
      
      <div className="flex items-start gap-2 mb-3">
        <MapPin className="h-5 w-5 text-candle-500 mt-0.5 flex-shrink-0" />
        <div>
          {result.formatted_address && (
            <p className="text-sm text-memorial-400">{result.formatted_address}</p>
          )}
        </div>
      </div>
      
      {/* Google Map */}
      <GoogleMapSection mapRef={mapRef} />
      
      <div className="mt-4 flex justify-between items-center">
        {result.place_id && (
          <a 
            href={GoogleMapsHelpers.generateGoogleMapsUrl(result.place_id)}
            target="_blank"
            rel="noopener noreferrer"
            className="text-sm text-candle-500 hover:text-candle-400 underline"
          >
            Lihat di Google Maps
          </a>
        )}
        <Button
          onClick={onSelectLocation}
          className="bg-candle-500 text-memorial-950 hover:bg-candle-400"
        >
          Pilih Lokasi
        </Button>
      </div>
    </div>
  );
}
