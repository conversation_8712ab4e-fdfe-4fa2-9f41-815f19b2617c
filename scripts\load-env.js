#!/usr/bin/env node

/**
 * Script to ensure proper environment loading for production
 * This script loads the correct .env file based on NODE_ENV
 */

const { config } = require('dotenv');
const { resolve } = require('path');

function loadEnvironment() {
  const nodeEnv = process.env.NODE_ENV || 'development';
  console.log(`🔧 Loading environment for NODE_ENV: ${nodeEnv}`);

  // Determine which env file to load
  let envFile = '.env.development';
  if (nodeEnv === 'production') {
    envFile = '.env.production';
  } else if (nodeEnv === 'development') {
    envFile = '.env.development';
  }

  console.log(`📁 Loading environment from: ${envFile}`);

  // Load environment variables
  const envPath = resolve(process.cwd(), envFile);
  const result = config({ path: envPath });

  if (result.error) {
    console.error(`❌ Could not load ${envFile}:`, result.error.message);
    console.log('   Falling back to system environment variables');
  } else {
    console.log(`✅ Successfully loaded ${envFile}`);
  }

  // Validate critical environment variables
  console.log('\n🔍 Environment Variables Status:');
  
  // Database
  const dbUrl = process.env.DATABASE_URL;
  if (dbUrl) {
    // Mask password for security
    const maskedUrl = dbUrl.replace(/:([^:@]+)@/, ':***@');
    console.log(`  ✅ DATABASE_URL: ${maskedUrl}`);
  } else {
    console.log('  ❌ DATABASE_URL: Not set');
  }

  // Supabase
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (supabaseUrl) {
    console.log(`  ✅ NEXT_PUBLIC_SUPABASE_URL: ${supabaseUrl}`);
  } else {
    console.log('  ⚠️  NEXT_PUBLIC_SUPABASE_URL: Not set');
  }

  if (supabaseKey) {
    const maskedKey = supabaseKey.substring(0, 12) + '***' + supabaseKey.substring(supabaseKey.length - 4);
    console.log(`  ✅ NEXT_PUBLIC_SUPABASE_ANON_KEY: ${maskedKey}`);
  } else {
    console.log('  ⚠️  NEXT_PUBLIC_SUPABASE_ANON_KEY: Not set');
  }

  if (serviceRoleKey) {
    const maskedKey = serviceRoleKey.substring(0, 12) + '***' + serviceRoleKey.substring(serviceRoleKey.length - 4);
    console.log(`  ✅ SUPABASE_SERVICE_ROLE_KEY: ${maskedKey}`);
  } else {
    console.log('  ⚠️  SUPABASE_SERVICE_ROLE_KEY: Not set');
  }

  // Google Maps
  const mapsKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
  if (mapsKey) {
    const maskedKey = mapsKey.substring(0, 8) + '***' + mapsKey.substring(mapsKey.length - 4);
    console.log(`  ✅ NEXT_PUBLIC_GOOGLE_MAPS_API_KEY: ${maskedKey}`);
  } else {
    console.log('  ❌ NEXT_PUBLIC_GOOGLE_MAPS_API_KEY: Not set');
  }

  console.log('\n✨ Environment loading completed!');
  
  return {
    nodeEnv,
    envFile,
    hasDatabase: !!dbUrl,
    hasSupabase: !!(supabaseUrl && supabaseKey),
    hasServiceRole: !!serviceRoleKey,
    hasMaps: !!mapsKey
  };
}

// Export for use in other scripts
module.exports = { loadEnvironment };

// Run if called directly
if (require.main === module) {
  loadEnvironment();
}
