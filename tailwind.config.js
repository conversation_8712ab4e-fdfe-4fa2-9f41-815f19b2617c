/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    "./app/**/*.{js,ts,jsx,tsx}",
    "./pages/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)'
      },
      colors: {
        // Core cemetery theme colors - dark to light
        memorial: {
          950: '#121212', // Nearly black - night sky
          900: '#1a1a1a', // Deep charcoal - shadows
          800: '#262626', // Dark granite
          700: '#333333', // Stone gray
          600: '#404040', // Slate
          500: '#595959', // Medium gray stone
          400: '#737373', // Weathered stone
          300: '#8c8c8c', // Light stone
          200: '#a6a6a6', // Marble gray
          100: '#d9d9d9', // Light marble
          50: '#f2f2f2',  // Almost white - morning mist
        },
        // Accent colors for highlights and special elements
        candle: {
          500: '#e6b800', // Candle flame gold
          400: '#ffcc00', // Bright candle light
          300: '#ffe066', // Soft candle glow
        },
        nature: {
          900: '#1e3a2b', // Deep forest green
          800: '#2d5c42', // Cemetery cypress
          700: '#3a7d57', // Evergreen
          600: '#4d9e6f', // Moss
          500: '#6ab04c', // Grass
          400: '#badc58', // Spring leaves
        },
        sky: {
          900: '#1e3a5f', // Dusk sky
          800: '#2d5c8f', // Evening blue
          700: '#3a7dbd', // Twilight
          600: '#4d9eeb', // Morning sky
          500: '#70a1ff', // Clear day
          400: '#c3e0ff', // Pale sky
        },
        // Keep the existing shadcn colors for UI components
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))'
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))'
        },
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))'
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))'
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))'
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))'
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))'
        },
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        chart: {
          '1': 'hsl(var(--chart-1))',
          '2': 'hsl(var(--chart-2))',
          '3': 'hsl(var(--chart-3))',
          '4': 'hsl(var(--chart-4))',
          '5': 'hsl(var(--chart-5))'
        }
      }
    }
  },
  plugins: [require("tailwindcss-animate")],
};

