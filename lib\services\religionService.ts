import { Religion } from '@/types';

class ReligionService {
  private cache: Religion[] | null = null;

  // Data agama sesuai dengan database schema (fallback static data)
  private getStaticReligions(): Religion[] {
    return [
      { id: "550e8400-e29b-41d4-a716-446655440001", slug: "islam", name: "Islam" },
      { id: "550e8400-e29b-41d4-a716-446655440002", slug: "kristen", name: "<PERSON>" },
      { id: "550e8400-e29b-41d4-a716-446655440003", slug: "kato<PERSON>", name: "<PERSON><PERSON><PERSON>" },
      { id: "550e8400-e29b-41d4-a716-446655440004", slug: "hindu", name: "Hindu" },
      { id: "550e8400-e29b-41d4-a716-446655440005", slug: "buddha", name: "<PERSON>" },
      { id: "550e8400-e29b-41d4-a716-446655440006", slug: "k<PERSON><PERSON><PERSON>", name: "<PERSON><PERSON><PERSON>" },
    ];
  }

  async getReligions(): Promise<Religion[]> {
    // Return cached data if available
    if (this.cache !== null) {
      return this.cache;
    }

    try {
      // Try to fetch from API first
      const response = await fetch('/api/religions');
      if (response.ok) {
        const data = await response.json();
        if (data.success && Array.isArray(data.data)) {
          this.cache = data.data;
          return this.cache!; // We just set it, so it's not null
        }
      }
    } catch (error) {
      console.warn('Failed to fetch religions from API, using static data:', error);
    }

    // Fallback to static data
    this.cache = this.getStaticReligions();
    return this.cache;
  }

  async getReligionById(id: string): Promise<Religion | null> {
    const religions = await this.getReligions();
    return religions.find(r => r.id === id) || null;
  }

  async getReligionByName(name: string): Promise<Religion | null> {
    const religions = await this.getReligions();
    return religions.find(r => r.name.toLowerCase() === name.toLowerCase()) || null;
  }

  // Clear cache to force refresh
  clearCache(): void {
    this.cache = null;
  }
}

// Export singleton instance
export const religionService = new ReligionService();
export default religionService;
