import { useState, useEffect } from 'react';
import { <PERSON><PERSON>r<PERSON>and<PERSON> } from '@/lib/errorHandler';
import regionService, { District, Province, Regency, Village } from '@/lib/services/regionService';

export function useProvinces() {
  const [provinces, setProvinces] = useState<Province[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadProvinces = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const data = await regionService.getProvinces();
        setProvinces(data);
      } catch (err) {
        const errorMessage = 'Gagal memuat data provinsi';
        setError(errorMessage);
        ErrorHandler.handle(err, 'Load Provinces');
      } finally {
        setLoading(false);
      }
    };

    loadProvinces();
  }, []);

  return { provinces, loading, error };
}

export function useRegencies(provinceId: string) {
  const [regencies, setRegencies] = useState<Regency[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!provinceId) {
      setRegencies([]);
      return;
    }

    const loadRegencies = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const data = await regionService.getRegencies(provinceId);
        setRegencies(data);
      } catch (err) {
        const errorMessage = 'Gagal memuat data kabupaten/kota';
        setError(errorMessage);
        ErrorHandler.handle(err, 'Load Regencies');
      } finally {
        setLoading(false);
      }
    };

    loadRegencies();
  }, [provinceId]);

  return { regencies, loading, error };
}

export function useDistricts(regencyId: string) {
  const [districts, setDistricts] = useState<District[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!regencyId) {
      setDistricts([]);
      return;
    }

    const loadDistricts = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const data = await regionService.getDistricts(regencyId);
        setDistricts(data);
      } catch (err) {
        const errorMessage = 'Gagal memuat data kecamatan';
        setError(errorMessage);
        ErrorHandler.handle(err, 'Load Districts');
      } finally {
        setLoading(false);
      }
    };

    loadDistricts();
  }, [regencyId]);

  return { districts, loading, error };
}

export function useVillages(districtId: string) {
  const [villages, setVillages] = useState<Village[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!districtId) {
      setVillages([]);
      return;
    }

    const loadVillages = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const data = await regionService.getVillages(districtId);
        setVillages(data);
      } catch (err) {
        const errorMessage = 'Gagal memuat data kelurahan';
        setError(errorMessage);
        ErrorHandler.handle(err, 'Load Villages');
      } finally {
        setLoading(false);
      }
    };

    loadVillages();
  }, [districtId]);

  return { villages, loading, error };
}

// Hook untuk membangun alamat lengkap
export function useCompleteAddress() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const buildAddress = async (regionIds: {
    provinceId: string;
    regencyId: string;
    districtId: string;
    villageId: string;
    detailAddress?: string;
  }) => {
    setLoading(true);
    setError(null);

    try {
      const address = await regionService.buildCompleteAddress(regionIds);
      return address;
    } catch (err) {
      const errorMessage = 'Gagal membangun alamat lengkap';
      setError(errorMessage);
      ErrorHandler.handle(err, 'Build Complete Address');
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { buildAddress, loading, error };
}