import { memorialRepository } from '../db/repositories';
import type { Memorial, NewMemorial } from '../db/types';
import { ApiError } from '@/utils/ApiError';

export const memorialService = {
  getAllMemorials: async (): Promise<Memorial[]> => {
    try {
      return await memorialRepository.findAll();
    } catch (error) {
      console.error('Error in getAllMemorials:', error);
      throw new ApiError('Failed to fetch memorials', 500, 'MEMORIAL_FETCH_ERROR');
    }
  },

  getMemorialById: async (id: string): Promise<Memorial | null> => {
    try {
      return await memorialRepository.findById(id);
    } catch (error) {
      console.error(`Error in getMemorialById(${id}):`, error);
      throw new ApiError('Failed to fetch memorial', 500, 'MEMORIAL_FETCH_ERROR');
    }
  },
  
  createMemorial: async (data: NewMemorial): Promise<Memorial> => {
    try {
      // Validasi data
      if (!data.name || !data.birthDate || !data.deathDate) {
        throw new ApiError('Name, birth date, and death date are required', 400, 'VALIDATION_ERROR');
      }

      return await memorialRepository.create(data);
    } catch (error) {
      console.error('Error in createMemorial:', error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError('Failed to create memorial', 500, 'MEMORIAL_CREATE_ERROR');
    }
  },

  updateMemorial: async (id: string, data: Partial<NewMemorial>): Promise<Memorial | null> => {
    try {
      const memorial = await memorialRepository.findById(id);

      if (!memorial) {
        return null;
      }

      return await memorialRepository.update(id, data);
    } catch (error) {
      console.error(`Error in updateMemorial(${id}):`, error);
      throw new ApiError('Failed to update memorial', 500, 'MEMORIAL_UPDATE_ERROR');
    }
  },

  deleteMemorial: async (id: string): Promise<boolean> => {
    try {
      const memorial = await memorialRepository.findById(id);

      if (!memorial) {
        return false;
      }

      await memorialRepository.delete(id);
      return true;
    } catch (error) {
      console.error(`Error in deleteMemorial(${id}):`, error);
      throw new ApiError('Failed to delete memorial', 500, 'MEMORIAL_DELETE_ERROR');
    }
  }
};

