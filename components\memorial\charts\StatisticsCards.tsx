interface StatisticsCardsProps {
  recentMemorials: number;
  averageAge: number;
  paidPackages: number;
  conversionRate: number;
  currentYear: number;
}

export default function StatisticsCards({ 
  recentMemorials, 
  averageAge, 
  paidPackages, 
  conversionRate, 
  currentYear 
}: StatisticsCardsProps) {
  return (
    <div className="mt-6 grid grid-cols-2 lg:grid-cols-4 gap-4">
      <div className="bg-gradient-to-br from-blue-800 to-blue-900 rounded-lg p-5 text-center border-2 border-blue-600 shadow-xl">
        <div className="text-3xl font-bold text-white mb-2">{recentMemorials}</div>
        <div className="text-blue-50 text-sm font-medium">Memorial {currentYear}</div>
      </div>

      <div className="bg-gradient-to-br from-emerald-800 to-emerald-900 rounded-lg p-5 text-center border-2 border-emerald-600 shadow-xl">
        <div className="text-3xl font-bold text-white mb-2">{averageAge}</div>
        <div className="text-emerald-50 text-sm font-medium">Rata-rata <PERSON></div>
      </div>

      <div className="bg-gradient-to-br from-purple-800 to-purple-900 rounded-lg p-5 text-center border-2 border-purple-600 shadow-xl">
        <div className="text-3xl font-bold text-white mb-2">{paidPackages}</div>
        <div className="text-purple-50 text-sm font-medium">Paket Berbayar</div>
      </div>

      <div className="bg-gradient-to-br from-amber-800 to-amber-900 rounded-lg p-5 text-center border-2 border-amber-600 shadow-xl">
        <div className="text-3xl font-bold text-white mb-2">{conversionRate.toFixed(1)}%</div>
        <div className="text-amber-50 text-sm font-medium">Konversi Premium</div>
      </div>
    </div>
  );
}
