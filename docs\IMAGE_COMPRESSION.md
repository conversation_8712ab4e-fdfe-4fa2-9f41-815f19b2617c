# Image Compression System

## Overview

Sistem kompresi gambar otomatis menggunakan Sharp untuk mengurangi ukuran file gambar tanpa mengorbankan kualitas visual yang signifikan. Sistem ini menghemat storage dan bandwidth dengan mengurangi ukuran file hingga 70-80%.

## Features

### 🚀 **Automatic Compression**
- **Smart Compression**: Otomatis memilih pengaturan optimal berdasarkan ukuran dan jenis file
- **Progressive Compression**: Mengurangi kualitas secara bertahap hingga mencapai target ukuran
- **Format Optimization**: Konversi ke format yang lebih efisien (WebP, JPEG)
- **Size Targeting**: Target ukuran 500KB untuk upload optimal

### 📊 **Compression Statistics**
- **Compression Ratio**: Persentase pengurangan ukuran file
- **Size Saved**: Total ukuran yang dihemat
- **Processing Time**: Waktu yang dibutuhkan untuk kompresi
- **Before/After Preview**: Perbandingan visual gambar asli vs terkompresi

### 🎯 **Intelligent Settings**
- **Files > 3MB**: Kompresi agresif (70% quality, WebP format, 1600x1200 max)
- **Files > 1MB**: Kompresi moderat (80% quality, JPEG format, 1920x1080 max)
- **Files < 1MB**: Kompresi ringan (85% quality, format asli, 2048x1536 max)

## Implementation

### 1. **Server-Side Compression**

```typescript
// lib/services/imageCompressionService.ts
import { ImageCompressionService } from '@/lib/services/imageCompressionService';

// Compress single image
const result = await ImageCompressionService.processImage(buffer);

// Smart compression (automatic settings)
const smartResult = await ImageCompressionService.smartCompress(buffer);

// Batch compression
const results = await ImageCompressionService.processMultipleImages(buffers);
```

### 2. **Client-Side Hook**

```typescript
// hooks/useImageCompression.ts
import { useImageCompression } from '@/hooks/useImageCompression';

function MyComponent() {
  const { compressImages, loading, error, statistics } = useImageCompression();
  
  const handleCompress = async (files: File[]) => {
    try {
      const compressed = await compressImages(files);
      console.log('Compressed images:', compressed);
    } catch (err) {
      console.error('Compression failed:', err);
    }
  };
}
```

### 3. **API Endpoint**

```typescript
// app/api/compress-image/route.ts
POST /api/compress-image
{
  "images": ["data:image/jpeg;base64,...", "data:image/png;base64,..."],
  "options": {
    "quality": 80,
    "maxWidth": 1920,
    "maxHeight": 1080,
    "format": "jpeg"
  }
}
```

### 4. **UI Component**

```tsx
// components/ui/ImageCompressionPreview.tsx
import ImageCompressionPreview from '@/components/ui/ImageCompressionPreview';

<ImageCompressionPreview
  files={selectedFiles}
  onCompressionComplete={(compressed) => {
    console.log('Compression completed:', compressed);
  }}
  onError={(error) => {
    console.error('Compression error:', error);
  }}
/>
```

## Configuration

### Environment Variables

```bash
# Optional: Enable/disable compression
ENABLE_IMAGE_COMPRESSION=true

# Optional: Compression quality (60-95)
DEFAULT_COMPRESSION_QUALITY=80

# Optional: Target file size in bytes
COMPRESSION_TARGET_SIZE=512000
```

### Compression Settings

```typescript
// lib/config/index.ts
export const COMPRESSION_CONFIG = {
  maxFileSize: 5 * 1024 * 1024, // 5MB
  targetSize: 500 * 1024, // 500KB
  minQuality: 60,
  maxQuality: 95,
  formats: ['jpeg', 'png', 'webp'],
  presets: {
    thumbnail: { quality: 75, maxWidth: 400, maxHeight: 400 },
    web: { quality: 80, maxWidth: 1920, maxHeight: 1080 },
    archive: { quality: 90, maxWidth: 2560, maxHeight: 1920 }
  }
};
```

## Usage Examples

### Basic Compression

```typescript
import { useImageCompression } from '@/hooks/useImageCompression';

function ImageUpload() {
  const { compressImages, loading } = useImageCompression();
  
  const handleFileSelect = async (files: File[]) => {
    const compressed = await compressImages(files);
    // Upload compressed images
    uploadToServer(compressed);
  };
}
```

### With Preview

```typescript
import { useImageCompressionWithPreview } from '@/hooks/useImageCompression';

function ImagePreview() {
  const { compressWithPreview, previews, statistics } = useImageCompressionWithPreview();
  
  const handleCompress = async (files: File[]) => {
    await compressWithPreview(files);
    // previews now contains before/after images
    // statistics contains compression metrics
  };
}
```

### Auto Compression on Upload

```typescript
import { useAutoImageCompression } from '@/hooks/useImageCompression';

function AutoUpload() {
  const { handleFileUpload, loading } = useAutoImageCompression();
  
  const onFileSelect = (files: File[]) => {
    handleFileUpload(
      files,
      (compressed) => {
        // Success: upload compressed images
        uploadToServer(compressed);
      },
      (error) => {
        // Error handling
        console.error(error);
      }
    );
  };
}
```

## Performance Benefits

### Storage Savings
- **Original**: 5MB image
- **Compressed**: 500KB-1MB (70-90% reduction)
- **Quality**: Visually identical for web use

### Bandwidth Savings
- **Upload Speed**: 5-10x faster
- **Download Speed**: 5-10x faster
- **Mobile Friendly**: Reduced data usage

### User Experience
- **Faster Uploads**: Less waiting time
- **Better Performance**: Reduced server load
- **Cost Effective**: Lower storage costs

## Technical Details

### Sharp Configuration

```typescript
// Optimal Sharp settings
const sharpPipeline = sharp(buffer)
  .resize(maxWidth, maxHeight, {
    fit: 'inside',
    withoutEnlargement: true
  })
  .jpeg({
    quality: 80,
    progressive: true,
    mozjpeg: true // Better compression
  });
```

### Format Selection Logic

1. **WebP**: Best compression, modern browsers
2. **JPEG**: Good compression, universal support
3. **PNG**: Lossless, for images with transparency

### Quality Optimization

- **High Quality (90-95%)**: Archive/professional use
- **Medium Quality (80-85%)**: Web display
- **Low Quality (60-75%)**: Thumbnails/previews

## Monitoring

### Compression Metrics

```typescript
interface CompressionStatistics {
  totalImages: number;
  totalOriginalSize: number;
  totalCompressedSize: number;
  totalSizeSaved: number;
  averageCompressionRatio: number;
  processingTime: number;
}
```

### Logging

```typescript
console.log(`✅ Compression completed:
  - Original: ${formatFileSize(originalSize)}
  - Compressed: ${formatFileSize(compressedSize)}
  - Saved: ${formatFileSize(sizeSaved)} (${ratio.toFixed(1)}%)
  - Time: ${processingTime}ms`);
```

## Best Practices

1. **Always compress images > 500KB**
2. **Use WebP for modern browsers**
3. **Maintain aspect ratios**
4. **Progressive JPEG for better loading**
5. **Batch process multiple images**
6. **Show compression statistics to users**
7. **Provide fallback for compression failures**

## Troubleshooting

### Common Issues

1. **Sharp not installed**: `npm install sharp`
2. **Memory issues**: Process images in batches
3. **Format not supported**: Check MIME types
4. **Quality too low**: Adjust minimum quality settings

### Error Handling

```typescript
try {
  const compressed = await compressImages(files);
} catch (error) {
  if (error.code === 'FILE_TOO_LARGE') {
    // Handle large files
  } else if (error.code === 'COMPRESSION_ERROR') {
    // Handle compression failures
  }
}
```

## Integration with Existing System

The compression system integrates seamlessly with:

- **ImageStorageService**: Automatic compression before upload
- **BioData Forms**: Compress memorial and evidence images
- **Image Upload Components**: Built-in compression preview
- **API Routes**: Server-side compression endpoints

This system ensures optimal performance while maintaining image quality for the memorial application.
