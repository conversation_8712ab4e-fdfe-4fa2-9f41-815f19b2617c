import { RefObject } from "react";

interface GoogleMapSectionProps {
  mapRef: RefObject<HTMLDivElement>;
  className?: string;
}

export default function GoogleMapSection({ 
  mapRef, 
  className = "w-full h-64 rounded-lg mb-3 bg-memorial-700" 
}: GoogleMapSectionProps) {
  return (
    <div className="space-y-2">
      <p className="text-center text-sm text-memorial-300">
        Drag <span className="font-bold text-candle-500">marker</span> untuk mengubah koordinat
      </p>
      
      {/* Google Map Container */}
      <div 
        ref={mapRef} 
        className={className}
      ></div>
    </div>
  );
}
