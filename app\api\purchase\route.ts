import { NextRequest, NextResponse } from 'next/server';
import { handleApiError } from '@/utils/errorHandler';
import { ApiError } from '@/utils/ApiError';
import { validatePurchaseRequest } from '@/validations/purchaseSchema';
import { purchaseService } from '@/lib/services/purchaseService';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Debug: Log received data
    console.log('Received purchase data:', {
      birthDate: body.birthDate,
      deathDate: body.deathDate,
      name: body.name
    });

    // Validate request data
    const validatedData = validatePurchaseRequest(body);

    // Validate package
    if (!purchaseService.validatePackage(validatedData.packageDetails.id)) {
      throw new ApiError('Invalid package selected', 400, 'INVALID_PACKAGE');
    }

    // Process the purchase
    const result = await purchaseService.processPurchase(validatedData);

    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Memorial created successfully',
      data: result
    }, { status: 201 });

  } catch (error) {
    console.error('Purchase API Error:', error);
    return handleApiError(error);
  }
}

// GET endpoint to retrieve purchase status or memorial details
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const memorialId = searchParams.get('memorialId');

    if (!memorialId) {
      throw new ApiError('Memorial ID is required', 400, 'VALIDATION_ERROR');
    }

    // Get memorial details using service
    const memorial = await purchaseService.getMemorialById(memorialId);

    return NextResponse.json({
      success: true,
      data: memorial
    });

  } catch (error) {
    return handleApiError(error);
  }
}
