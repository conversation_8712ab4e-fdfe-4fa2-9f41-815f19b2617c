"use client";
import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import {
  Landmark,
  BookOpen,
  ShoppingBag,
  Menu,
  X,
  DoorClosed,
  Search,
  Settings
} from "lucide-react";
import Link from "next/link";
import NavItem from "@/components/layouts/NavItem";
import Logo from "@/components/layouts/Logo";
import { Button } from "@/components/ui/button";
import { useReligionStore } from "@/store/useReligionStore";

interface NavbarProps {
  onOpenSearch: () => void;
  onOpenReligionSelection?: () => void;
}

export default function Navbar({ onOpenSearch, onOpenReligionSelection }: NavbarProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { selectedReligion } = useReligionStore();
  const pathname = usePathname();

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMenuOpen(false);
  }, [pathname]);

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (isMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isMenuOpen]);

  // Handler to close mobile menu
  const closeMobileMenu = () => {
    setIsMenuOpen(false);
  };


  
  return (
    <nav className="fixed top-0 left-0 right-0 bg-memorial-900/95 backdrop-blur-md border-b border-memorial-700 z-50">
      <div className="container mx-auto px-4 md:px-6 lg:px-8 py-4 md:py-5 lg:py-6 flex items-center justify-between">
        {/* Logo */}
        <Link href="/" className="flex items-center gap-3 md:gap-4 text-memorial-50 hover:text-candle-300 transition-all duration-200 group">
          <span className="font-bold text-lg md:text-xl lg:text-3xl tracking-tight">Pemakaman Digital</span>
        </Link>

        {/* Mobile/Tablet menu button - show on screens smaller than lg */}
        <button
          className="lg:hidden p-2 rounded-lg text-memorial-50 hover:bg-memorial-800 transition-colors"
          onClick={() => setIsMenuOpen(!isMenuOpen)}
          aria-label={isMenuOpen ? "Tutup menu" : "Buka menu"}
        >
          {isMenuOpen ? <X className="h-5 w-5 md:h-6 md:w-6" /> : <Menu className="h-5 w-5 md:h-6 md:w-6" />}
        </button>
        
        {/* Desktop/Tablet menu with improved layout */}
        <div className="hidden md:flex items-center gap-4 md:gap-6 lg:gap-8 xl:gap-10">
          {/* Navigation links */}
          <div className="flex items-center gap-4 md:gap-6 lg:gap-8 xl:gap-10">
            <NavItem icon={<DoorClosed className="h-4 w-4 lg:h-5 lg:w-5" />} label="Gerbang" href="/" />
            <NavItem icon={<Landmark className="h-4 w-4 lg:h-5 lg:w-5" />} label="Pemakaman" href="/memorial" />
            <NavItem icon={<BookOpen className="h-4 w-4 lg:h-5 lg:w-5" />} label="Tentang Kami" href="/tentang-kami" />
          </div>

          {/* Divider */}
          <div className="h-6 w-px bg-memorial-700/50"></div>

          {/* Action buttons */}
          <div className="flex items-center gap-2 md:gap-3 lg:gap-4">
            {/* Religion selection button */}
            {onOpenReligionSelection && (
              <Button
                onClick={onOpenReligionSelection}
                className="bg-memorial-800/90 hover:bg-memorial-700 text-memorial-50 border border-memorial-700/50 h-9 md:h-10 px-3 md:px-4 text-xs md:text-sm transition-all duration-200"
                variant="outline"
                title="Pilih Agama"
              >
                <Settings className="h-3.5 w-3.5 md:h-4 md:w-4 mr-1.5 md:mr-2" />
                <span className="text-candle-400 font-medium hidden lg:inline">{selectedReligion || "Pilih Agama"}</span>
                <span className="text-candle-400 font-medium lg:hidden">Agama</span>
              </Button>
            )}

            {/* Purchase button - highlighted */}
            <Link href="/purchase">
              <Button
                className="bg-gradient-to-r from-candle-500 to-candle-400 text-memorial-950 hover:from-candle-400 hover:to-candle-300 h-9 md:h-10 px-3 md:px-4 lg:px-5 font-semibold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 text-xs md:text-sm"
                title="Beli Memorial Digital"
              >
                <ShoppingBag className="h-3.5 w-3.5 md:h-4 md:w-4 mr-1.5 md:mr-2" />
                <span className="hidden lg:inline">Beli Memorial</span>
                <span className="lg:hidden">Beli</span>
              </Button>
            </Link>
          </div>
        </div>
      </div>
      
      {/* Mobile menu dropdown */}
      {isMenuOpen && (
        <>
          {/* Overlay */}
          <div
            className="fixed inset-0 z-40 md:hidden"
            onClick={closeMobileMenu}
          />

          {/* Menu content */}
          <div className="absolute top-full left-0 right-0 z-50 lg:hidden bg-memorial-900 backdrop-blur-md border-b border-memorial-700 shadow-2xl animate-in slide-in-from-top-2 duration-200">
            <div className="container mx-auto px-4 md:px-6 py-4 md:py-6 flex flex-col gap-1">
              {/* Religion selection for mobile/tablet */}
              {onOpenReligionSelection && (
                <button
                  onClick={() => {
                    onOpenReligionSelection();
                    closeMobileMenu();
                  }}
                  className="flex items-center gap-3 p-3 md:p-4 rounded-lg text-memorial-300 hover:text-candle-300 hover:bg-memorial-800 transition-colors"
                >
                  <Settings className="h-4 w-4 md:h-5 md:w-5" />
                  <span className="text-sm md:text-base font-medium">{selectedReligion || "Pilih Agama"}</span>
                </button>
              )}

              {/* Divider */}
              <div className="h-px bg-memorial-700 my-2 md:my-3"></div>

              <NavItem
                icon={<DoorClosed className="h-4 w-4 md:h-5 md:w-5" />}
                label="Gerbang"
                href="/"
                onClick={closeMobileMenu}
              />
              <NavItem
                icon={<Landmark className="h-4 w-4 md:h-5 md:w-5" />}
                label="Pemakaman"
                href="/memorial"
                onClick={closeMobileMenu}
              />
              <NavItem
                icon={<BookOpen className="h-4 w-4 md:h-5 md:w-5" />}
                label="Tentang Kami"
                href="/tentang-kami"
                onClick={closeMobileMenu}
              />
              {/* Divider */}
              <div className="h-px bg-memorial-700 my-2 md:my-3"></div>

              {/* Purchase button for mobile/tablet */}
              <div className="mt-2 md:mt-4">
                <Link href="/purchase" className="w-full" onClick={closeMobileMenu}>
                  <Button
                    className="flex items-center justify-center gap-3 w-full bg-candle-500 text-memorial-950 hover:bg-candle-400 h-12 md:h-14 font-medium shadow-lg text-sm md:text-base"
                  >
                    <ShoppingBag className="h-5 w-5 md:h-6 md:w-6" />
                    <span>Beli Memorial Digital</span>
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </>
      )}
    </nav>
  );
}



