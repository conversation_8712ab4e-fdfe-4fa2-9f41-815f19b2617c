import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db';
import { packages } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { handleApiError } from '@/utils/errorHandler';
import { ApiError } from '@/utils/ApiError';

/**
 * GET /api/packages/[id]
 * Retrieve specific package by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const packageId = params.id;

    if (!packageId) {
      throw ApiError.badRequest('Package ID is required', 'MISSING_PACKAGE_ID');
    }

    console.log(`📦 Fetching package with ID: ${packageId}`);

    const packageData = await db
      .select()
      .from(packages)
      .where(eq(packages.id, packageId))
      .limit(1);

    if (packageData.length === 0) {
      throw ApiError.notFound('Package not found', 'PACKAGE_NOT_FOUND');
    }

    // Transform features from JSON string to array
    const transformedPackage = {
      ...packageData[0],
      features: JSON.parse(packageData[0].features)
    };

    console.log(`✅ Found package: ${transformedPackage.name}`);

    return NextResponse.json({
      success: true,
      data: transformedPackage
    });

  } catch (error) {
    console.error(`❌ Error fetching package ${params.id}:`, error);
    return handleApiError(error);
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
