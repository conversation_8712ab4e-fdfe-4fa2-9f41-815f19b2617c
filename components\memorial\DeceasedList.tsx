import { Deceased } from "@/data/deceased";
import DeceasedCard from "@/components/memorial/DeceasedCard";
import PackageStats from "./PackageStats";

interface DeceasedListProps {
  deceasedList: Deceased[];
  showStats?: boolean;
}



export default function DeceasedList({ deceasedList, showStats = false }: DeceasedListProps) {
  return (
    <div>
      {/* {showStats && <PackageStats deceasedList={deceasedList} />} */}
      <div className="space-y-8">
        {deceasedList.map((deceased) => (
          <DeceasedCard key={deceased.id} deceased={deceased} />
        ))}
      </div>
    </div>
  );
}