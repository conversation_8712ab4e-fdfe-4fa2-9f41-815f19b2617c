import { useState, useEffect } from "react";
import religionService from "@/lib/services/religionService";
import { withErrorHandling } from "@/lib/errorHandler";

interface Religion {
  id: string;
  slug: string;
  name: string;
}

export function useReligionData() {
  const [religions, setReligions] = useState<Religion[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fallback static data
  const fallbackReligions: Religion[] = [
    { id: "cebc1d27-dad8-4029-8406-645d940a0a2a", slug: "islam", name: "Islam" },
    { id: "0a0f945b-80df-4bcf-ae23-1f9903a3ed1c", slug: "kristen", name: "<PERSON>" },
    { id: "20610682-e6dd-498c-977b-883d2d35d368", slug: "kato<PERSON>", name: "<PERSON><PERSON><PERSON>" },
    { id: "d5145b6b-2b8f-4be6-8a89-60413b5969e1", slug: "hindu", name: "Hindu" },
    { id: "f9816867-fe50-412a-8f76-faa92d1e09b2", slug: "buddha", name: "Buddha" },
    { id: "ab875097-9eca-42d8-b6cb-9afcfb9d4f9c", slug: "konghucu", name: "Konghucu" },
  ];

  useEffect(() => {
    const loadReligions = async () => {
      try {
        setLoading(true);
        setError(null);

        const religionData = await withErrorHandling(
          () => religionService.getReligions(),
          'Load Religions'
        );

        if (religionData && religionData.length > 0) {
          setReligions(religionData);
        } else {
          // Use fallback data if service returns empty or null
          setReligions(fallbackReligions);
        }
      } catch (err) {
        console.error('Failed to load religions:', err);
        setError('Failed to load religions');
        // Use fallback data on error
        setReligions(fallbackReligions);
      } finally {
        setLoading(false);
      }
    };

    loadReligions();
  }, []);

  // Helper function to get religion by ID
  const getReligionById = (id: string | null): Religion | null => {
    if (!id) return null;
    return religions.find(r => r.id === id) || null;
  };

  // Helper function to get religion by name
  const getReligionByName = (name: string | null): Religion | null => {
    if (!name) return null;
    return religions.find(r => r.name === name) || null;
  };

  // Helper function to get religion by slug
  const getReligionBySlug = (slug: string | null): Religion | null => {
    if (!slug) return null;
    return religions.find(r => r.slug === slug) || null;
  };

  // Convert religions to select options format
  const religionOptions = religions.map(religion => ({
    value: religion.id,
    label: religion.name
  }));

  return {
    religions,
    religionOptions,
    loading,
    error,
    getReligionById,
    getReligionByName,
    getReligionBySlug
  };
}
