# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# database files
*.sql.backup
*.dump

# scripts logs
scripts/*.log

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# development files (tidak perlu di production)
# /scripts/ - keep scripts for production deployment
# /docs/ - keep docs for documentation

# database files
*.sql
*.db
*.sqlite
*.sqlite3
backup.sql
*.dump
*.backup

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
Thumbs.db
.DS_Store

# Test files (excluded from production)
__tests__/
tests/
/coverage/
*.lcov
jest.config.js
jest.config.ts
vitest.config.ts
vitest.config.js
*.test.js
*.test.ts
*.test.tsx
*.spec.js
*.spec.ts
*.spec.tsx
**/testing/
**/test-*
TEST_RESULTS.md

# Build artifacts
*.tsbuildinfo
.next/
out/
dist/
build/

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*
