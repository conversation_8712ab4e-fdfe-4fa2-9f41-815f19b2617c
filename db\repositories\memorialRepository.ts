import { db } from '../index';
import { memorials } from '../schema';
import { eq } from 'drizzle-orm';
import type { Memorial, NewMemorial } from '../types';

export const memorialRepository = {
  findAll: async (): Promise<Memorial[]> => {
    return await db.select().from(memorials);
  },
  
  findById: async (id: string): Promise<Memorial | null> => {
    const results = await db.select().from(memorials).where(eq(memorials.id, id));
    return results.length > 0 ? results[0] : null;
  },
  
  create: async (data: NewMemorial): Promise<Memorial> => {
    const result = await db.insert(memorials).values(data).returning();
    return result[0];
  },
  
  update: async (id: string, data: Partial<NewMemorial>): Promise<Memorial | null> => {
    const result = await db
      .update(memorials)
      .set({ ...data, updatedAt: new Date() })
      .where(eq(memorials.id, id))
      .returning();
    return result.length > 0 ? result[0] : null;
  },

  delete: async (id: string): Promise<void> => {
    await db.delete(memorials).where(eq(memorials.id, id));
  }
};
