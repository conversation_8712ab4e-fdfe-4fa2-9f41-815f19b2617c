"use client";
import { useState } from "react";
import Image from "next/image";
import { Home } from "lucide-react";

interface LogoProps {
  className?: string;
  size?: "sm" | "md" | "lg" | "xl";
}

export default function Logo({ className = "", size = "md" }: LogoProps) {
  const [imageError, setImageError] = useState(false);

  const sizeClasses = {
    sm: "h-5 w-5",
    md: "h-6 w-6 lg:h-8 lg:w-8",
    lg: "h-10 w-10 lg:h-12 lg:w-12",
    xl: "h-12 w-12 lg:h-16 lg:w-16"
  };

  const sizeValues = {
    sm: { width: 20, height: 20 },
    md: { width: 32, height: 32 },
    lg: { width: 48, height: 48 },
    xl: { width: 64, height: 64 }
  };

  if (imageError) {
    return (
      <Home 
        className={`${sizeClasses[size]} text-candle-500 group-hover:text-candle-400 transition-colors ${className}`} 
      />
    );
  }

  return (
    <Image 
      src="/images/logo.png" 
      alt="Pemakaman Digital Logo" 
      width={sizeValues[size].width}
      height={sizeValues[size].height}
      className={`${sizeClasses[size]} object-contain group-hover:scale-105 transition-transform duration-200 ${className}`}
      priority
      onError={() => setImageError(true)}
    />
  );
}
