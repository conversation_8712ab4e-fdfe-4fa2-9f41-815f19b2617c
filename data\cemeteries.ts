export interface Cemetery {
  id: number;
  name: string;
  location: string;
  religion: string;
  image: string;
  availableSpots: number;
}

// Sample cemetery data
export const cemeteries: Cemetery[] = [
  {
    id: 1,
    name: "TPU Jeruk Purut",
    location: "Jakarta Selatan",
    religion: "Islam",
    image: "https://placehold.co/600x400/nature/white?text=TPU+Jeruk+<PERSON>",
    availableSpots: 120,
  },
  {
    id: 2,
    name: "TPU Tanah Kusir",
    location: "Jakarta Selatan",
    religion: "Islam",
    image: "https://placehold.co/600x400/nature/white?text=TPU+Tanah+Kusir",
    availableSpots: 85,
  },
  {
    id: 3,
    name: "TPU Karet Bivak",
    location: "Jakarta Pusat",
    religion: "Islam",
    image: "https://placehold.co/600x400/nature/white?text=TPU+Karet+Bivak",
    availableSpots: 42,
  },
  {
    id: 4,
    name: "San Diego Hills",
    location: "Karawang",
    religion: "Semua Agama",
    image: "https://placehold.co/600x400/nature/white?text=San+Diego+Hills",
    availableSpots: 230,
  },
  {
    id: 5,
    name: "TPU Pondok Ranggon",
    location: "Jakarta Timur",
    religion: "Islam",
    image: "https://placehold.co/600x400/nature/white?text=TPU+Pondok+Ranggon",
    availableSpots: 67,
  },
  {
    id: 6,
    name: "TPU Menteng Pulo",
    location: "Jakarta Selatan",
    religion: "Islam",
    image: "https://placehold.co/600x400/nature/white?text=TPU+Menteng+Pulo",
    availableSpots: 53,
  },
];