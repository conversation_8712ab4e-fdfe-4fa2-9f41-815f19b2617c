import { ApiError } from '@/utils/ApiError';
import { locationRequestSchema } from '@/validations/locationSchema';

/**
 * Memvalidasi request lokasi menggunakan Zod schema
 * @throws ApiError jika validasi gagal
 */
export function validateLocationRequest(body: unknown) {
  const validationResult = locationRequestSchema.safeParse(body);
  
  if (!validationResult.success) {
    const errorMessages = validationResult.error.errors.map(err => 
      `${err.path.join('.')}: ${err.message}`
    ).join(', ');
    
    throw ApiError.badRequest(
      `Validation error: ${errorMessages}`, 
      'VALIDATION_ERROR'
    );
  }
  
  return validationResult.data;
}