import { useState, useEffect } from "react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  type CarouselApi,
} from "@/components/ui/carousel";
import { CarouselDots } from "@/components/ui/carousel-dots";
import CemeteryCard from "@/components/pemakaman/CemeteryCard";

interface Cemetery {
  id: number;
  name: string;
  location: string;
  religion: string;
  image: string;
  availableSpots: number;
}

interface CemeteriesCarouselProps {
  cemeteries: Cemetery[];
}

export default function CemeteriesCarousel({ cemeteries }: CemeteriesCarouselProps) {
  const [api, setApi] = useState<CarouselApi>();
  const [slidesPerView, setSlidesPerView] = useState(1);

  // Update slides per view based on screen size
  useEffect(() => {
    const handleResize = () => {
      setSlidesPerView(window.innerWidth >= 768 ? 3 : 1);
    };
    
    // Set initial value
    handleResize();
    
    // Add event listener
    window.addEventListener('resize', handleResize);
    
    // Clean up
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <>
      <Carousel 
        setApi={setApi}
        opts={{
          align: "start",
          loop: true,
          slidesToScroll: slidesPerView,
        }}
        className="w-full"
      >
        <CarouselContent>
          {cemeteries.map((cemetery) => (
            <CarouselItem 
              key={cemetery.id} 
              className={`${slidesPerView === 3 ? 'basis-1/3' : 'basis-full'} pl-4`}
            >
              <CemeteryCard cemetery={cemetery} />
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious 
          className="bg-memorial-800/80 hover:bg-memorial-700 border-none text-memorial-50"
        />
        <CarouselNext 
          className="bg-memorial-800/80 hover:bg-memorial-700 border-none text-memorial-50"
        />
      </Carousel>
      
      <CarouselDots 
        api={api} 
        count={Math.ceil(cemeteries.length / slidesPerView)} 
      />
    </>
  );
}