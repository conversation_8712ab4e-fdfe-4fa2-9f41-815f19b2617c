import { NextRequest, NextResponse } from 'next/server';
import { memorialService } from '@/services/memorialService';
import { handleApiError } from '@/utils/errorHandler';
import { validateMemorialId, validateUpdateMemorialRequest } from '@/validators/memorialValidator';
import { ApiError } from '@/utils/ApiError';

/**
 * GET handler untuk mendapatkan memorial berdasarkan ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Validasi ID
    const id = validateMemorialId(params);
    
    // Dapatkan memorial dari service
    const memorial = await memorialService.getMemorialById(id);
    
    if (!memorial) {
      throw new ApiError('Memorial not found', 404, 'MEMORIAL_NOT_FOUND');
    }
    
    return NextResponse.json({ success: true, data: memorial });
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * PUT handler untuk mengupdate memorial berdasarkan ID
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Validasi ID
    const id = validateMemorialId(params);
    
    // Parse dan validasi body request
    const body = await request.json();
    const validatedData = validateUpdateMemorialRequest(body);
    
    // Update memorial
    const updatedMemorial = await memorialService.updateMemorial(id, validatedData);
    
    if (!updatedMemorial) {
      throw new ApiError('Memorial not found', 404, 'MEMORIAL_NOT_FOUND');
    }
    
    return NextResponse.json({ success: true, data: updatedMemorial });
  } catch (error) {
    return handleApiError(error);
  }
}

/**
 * DELETE handler untuk menghapus memorial berdasarkan ID
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Validasi ID
    const id = validateMemorialId(params);
    
    // Hapus memorial
    const success = await memorialService.deleteMemorial(id);
    
    if (!success) {
      throw new ApiError('Memorial not found', 404, 'MEMORIAL_NOT_FOUND');
    }
    
    return NextResponse.json(
      { success: true, message: 'Memorial deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    return handleApiError(error);
  }
}
