"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Eye, X, Download, ZoomIn, ZoomOut } from "lucide-react";
import Image from "next/image";

interface EvidencePopupProps {
  evidenceImage: File | string | null;
  evidenceName: string;
  trigger?: React.ReactNode;
}

export default function EvidencePopup({ evidenceImage, evidenceName, trigger }: EvidencePopupProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [zoom, setZoom] = useState(1);

  if (!evidenceImage) return null;

  const getImageUrl = () => {
    if (typeof evidenceImage === 'string') {
      return evidenceImage;
    }
    return URL.createObjectURL(evidenceImage);
  };

  const handleDownload = () => {
    const url = getImageUrl();
    const link = document.createElement('a');
    link.href = url;
    link.download = evidenceName || 'evidence';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 0.25, 3));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 0.25, 0.5));
  };

  const resetZoom = () => {
    setZoom(1);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button
            type="button"
            variant="outline"
            size="sm"
            className="border-memorial-600 text-memorial-300 hover:bg-memorial-800"
          >
            <Eye className="h-4 w-4 mr-2" />
            Lihat Bukti
          </Button>
        )}
      </DialogTrigger>
      
      <DialogContent className="max-w-4xl max-h-[90vh] bg-memorial-900 border-memorial-700">
        <DialogHeader className="flex flex-row items-center justify-between">
          <DialogTitle className="text-memorial-50">
            {evidenceName || "Bukti Kematian"}
          </DialogTitle>
          
          {/* Controls */}
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleZoomOut}
              disabled={zoom <= 0.5}
              className="border-memorial-600 text-memorial-300 hover:bg-memorial-800"
            >
              <ZoomOut className="h-4 w-4" />
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={resetZoom}
              className="border-memorial-600 text-memorial-300 hover:bg-memorial-800 px-3"
            >
              {Math.round(zoom * 100)}%
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleZoomIn}
              disabled={zoom >= 3}
              className="border-memorial-600 text-memorial-300 hover:bg-memorial-800"
            >
              <ZoomIn className="h-4 w-4" />
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleDownload}
              className="border-memorial-600 text-memorial-300 hover:bg-memorial-800"
            >
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>
        
        {/* Image Container */}
        <div className="relative overflow-auto max-h-[70vh] bg-memorial-950 rounded-lg border border-memorial-800">
          <div 
            className="flex items-center justify-center min-h-[400px] p-4"
            style={{ transform: `scale(${zoom})`, transformOrigin: 'center' }}
          >
            <Image
              src={getImageUrl()}
              alt={evidenceName || "Bukti Kematian"}
              width={800}
              height={600}
              className="max-w-full h-auto rounded-lg shadow-lg"
              style={{ objectFit: 'contain' }}
            />
          </div>
        </div>
        
        {/* Info */}
        <div className="text-sm text-memorial-300 bg-memorial-800 p-3 rounded-lg">
          <p><strong>Nama File:</strong> {evidenceName}</p>
          {typeof evidenceImage !== 'string' && (
            <>
              <p><strong>Ukuran:</strong> {(evidenceImage.size / 1024 / 1024).toFixed(2)} MB</p>
              <p><strong>Tipe:</strong> {evidenceImage.type}</p>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
