import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/errorHandler";
import { BioData } from "@/components/purchase/BioDataStep";

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

export class BioDataValidators {
  static validateName(name: string): ValidationResult {
    if (!name.trim()) {
      return { isValid: false, error: "Nama lengkap harus diisi" };
    }
    return { isValid: true };
  }

  static validateBirthPlace(birthPlace: string): ValidationResult {
    if (!birthPlace.trim()) {
      return { isValid: false, error: "Tempat lahir harus diisi" };
    }
    return { isValid: true };
  }

  static validateBirthDate(birthDate: string): ValidationResult {
    if (!birthDate) {
      return { isValid: false, error: "Tanggal lahir harus diisi" };
    }

    // Validate birth date format
    if (!/^\d{4}-\d{2}-\d{2}$/.test(birthDate)) {
      return { isValid: false, error: "Format tanggal lahir tidak valid" };
    }

    // Validate birth date is not in the future
    const birthDateObj = new Date(birthDate);
    const today = new Date();
    today.setHours(23, 59, 59, 999); // End of today

    if (birthDateObj > today) {
      return { isValid: false, error: "Tanggal lahir tidak boleh lebih dari hari ini" };
    }

    // Validate birth date is not too old (before 1500)
    const minDate = new Date('1500-01-01');
    if (birthDateObj < minDate) {
      return { isValid: false, error: "Tanggal lahir tidak boleh sebelum tahun 1500" };
    }

    return { isValid: true };
  }

  static validateDeathDate(deathDate: string): ValidationResult {
    if (!deathDate) {
      return { isValid: false, error: "Tanggal wafat harus diisi" };
    }

    // Validate death date format
    if (!/^\d{4}-\d{2}-\d{2}$/.test(deathDate)) {
      return { isValid: false, error: "Format tanggal wafat tidak valid" };
    }

    // Validate death date is not in the future
    const deathDateObj = new Date(deathDate);
    const today = new Date();
    today.setHours(23, 59, 59, 999); // End of today

    if (deathDateObj > today) {
      return { isValid: false, error: "Tanggal meninggal tidak boleh lebih dari hari ini" };
    }

    // Validate death date is not too old (before 1500)
    const minDate = new Date('1500-01-01');
    if (deathDateObj < minDate) {
      return { isValid: false, error: "Tanggal meninggal tidak boleh sebelum tahun 1500" };
    }

    return { isValid: true };
  }

  static validateDateRange(birthDate: string, deathDate: string): ValidationResult {
    if (!birthDate || !deathDate) {
      return { isValid: true }; // Individual date validation will catch this
    }

    // Validate birth date is before death date
    if (new Date(birthDate) >= new Date(deathDate)) {
      return { isValid: false, error: "Tanggal lahir harus sebelum tanggal wafat" };
    }

    return { isValid: true };
  }

  static validateReligion(religionId: string | null): ValidationResult {
    if (!religionId || religionId === null) {
      return { isValid: false, error: "Agama harus dipilih" };
    }
    return { isValid: true };
  }

  static validateSubmittedBy(submittedBy: string): ValidationResult {
    if (!submittedBy.trim()) {
      return { isValid: false, error: "Nama pengaju harus diisi" };
    }
    return { isValid: true };
  }

  static validateDescription(description: string): ValidationResult {
    if (!description.trim()) {
      return { isValid: false, error: "Kisah hidup harus diisi" };
    }
    return { isValid: true };
  }

  static validateEvidenceName(evidenceName: string): ValidationResult {
    if (!evidenceName.trim()) {
      return { isValid: false, error: "Nama bukti kematian harus diisi" };
    }
    return { isValid: true };
  }

  static validateEvidenceImage(evidenceImage: File | null): ValidationResult {
    if (!evidenceImage) {
      return { isValid: false, error: "Foto bukti kematian wajib diupload" };
    }
    return { isValid: true };
  }

  static validateAll(bioData: BioData): ValidationResult {
    const validations = [
      this.validateName(bioData.name),
      this.validateBirthPlace(bioData.birthPlace),
      this.validateBirthDate(bioData.birthDate),
      this.validateDeathDate(bioData.deathDate),
      this.validateDateRange(bioData.birthDate, bioData.deathDate),
      this.validateReligion(bioData.religionId),
      this.validateSubmittedBy(bioData.submittedBy),
      this.validateDescription(bioData.description),
      this.validateEvidenceName(bioData.evidenceName),
      this.validateEvidenceImage(bioData.evidenceImage)
    ];

    for (const validation of validations) {
      if (!validation.isValid) {
        return validation;
      }
    }

    return { isValid: true };
  }

  static validateAndHandle(bioData: BioData): boolean {
    const result = this.validateAll(bioData);
    if (!result.isValid && result.error) {
      ErrorHandler.handle(result.error, "Form Validation");
      return false;
    }
    return true;
  }
}
