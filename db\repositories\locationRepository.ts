import { db } from '../index';
import { locations } from '../schema';
import { eq } from 'drizzle-orm';
import type { NewLocation, Location } from '../types';

export const locationRepository = {
  findAll: async (): Promise<Location[]> => {
    return await db.select().from(locations);
  },
  
  findById: async (id: string): Promise<Location | undefined> => {
    const results = await db.select().from(locations).where(eq(locations.id, id));
    return results[0];
  },
  
  create: async (data: NewLocation): Promise<Location> => {
    const result = await db.insert(locations).values(data).returning();
    return result[0];
  },
  
  update: async (id: string, data: Partial<NewLocation>): Promise<Location | undefined> => {
    const result = await db
      .update(locations)
      .set(data)
      .where(eq(locations.id, id))
      .returning();
    return result[0];
  },

  delete: async (id: string): Promise<boolean> => {
    const result = await db
      .delete(locations)
      .where(eq(locations.id, id))
      .returning({ id: locations.id });
    return result.length > 0;
  }
};