import { Button } from "@/components/ui/button";

interface LocationActionsProps {
  onReset?: () => void;
  onSubmit: (e: React.FormEvent) => void;
  loading?: boolean;
  showResetButton?: boolean;
  submitButtonText?: string;
  resetButtonText?: string;
}

export default function LocationActions({
  onReset,
  onSubmit,
  loading = false,
  showResetButton = true,
  submitButtonText = "Cari Koordinat",
  resetButtonText = "Reset Form"
}: LocationActionsProps) {
  return (
    <div className="flex flex-col sm:flex-row gap-3">
      {showResetButton && onReset && (
        <Button
          type="button"
          variant="outline"
          onClick={onReset}
          disabled={loading}
          className="w-full sm:w-auto border-memorial-600 text-memorial-300 hover:bg-memorial-800 hover:text-memorial-50"
        >
          {resetButtonText}
        </Button>
      )}
      <Button
        type="submit"
        onClick={onSubmit}
        disabled={loading}
        className="flex-1 bg-candle-500 text-memorial-950 hover:bg-candle-400 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {loading ? "Mencari..." : submitButtonText}
      </Button>
    </div>
  );
}
