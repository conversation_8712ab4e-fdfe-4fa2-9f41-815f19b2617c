/**
 * Force environment loading for Next.js
 * This ensures the correct environment file is loaded based on NODE_ENV
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// Force load environment variables before Next.js starts
function forceLoadEnvironment() {
  const nodeEnv = process.env.NODE_ENV || 'development';
  
  // Determine which env file to load
  let envFile = '.env.development';
  if (nodeEnv === 'production') {
    envFile = '.env.production';
  }

  // Load environment variables with override
  const envPath = resolve(process.cwd(), envFile);
  const result = config({ 
    path: envPath,
    override: true  // Override existing environment variables
  });

  if (!result.error) {
    console.log(`🔧 Force loaded ${envFile} for NODE_ENV=${nodeEnv}`);
    
    // Log critical environment variables for debugging
    if (nodeEnv === 'production') {
      const dbUrl = process.env.DATABASE_URL;
      if (dbUrl) {
        const maskedUrl = dbUrl.replace(/:([^:@]+)@/, ':***@');
        console.log(`📊 DATABASE_URL: ${maskedUrl}`);
      }
    }
  } else {
    console.warn(`⚠️ Could not force load ${envFile}:`, result.error.message);
  }

  return {
    nodeEnv,
    envFile,
    loaded: !result.error
  };
}

// Auto-load on import
const envInfo = forceLoadEnvironment();

export { envInfo, forceLoadEnvironment };
