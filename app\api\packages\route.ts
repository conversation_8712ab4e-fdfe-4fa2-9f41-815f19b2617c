import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db';
import { packages } from '@/db/schema';
import { eq, asc } from 'drizzle-orm';
import { handleApiError } from '@/utils/errorHandler';

/**
 * GET /api/packages
 * Retrieve all active packages from database
 */
export async function GET(request: NextRequest) {
  try {
    console.log('📦 Fetching packages from database...');

    // Get all active packages ordered by sort order
    const packagesData = await db
      .select()
      .from(packages)
      .where(eq(packages.isActive, 1))
      .orderBy(asc(packages.sortOrder));

    console.log(`✅ Found ${packagesData.length} active packages`);

    // Transform features from JSON string to array
    const transformedPackages = packagesData.map(pkg => ({
      ...pkg,
      features: JSON.parse(pkg.features)
    }));

    return NextResponse.json({
      success: true,
      data: transformedPackages
    });

  } catch (error) {
    console.error('❌ Error fetching packages:', error);
    return handleApiError(error);
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}


