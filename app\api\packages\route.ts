import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db';
import { packages } from '@/db/schema';
import { eq, asc } from 'drizzle-orm';
import { handleApiError } from '@/utils/errorHandler';

/**
 * GET /api/packages
 * Retrieve all active packages from database
 */
export async function GET(request: NextRequest) {
  try {
    console.log('📦 Fetching packages from database...');

    // Get all active packages ordered by sort order
    const packagesData = await db
      .select()
      .from(packages)
      .where(eq(packages.isActive, 1))
      .orderBy(asc(packages.sortOrder));

    console.log(`✅ Found ${packagesData.length} active packages`);

    // Transform features from JSON string to array
    const transformedPackages = packagesData.map(pkg => ({
      ...pkg,
      features: JSON.parse(pkg.features)
    }));

    return NextResponse.json({
      success: true,
      data: transformedPackages
    });

  } catch (error) {
    console.error('❌ Error fetching packages:', error);
    return handleApiError(error);
}
}

/**
 * GET /api/packages/[id]
 * Retrieve specific package by ID
 */
export async function getPackageById(packageId: string) {
  try {
    console.log(`📦 Fetching package with ID: ${packageId}`);

    const packageData = await db
      .select()
      .from(packages)
      .where(eq(packages.id, packageId))
      .limit(1);

    if (packageData.length === 0) {
      return null;
    }

    // Transform features from JSON string to array
    const transformedPackage = {
      ...packageData[0],
      features: JSON.parse(packageData[0].features)
    };

    console.log(`✅ Found package: ${transformedPackage.name}`);
    return transformedPackage;

  } catch (error) {
    console.error(`❌ Error fetching package ${packageId}:`, error);
    throw error;
  }
}
