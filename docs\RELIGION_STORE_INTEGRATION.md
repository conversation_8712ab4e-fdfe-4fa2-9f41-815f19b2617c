# Religion Store Integration

## Overview

BioDataStep sekarang terintegrasi dengan `religion-storage` state management untuk memuat religionId secara otomatis berdasarkan pilihan agama yang tersimpan di store.

## Integration Architecture

### State Management Structure

```typescript
// useReligionStore.ts
interface ReligionState {
  selectedReligion: string | null;  // "Islam", "Kristen", "Katolik", etc.
  hasSeenPopup: boolean;
  setSelectedReligion: (religion: string) => void;
  setHasSeenPopup: (seen: boolean) => void;
}

// Storage name: 'religion-storage'
```

### BioDataStep Integration

```typescript
// BioDataStep.tsx
import { useReligionStore } from "@/store/useReligionStore";

export default function BioDataStep({ initialData, onProceedToLocation, onReset }: BioDataStepProps) {
  // Get religion from store
  const { selectedReligion } = useReligionStore();
  
  // Convert religion name to ID
  const getReligionIdByName = (religionName: string | null): number | null => {
    // Implementation details...
  };
}
```

## Religion Name Mapping

### Store Names → Database Names

```typescript
const religionMap: { [key: string]: string } = {
  'Islam': 'Islam',                    // Store: Islam → DB: Islam (ID: 1)
  'Kristen': 'Kristen Protestan',     // Store: Kristen → DB: Kristen Protestan (ID: 2)
  'Katolik': 'Kristen Katolik',       // Store: Katolik → DB: Kristen Katolik (ID: 3)
  'Hindu': 'Hindu',                   // Store: Hindu → DB: Hindu (ID: 4)
  'Buddha': 'Buddha',                 // Store: Buddha → DB: Buddha (ID: 5)
  'Konghucu': 'Konghucu'             // Store: Konghucu → DB: Konghucu (ID: 6)
};
```

### Mapping Function

```typescript
const getReligionIdByName = (religionName: string | null): number | null => {
  if (!religionName || religions.length === 0) return null;
  
  const religionMap: { [key: string]: string } = {
    'Islam': 'Islam',
    'Kristen': 'Kristen Protestan',
    'Katolik': 'Kristen Katolik',
    'Hindu': 'Hindu',
    'Buddha': 'Buddha',
    'Konghucu': 'Konghucu'
  };
  
  const mappedName = religionMap[religionName] || religionName;
  const religion = religions.find(r => r.name === mappedName);
  return religion ? religion.id : null;
};
```

## Priority Logic

### Data Source Priority

1. **Session Data** (Highest Priority)
   - Data dari purchase store session
   - Digunakan jika ada dan valid (religionId > 0)

2. **Religion Store** (Fallback)
   - Data dari religion-storage
   - Digunakan jika session kosong atau invalid

3. **Null** (Default)
   - Jika tidak ada data dari kedua source

### Implementation

```typescript
// Session data handling with religion store fallback
if (hasContent) {
  // Get religionId from religion store if not in session data
  const religionIdFromStore = selectedReligion && religions.length > 0 
    ? getReligionIdByName(selectedReligion) 
    : null;
    
  setBioData({
    ...initialData,
    images: [],
    evidenceImage: null,
    // Prioritize session religionId, fallback to religion store, then null
    religionId: initialData.religionId || religionIdFromStore || null
  });
} else {
  // For empty session, try to load from religion store
  const religionIdFromStore = selectedReligion && religions.length > 0 
    ? getReligionIdByName(selectedReligion) 
    : null;
    
  setBioData({
    ...defaultData,
    religionId: religionIdFromStore || null
  });
}
```

## useEffect Dependencies

### Religion Store Loading

```typescript
// Load religionId from religion store when religions data is available
useEffect(() => {
  if (religions.length > 0 && selectedReligion && !bioData.religionId) {
    const religionId = getReligionIdByName(selectedReligion);
    if (religionId) {
      console.log(`🔄 Loading religionId from religion-storage: ${selectedReligion} → ${religionId}`);
      setBioData(prev => ({ ...prev, religionId }));
    }
  }
}, [religions, selectedReligion, bioData.religionId]);
```

### Session Data Handling

```typescript
// Enhanced dependency array to include religion store data
useEffect(() => {
  // Session data handling logic...
}, [initialData, JSON.stringify(initialData), selectedReligion, religions]);
```

## Integration Scenarios

### Scenario 1: New User with Religion Store

```
User Flow:
1. User visits homepage
2. Religion popup appears → User selects "Islam"
3. selectedReligion = "Islam" stored in religion-storage
4. User goes to purchase page
5. BioDataStep loads → religionId automatically set to 1
6. Religion dropdown shows "Islam" pre-selected

Result: ✅ Seamless experience
```

### Scenario 2: Returning User with Session

```
User Flow:
1. User has existing session with religionId = 2
2. User also has selectedReligion = "Islam" in store
3. BioDataStep loads → religionId = 2 (session priority)
4. Religion dropdown shows "Kristen Protestan" (from session)

Result: ✅ Session data preserved
```

### Scenario 3: Invalid Session with Valid Store

```
User Flow:
1. User has corrupted session with religionId = 0
2. User has selectedReligion = "Hindu" in store
3. BioDataStep loads → religionId = 4 (store fallback)
4. Religion dropdown shows "Hindu" pre-selected

Result: ✅ Graceful fallback
```

### Scenario 4: Empty State

```
User Flow:
1. User has no session data
2. User has no religion store data
3. BioDataStep loads → religionId = null
4. Religion dropdown shows placeholder "Pilih Agama"

Result: ✅ Clean empty state
```

## Testing Results

### Mapping Tests
```bash
npm run test:religion:store

# Results:
✅ Islam → ID: 1
✅ Kristen → ID: 2  
✅ Katolik → ID: 3
✅ Hindu → ID: 4
✅ Buddha → ID: 5
✅ Konghucu → ID: 6
✅ null → ID: null
✅ Unknown → ID: null
```

### Priority Tests
```bash
# Results:
✅ Session religionId takes priority over store
✅ Store used when session is null
✅ Store used when session religionId is 0 (invalid)
✅ null when both sources are empty
```

### Integration Flow Tests
```bash
# Results:
✅ New user with store → religionId from store
✅ Returning user with session → religionId from session
✅ Invalid session with store → religionId from store
```

## Benefits

### 1. **Improved User Experience**
- Form pre-fills with user's religion preference
- No need to select religion again if already chosen
- Seamless flow from homepage to purchase

### 2. **Data Consistency**
- Single source of truth for religion preference
- Consistent behavior across the application
- Proper fallback mechanisms

### 3. **Robust Handling**
- Handles edge cases gracefully
- Proper priority logic
- No data loss scenarios

### 4. **Performance**
- Efficient useEffect dependencies
- Prevents unnecessary re-renders
- Cached religion data

## Usage Examples

### Setting Religion in Store

```typescript
// From ReligionSelectionPopup
const handleSelectReligion = (religion: string) => {
  setSelectedReligion(religion);  // Stored in religion-storage
  setHasSeenPopup(true);
};
```

### Reading in BioDataStep

```typescript
// Automatic loading in BioDataStep
const { selectedReligion } = useReligionStore();

// Converts to religionId automatically
useEffect(() => {
  if (religions.length > 0 && selectedReligion && !bioData.religionId) {
    const religionId = getReligionIdByName(selectedReligion);
    if (religionId) {
      setBioData(prev => ({ ...prev, religionId }));
    }
  }
}, [religions, selectedReligion, bioData.religionId]);
```

## Commands

```bash
# Test religion store integration
npm run test:religion:store

# Test basic religion handling
npm run test:religion

# Manual testing
npm run dev
# 1. Visit homepage, select religion in popup
# 2. Go to purchase page
# 3. Verify religion is pre-selected
```

## Future Enhancements

1. **Sync Updates**
   - Update religion store when user changes religion in form
   - Bidirectional synchronization

2. **Advanced Mapping**
   - Support for more religion variations
   - Fuzzy matching for similar names

3. **Analytics**
   - Track religion selection patterns
   - Optimize user experience based on data

This integration ensures that religion input in BioDataStep works seamlessly with the global religion store, providing a better user experience and consistent data handling.
