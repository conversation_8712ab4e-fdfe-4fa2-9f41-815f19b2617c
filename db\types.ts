import { memorials, locations, religions, memorialImages, orders } from './schema';
import { InferSelectModel, InferInsertModel } from 'drizzle-orm';

// Memorial types
export type Memorial = InferSelectModel<typeof memorials>;
export type NewMemorial = InferInsertModel<typeof memorials>;

// Location types
export type Location = InferSelectModel<typeof locations>;
export type NewLocation = InferInsertModel<typeof locations>;

// Religion types
export type Religion = InferSelectModel<typeof religions>;
export type NewReligion = InferInsertModel<typeof religions>;

// Memorial Image types
export type MemorialImage = InferSelectModel<typeof memorialImages>;
export type NewMemorialImage = InferInsertModel<typeof memorialImages>;

// Order types
export type Order = InferSelectModel<typeof orders>;
export type NewOrder = InferInsertModel<typeof orders>;

