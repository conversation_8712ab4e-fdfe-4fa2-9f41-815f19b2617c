# Component Structure

## Memorial Components Architecture

Komponen memorial telah dipisahkan menjadi beberapa file yang lebih modular dan mudah di-maintain.

### File Structure

```
components/memorial/
├── index.ts                    # Export barrel file
├── DeceasedCard.tsx           # Main card component (orchestrator)
├── DeceasedList.tsx           # List wrapper component
├── BioItem.tsx                # Bio data display components
├── MemorialImageGallery.tsx   # Image gallery with carousel
├── MemorialBioSection.tsx     # Bio section wrapper
└── MemorialLifeStory.tsx      # Life story section

hooks/
└── useIsMobile.ts             # Mobile detection hook

lib/utils/
└── memorialUtils.ts           # Helper functions
```

### Component Responsibilities

#### 1. **DeceasedCard.tsx** (Main Orchestrator)
```typescript
// Responsibilities:
// - Layout coordination
// - Mobile/desktop logic
// - Component composition
// - Main card structure

import { useIsMobile } from "@/hooks/useIsMobile";
import { MemorialImageGallery } from "./MemorialImageGallery";
import { MemorialBioSection } from "./MemorialBioSection";
import { MemorialLifeStory } from "./MemorialLifeStory";
```

#### 2. **BioItem.tsx** (Data Display)
```typescript
// Responsibilities:
// - Basic bio data display
// - Location with Google Maps link
// - Consistent styling

export function BioItem({ label, value }: BioItemProps);
export function BioItemWithLocation({ label, value, locationUrl }: BioItemWithLocationProps);
```

#### 3. **MemorialImageGallery.tsx** (Image Handling)
```typescript
// Responsibilities:
// - Image carousel for mobile
// - Side-by-side layout for desktop
// - Image optimization
// - Responsive behavior

export function MemorialImageGallery({ images, name, isMobile }: MemorialImageGalleryProps);
```

#### 4. **MemorialBioSection.tsx** (Bio Coordination)
```typescript
// Responsibilities:
// - Bio data layout
// - Date formatting integration
// - Location URL generation
// - Responsive width calculation

export function MemorialBioSection({ deceased, hasTwoImages, isMobile }: MemorialBioSectionProps);
```

#### 5. **MemorialLifeStory.tsx** (Content Display)
```typescript
// Responsibilities:
// - Life story display
// - Consistent styling
// - Text formatting

export function MemorialLifeStory({ description }: MemorialLifeStoryProps);
```

### Utility Functions

#### 1. **memorialUtils.ts**
```typescript
// Date formatting
export const formatDate = (dateString: string): string;

// Google Maps URL generation
export const getLocationUrl = (deceased: Deceased): string | null;

// Mobile detection utility
export const isMobileDevice = (width?: number): boolean;
```

#### 2. **useIsMobile.ts** (Custom Hook)
```typescript
// Responsive behavior hook
export function useIsMobile(): boolean;
```

### Benefits of Modular Structure

#### 1. **Separation of Concerns**
- Each component has single responsibility
- Easy to test individual components
- Clear dependencies

#### 2. **Reusability**
- BioItem can be used in other contexts
- Image gallery can be reused
- Utility functions are shared

#### 3. **Maintainability**
- Small, focused files
- Easy to locate specific functionality
- Clear import/export structure

#### 4. **Performance**
- Tree-shaking friendly
- Lazy loading potential
- Smaller bundle chunks

#### 5. **Testing**
- Unit test individual components
- Mock dependencies easily
- Isolated testing scope

### Usage Examples

#### 1. **Using Individual Components**
```typescript
import { BioItem, BioItemWithLocation } from '@/components/memorial';
import { formatDate } from '@/lib/utils/memorialUtils';

// In any component
<BioItem label="Nama" value={person.name} />
<BioItemWithLocation 
  label="Lokasi" 
  value={location.name} 
  locationUrl={getLocationUrl(person)} 
/>
```

#### 2. **Using Utility Functions**
```typescript
import { formatDate, getLocationUrl } from '@/lib/utils/memorialUtils';

const formattedDate = formatDate('2023-12-25T00:00:00.000Z');
// Output: "25 Desember 2023"

const mapsUrl = getLocationUrl(deceased);
// Output: "https://www.google.com/maps/place/?q=place_id:ChIJ..."
```

#### 3. **Using Custom Hook**
```typescript
import { useIsMobile } from '@/hooks/useIsMobile';

function MyComponent() {
  const isMobile = useIsMobile();
  
  return (
    <div className={isMobile ? 'mobile-layout' : 'desktop-layout'}>
      {/* Content */}
    </div>
  );
}
```

### Import/Export Strategy

#### 1. **Barrel Exports** (components/memorial/index.ts)
```typescript
// Clean imports from outside
import { DeceasedCard, BioItem, MemorialImageGallery } from '@/components/memorial';
```

#### 2. **Direct Imports** (for specific needs)
```typescript
// Direct import for specific component
import { BioItem } from '@/components/memorial/BioItem';
```

#### 3. **Utility Imports**
```typescript
// Utility functions
import { formatDate, getLocationUrl } from '@/lib/utils/memorialUtils';
```

### Testing Strategy

#### 1. **Component Tests**
```typescript
// Test individual components
describe('BioItem', () => {
  it('should display label and value correctly', () => {
    // Test implementation
  });
});

describe('MemorialImageGallery', () => {
  it('should show carousel on mobile with multiple images', () => {
    // Test implementation
  });
});
```

#### 2. **Utility Tests**
```typescript
// Test utility functions
describe('memorialUtils', () => {
  describe('formatDate', () => {
    it('should format ISO date to Indonesian format', () => {
      expect(formatDate('2023-12-25T00:00:00.000Z')).toBe('25 Desember 2023');
    });
  });
});
```

#### 3. **Integration Tests**
```typescript
// Test component integration
describe('DeceasedCard', () => {
  it('should render all sections correctly', () => {
    // Test full card rendering
  });
});
```

### Migration Benefits

#### Before (Monolithic)
- 222 lines in single file
- Mixed responsibilities
- Hard to test specific features
- Difficult to reuse components

#### After (Modular)
- 6 focused files (30-60 lines each)
- Clear separation of concerns
- Easy to test and maintain
- Reusable components
- Better developer experience

This modular structure makes the codebase more maintainable, testable, and scalable for future development.
