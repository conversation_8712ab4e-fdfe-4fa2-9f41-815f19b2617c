import { CakeSlice, Search, Users } from "lucide-react";

export default function AboutMission() {
  const missions = [
    {
      icon: <Search className="h-10 w-10 text-candle-500" />,
      title: "Ke<PERSON><PERSON><PERSON> Pencarian",
      description: "Menyediakan akses mudah untuk mencari lokasi pemakaman berdasarkan berbagai kriteria seperti lokasi, agama, dan ketersediaan."
    },
    {
      icon: <Users className="h-10 w-10 text-candle-500" />,
      title: "Pengelolaan Modern",
      description: "Membantu pengelola TPU dengan sistem administrasi digital yang efisien untuk manajemen data pemakaman."
    },
    {
      icon: <CakeSlice className="h-10 w-10 text-candle-500" />,
      title: "Menghormati Tradisi",
      description: "Menggabungkan teknologi modern dengan penghormatan terhadap tradisi dan nilai-nilai budaya dalam pengelolaan pemakaman."
    }
  ];

  return (
    <section className="mb-16">
      <h2 className="text-2xl md:text-3xl font-bold mb-8 text-center"><PERSON><PERSON></h2>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {missions.map((mission, index) => (
          <div key={index} className="bg-memorial-900 border border-memorial-800 rounded-lg p-6 text-center">
            <div className="flex justify-center mb-4">
              {mission.icon}
            </div>
            <h3 className="text-xl font-semibold mb-3 text-memorial-50">{mission.title}</h3>
            <p className="text-memorial-300">{mission.description}</p>
          </div>
        ))}
      </div>
    </section>
  );
}
