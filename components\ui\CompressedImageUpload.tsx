"use client";

import { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ImageHelpers } from '@/lib/utils/imageHelpers';
import { useImageCompression, CompressedImageResult } from '@/hooks/useImageCompression';
import { <PERSON>rrorHandler } from '@/lib/errorHandler';
import { Upload, X, Loader2, Zap, FileImage, CheckCircle } from 'lucide-react';

interface CompressedImageUploadProps {
  maxImages?: number;
  onImagesChange?: (compressedImages: CompressedImageResult[]) => void;
  onFilesChange?: (files: File[]) => void;
  className?: string;
  accept?: string;
  disabled?: boolean;
  showCompressionStats?: boolean;
}

export default function CompressedImageUpload({
  maxImages = 2,
  onImagesChange,
  onFilesChange,
  className = "",
  accept = "image/*",
  disabled = false,
  showCompressionStats = true
}: CompressedImageUploadProps) {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [compressedImages, setCompressedImages] = useState<CompressedImageResult[]>([]);
  const [isCompressed, setIsCompressed] = useState(false);
  
  const { compressImages, loading, error, statistics } = useImageCompression();

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFiles = Array.from(e.target.files);
      
      // Validate each file
      for (const file of newFiles) {
        const validation = ImageHelpers.validateImageFile(file);
        if (!validation.isValid) {
          ErrorHandler.handle(validation.error || "Invalid image file", "Image Upload");
          return;
        }
      }

      // Limit to maximum images
      const limitedFiles = ImageHelpers.limitImages(selectedFiles, newFiles, maxImages);
      
      setSelectedFiles(limitedFiles);
      setIsCompressed(false);
      setCompressedImages([]);
      
      // Notify parent of file changes
      onFilesChange?.(limitedFiles);
      
      // Reset file input
      e.target.value = '';
    }
  }, [selectedFiles, maxImages, onFilesChange]);

  const handleCompress = useCallback(async () => {
    if (selectedFiles.length === 0) return;

    try {
      const compressed = await compressImages(selectedFiles);
      setCompressedImages(compressed);
      setIsCompressed(true);
      
      // Notify parent of compressed images
      onImagesChange?.(compressed);
      
      ErrorHandler.handleSuccess(
        `${compressed.length} gambar berhasil dikompresi! Ukuran berkurang rata-rata ${statistics?.averageCompressionRatio.toFixed(1)}%`
      );
      
    } catch (err) {
      ErrorHandler.handle(err, "Image Compression");
    }
  }, [selectedFiles, compressImages, onImagesChange, statistics]);

  const handleRemoveFile = useCallback((index: number) => {
    const updatedFiles = ImageHelpers.removeImageByIndex(selectedFiles, index);
    const updatedCompressed = compressedImages.filter((_, i) => i !== index);
    
    setSelectedFiles(updatedFiles);
    setCompressedImages(updatedCompressed);
    setIsCompressed(updatedFiles.length === 0 ? false : updatedCompressed.length === updatedFiles.length);
    
    onFilesChange?.(updatedFiles);
    onImagesChange?.(updatedCompressed);
  }, [selectedFiles, compressedImages, onFilesChange, onImagesChange]);

  const handleClearAll = useCallback(() => {
    setSelectedFiles([]);
    setCompressedImages([]);
    setIsCompressed(false);
    onFilesChange?.([]);
    onImagesChange?.([]);
  }, [onFilesChange, onImagesChange]);

  const totalOriginalSize = selectedFiles.reduce((sum, file) => sum + file.size, 0);
  const totalCompressedSize = compressedImages.reduce((sum, img) => sum + img.size, 0);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      <Card className="bg-memorial-800 border-memorial-700">
        <CardContent className="p-6">
          <div className="space-y-4">
            {/* File Input */}
            <div className="flex items-center justify-center w-full">
              <label className={`flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer transition-colors ${
                disabled 
                  ? 'border-memorial-600 bg-memorial-900 cursor-not-allowed' 
                  : 'border-memorial-600 bg-memorial-800 hover:bg-memorial-700 hover:border-memorial-500'
              }`}>
                <div className="flex flex-col items-center justify-center pt-5 pb-6">
                  <Upload className={`w-8 h-8 mb-2 ${disabled ? 'text-memorial-600' : 'text-memorial-400'}`} />
                  <p className={`mb-2 text-sm ${disabled ? 'text-memorial-600' : 'text-memorial-400'}`}>
                    <span className="font-semibold">Click to upload</span> or drag and drop
                  </p>
                  <p className={`text-xs ${disabled ? 'text-memorial-600' : 'text-memorial-500'}`}>
                    {ImageHelpers.getAcceptedExtensions()} (MAX. {ImageHelpers.formatFileSize(ImageHelpers.getMaxFileSize())})
                  </p>
                  <p className={`text-xs ${disabled ? 'text-memorial-600' : 'text-memorial-500'}`}>
                    Maximum {maxImages} images
                  </p>
                </div>
                <input
                  type="file"
                  className="hidden"
                  accept={accept}
                  multiple={maxImages > 1}
                  onChange={handleFileSelect}
                  disabled={disabled}
                />
              </label>
            </div>

            {/* Selected Files */}
            {selectedFiles.length > 0 && (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-memorial-50">
                    Selected Images ({selectedFiles.length}/{maxImages})
                  </h4>
                  <Button
                    onClick={handleClearAll}
                    variant="outline"
                    size="sm"
                    className="border-memorial-600 text-memorial-300 hover:bg-memorial-700"
                  >
                    Clear All
                  </Button>
                </div>

                <div className="grid gap-3">
                  {selectedFiles.map((file, index) => (
                    <div key={index} className="flex items-center gap-3 p-3 bg-memorial-700 rounded-lg">
                      <div className="flex-shrink-0">
                        <FileImage className="h-8 w-8 text-memorial-400" />
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-memorial-50 truncate">
                          {file.name}
                        </p>
                        <div className="flex items-center gap-2 mt-1">
                          <span className="text-xs text-memorial-400">
                            {ImageHelpers.formatFileSize(file.size)}
                          </span>
                          {isCompressed && compressedImages[index] && (
                            <>
                              <span className="text-xs text-memorial-500">→</span>
                              <span className="text-xs text-green-400">
                                {ImageHelpers.formatFileSize(compressedImages[index].size)}
                              </span>
                              <Badge variant="secondary" className="bg-green-900/30 text-green-200 text-xs">
                                -{compressedImages[index].compressionRatio.toFixed(1)}%
                              </Badge>
                            </>
                          )}
                        </div>
                      </div>

                      {isCompressed && compressedImages[index] && (
                        <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                      )}

                      <Button
                        onClick={() => handleRemoveFile(index)}
                        variant="ghost"
                        size="sm"
                        className="text-memorial-400 hover:text-memorial-50 hover:bg-memorial-600 flex-shrink-0"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>

                {/* Compression Controls */}
                <div className="flex items-center gap-3 pt-2">
                  <Button
                    onClick={handleCompress}
                    disabled={loading || isCompressed}
                    className="bg-candle-500 hover:bg-candle-400 text-memorial-950"
                  >
                    {loading ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Compressing...
                      </>
                    ) : isCompressed ? (
                      <>
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Compressed
                      </>
                    ) : (
                      <>
                        <Zap className="h-4 w-4 mr-2" />
                        Compress Images
                      </>
                    )}
                  </Button>

                  {!isCompressed && (
                    <p className="text-xs text-memorial-400">
                      Compress images to reduce file size by up to 80%
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Compression Statistics */}
            {showCompressionStats && isCompressed && statistics && (
              <Card className="bg-memorial-700 border-memorial-600">
                <CardContent className="p-4">
                  <h5 className="font-medium text-memorial-50 mb-3">Compression Results</h5>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
                    <div className="text-center">
                      <div className="text-lg font-bold text-candle-500">
                        {statistics.averageCompressionRatio.toFixed(1)}%
                      </div>
                      <div className="text-memorial-400">Reduction</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-green-500">
                        {ImageHelpers.formatFileSize(statistics.totalSizeSaved)}
                      </div>
                      <div className="text-memorial-400">Saved</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-blue-500">
                        {ImageHelpers.formatFileSize(totalCompressedSize)}
                      </div>
                      <div className="text-memorial-400">Final Size</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-purple-500">
                        {statistics.processingTime}ms
                      </div>
                      <div className="text-memorial-400">Time</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Error Display */}
            {error && (
              <div className="p-3 bg-red-900/30 border border-red-800 rounded-lg">
                <p className="text-sm text-red-200">{error}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
