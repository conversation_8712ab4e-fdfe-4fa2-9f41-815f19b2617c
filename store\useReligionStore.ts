import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface ReligionState {
  selectedReligion: string | null;
  hasSeenPopup: boolean;
  setSelectedReligion: (religion: string) => void;
  setHasSeenPopup: (seen: boolean) => void;
}

export const useReligionStore = create<ReligionState>()(
  persist(
    (set) => ({
      selectedReligion: null,
      hasSeenPopup: false,
      setSelectedReligion: (religion) => set({ selectedReligion: religion }),
      setHasSeenPopup: (seen) => set({ hasSeenPopup: seen }),
    }),
    {
      name: 'religion-storage',
    }
  )
);