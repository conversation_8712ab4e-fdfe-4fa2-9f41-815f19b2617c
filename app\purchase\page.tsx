"use client";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { AlertDialog } from "@/components/ui/alert-dialog";
import { ArrowLeft } from "lucide-react";
import PurchaseStepper from "@/components/purchase/PurchaseStepper";
import PackageStep from "@/components/purchase/PackageStep";
import PaymentStep from "@/components/purchase/PaymentStep";
import BioDataStep, { BioData } from "@/components/purchase/BioDataStep";
import LocationStep from "@/components/purchase/LocationStep";
import ReviewDetailsStep from "@/components/purchase/ReviewDetailsStep";
import ConfirmationStep from "@/components/purchase/ConfirmationStep";
import { PackageDetails, PurchaseResult, LocationSearchResult } from "@/types";
import PageWithoutFooter from "@/components/layouts/PageWithoutFooter";
import { usePurchaseStore } from "@/store/usePurchaseStore";
import { useAlertDialog } from "@/hooks/useAlertDialog";
import { ErrorHandler } from "@/lib/errorHandler";
import { packageService } from "@/lib/services/packageService";
import { useToast } from "@/hooks/useToast";

export default function PurchasePage() {
  const router = useRouter();
  const { toast } = useToast();

  // Alert dialog hook
  const { isOpen, config, showAlert, hideAlert, handleConfirm } = useAlertDialog();

  // Gunakan store untuk state management
  const {
    currentStep,
    bioData,
    selectedLocation,
    packageDetails,
    paymentToken,
    setCurrentStep,
    setBioData,
    setSelectedLocation,
    setPackageDetails,
    setPaymentToken,
    resetBioData,
    resetLocation,
    resetPackageDetails,
    resetPurchase,
    resetAfterPayment
  } = usePurchaseStore();

  // State untuk menyimpan hasil purchase
  const [purchaseResult, setPurchaseResult] = useState<PurchaseResult | null>(null);

  // State untuk mengelola reset confirmation
  const [autoResetTimer, setAutoResetTimer] = useState<NodeJS.Timeout | null>(null);

  // State untuk packages
  const [packages, setPackages] = useState<Record<string, PackageDetails>>({});
  const [packagesLoading, setPackagesLoading] = useState(true);
  
  // Pastikan bioData tidak null saat pertama kali load
  useEffect(() => {
    if (!bioData) {
      setBioData({
        name: "",
        birthPlace: "",
        birthDate: "",
        deathDate: "",
        religionId: null,  // Fixed: Use null instead of 0
        submittedBy: "",
        description: "",
        images: [],
        evidenceName: "",
        evidenceImage: null
      });
    }
  }, [bioData, setBioData]);

  // Load packages on mount
  useEffect(() => {
    const loadPackages = async () => {
      try {
        setPackagesLoading(true);
        const packagesData = await packageService.getPackagesAsDetails();
        setPackages(packagesData);
      } catch (error) {
        console.error('❌ Error loading packages:', error);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Gagal memuat data paket. Menggunakan data default.",
        });

        // Fallback to default packages
        setPackages(packageService.getDefaultPackages());
      } finally {
        setPackagesLoading(false);
      }
    };

    loadPackages();
  }, [toast]);

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (autoResetTimer) {
        clearTimeout(autoResetTimer);
      }
    };
  }, [autoResetTimer]);
  
  const handlePackageSelected = (packageDetails: PackageDetails) => {
    setPackageDetails(packageDetails);
    setCurrentStep('payment');
  };

  const handleCompletePayment = (token: string) => {
    console.log('Payment completed, token:', token);
    setPaymentToken(token);
    setCurrentStep('biodata');
  };

  const handleBioDataSubmitted = (data: BioData) => {
    setBioData(data);
    setCurrentStep('location');
  };

  const handleLocationSelected = (location: LocationSearchResult) => {
    setSelectedLocation(location);
    setCurrentStep('details');
  };

  const handleSubmitComplete = (result: PurchaseResult) => {
    console.log('Memorial created successfully:', result);
    setPurchaseResult(result);
    setCurrentStep('confirmation');

    // Set auto-reset timer for 5 seconds (shorter delay for better UX)
    const timer = setTimeout(() => {
      console.log('🧹 Auto-clearing purchase state after 30 seconds');
      handleClearState();
    }, 30000); // 5 seconds auto-reset

    setAutoResetTimer(timer);
  };

  // Handler untuk clear state (background - reset semua ke package)
  const handleClearState = () => {
    if (autoResetTimer) {
      clearTimeout(autoResetTimer);
      setAutoResetTimer(null);
    }

    // Background reset - selalu reset semua ke package step
    resetPurchase();
    console.log('🧹 Purchase state cleared completely - back to package selection');
  };

  // Handler untuk manual clear state via navigation buttons (background reset)
  const handleManualClearState = () => {
    handleClearState();
    ErrorHandler.handleSuccess("Data pembelian berhasil dibersihkan, kembali ke pemilihan paket");
  };

  // Reset handlers for each step
  const handleResetBioData = () => {
    console.log('🔄 handleResetBioData called');
    showAlert({
      title: "Reset Data Biodata",
      description: "Apakah Anda yakin ingin mengosongkan semua data biodata? Data yang sudah diisi akan hilang.",
      confirmText: "Ya, Reset",
      cancelText: "Batal",
      variant: "destructive",
      onConfirm: () => {
        console.log('✅ User confirmed bioData reset');
        resetBioData();
        // Force re-render by updating the step
        setCurrentStep('biodata');
        ErrorHandler.handleSuccess("Data biodata berhasil direset");
      }
    });
  };

  const handleResetLocation = () => {
    console.log('🔄 handleResetLocation called');
    showAlert({
      title: "Reset Lokasi",
      description: "Apakah Anda yakin ingin menghapus lokasi yang sudah dipilih?",
      confirmText: "Ya, Reset",
      cancelText: "Batal",
      variant: "destructive",
      onConfirm: () => {
        console.log('✅ User confirmed location reset');
        resetLocation();
        // Force re-render by updating the step
        setCurrentStep('location');
        ErrorHandler.handleSuccess("Lokasi berhasil direset");
      }
    });
  };



  const handleResetAll = () => {
    const hasPayment = !!paymentToken;

    if (hasPayment) {
      // Foreground reset - hanya reset sampai biodata step
      showAlert({
        title: "Reset Data Setelah Pembayaran",
        description: "Apakah Anda yakin ingin mengosongkan semua data biodata? Data yang sudah diisi akan hilang.",
        confirmText: "Ya, Reset ke Biodata",
        cancelText: "Batal",
        variant: "default",
        onConfirm: () => {
          resetAfterPayment();
          ErrorHandler.handleSuccess("Data berhasil direset ke langkah biodata");
        }
      });
    } else {
      // Reset semua kembali ke package step
      showAlert({
        title: "Reset Semua Data",
        description: "Apakah Anda yakin ingin mengosongkan semua data dan memulai dari awal? Semua progress akan hilang.",
        confirmText: "Ya, Reset Semua",
        cancelText: "Batal",
        variant: "destructive",
        onConfirm: () => {
          resetPurchase();
          ErrorHandler.handleSuccess("Semua data berhasil direset, kembali ke pemilihan paket");
        }
      });
    }
  };

  const handleBack = () => {
    switch (currentStep) {
      case 'payment':
        setCurrentStep('package');
        break;
      case 'biodata':
        // Jika sudah ada paymentToken, tidak bisa kembali ke payment
        if (paymentToken) {
          router.push('/');
        } else {
          setCurrentStep('payment');
        }
        break;
      case 'location':
        setCurrentStep('biodata');
        break;
      case 'details':
        setCurrentStep('location');
        break;
      // Removed confirmation case - no back button on confirmation step
      default:
        router.push('/');
        break;
    }
  };

  const handleBackToHome = () => {
    router.push('/');
  };
  
  return (
    <PageWithoutFooter>
      <main className="flex-1 bg-memorial-950">
        <div className={`container mx-auto py-8 px-4 min-h-screen flex flex-col items-center mt-16 md:mt-20`}>
          
          <h1 className="text-2xl md:text-3xl font-bold text-memorial-50 mb-8 text-center">
            Pembelian Memorial Digital
          </h1>
          
          {/* Back Button and Reset All - Hide completely on confirmation step */}
          {currentStep !== 'confirmation' && (
            <div className="w-full max-w-4xl mb-6 flex justify-between items-center">
              <div>
                {currentStep === 'package' ? (
                  <Button
                    variant="ghost"
                    onClick={handleBackToHome}
                    className="flex items-center gap-2 text-memorial-300 hover:text-memorial-50"
                  >
                    <ArrowLeft className="h-4 w-4" />
                    Kembali ke Gerbang
                  </Button>
                ) : currentStep === 'biodata' && paymentToken ? (
                  <Button
                    variant="ghost"
                    onClick={handleBackToHome}
                    className="flex items-center gap-2 text-memorial-300 hover:text-memorial-50"
                  >
                    <ArrowLeft className="h-4 w-4" />
                    Kembali ke Beranda
                  </Button>
                ) : (
                  <Button
                    variant="ghost"
                    onClick={handleBack}
                    className="flex items-center gap-2 text-memorial-300 hover:text-memorial-50"
                  >
                    <ArrowLeft className="h-4 w-4" />
                    Kembali
                  </Button>
                )}
              </div>

              {/* Reset All Button */}
              {paymentToken && (
                <Button
                variant="outline"
                onClick={handleResetAll}
                className={`text-sm border-red-600 text-red-400 hover:bg-red-900/20 hover:text-red-300`}
                title="Reset ke langkah biodata (pembayaran tetap terjaga)"
              >
                Reset Biodata
              </Button>
              )}
              
            </div>
          )}

          <div className="w-full max-w-4xl mb-6 md:mb-10">
            {/* Stepper */}
            <PurchaseStepper currentStep={currentStep} />
            
            {/* Step content */}
            {currentStep === 'package' && (
              <PackageStep
                key={`package-${packageDetails?.id || 'empty'}`}
                initialPackage={packageDetails?.id}
                onProceedToPayment={handlePackageSelected}
                packages={packages}
                loading={packagesLoading}
              />
            )}

            {currentStep === 'payment' && packageDetails && (
              <PaymentStep
                packageDetails={packageDetails}
                onCompletePayment={handleCompletePayment}
              />
            )}

            {currentStep === 'biodata' && paymentToken && (
              <BioDataStep
                key={`biodata-${bioData?.name || 'empty'}-${bioData?.birthDate || 'empty'}`}
                initialData={bioData || undefined}
                onProceedToLocation={handleBioDataSubmitted}
                onReset={handleResetBioData}
                selectedPackage={packageDetails?.id}
              />
            )}

            {currentStep === 'location' && paymentToken && bioData && (
              <LocationStep
                key={`location-${selectedLocation?.place_id || 'empty'}`}
                initialData={selectedLocation || undefined}
                onLocationSelected={handleLocationSelected}
                onReset={handleResetLocation}
              />
            )}

            {currentStep === 'details' && paymentToken && bioData && selectedLocation && packageDetails && (
              <ReviewDetailsStep
                packageDetails={packageDetails}
                bioData={bioData}
                selectedLocation={selectedLocation}
                paymentToken={paymentToken}
                onSubmitComplete={handleSubmitComplete}
                onReset={() => setCurrentStep('location')}
              />
            )}

            {currentStep === 'confirmation' && (
              <ConfirmationStep
                purchaseResult={purchaseResult}
                onClearState={handleManualClearState}
              />
            )}

            {/* Fallback: If no step matches, show package step */}
            {!['package', 'payment', 'biodata', 'location', 'details', 'confirmation'].includes(currentStep) && (
              <PackageStep
                key="fallback-package"
                initialPackage={packageDetails?.id}
                onProceedToPayment={handlePackageSelected}
                packages={packages}
                loading={packagesLoading}
              />
            )}

            {/* Show loading if step conditions are not met */}
            {currentStep === 'payment' && !packageDetails && (
              <div className="bg-memorial-900 p-6 rounded-lg border border-memorial-800 text-center">
                <p className="text-memorial-300">Memuat data paket...</p>
              </div>
            )}

            {currentStep === 'biodata' && !paymentToken && (
              <div className="bg-memorial-900 p-6 rounded-lg border border-memorial-800 text-center">
                <p className="text-memorial-300">Menunggu konfirmasi pembayaran...</p>
              </div>
            )}

            {currentStep === 'location' && (!paymentToken || !bioData) && (
              <div className="bg-memorial-900 p-6 rounded-lg border border-memorial-800 text-center">
                <p className="text-memorial-300">Menunggu data biodata...</p>
              </div>
            )}

            {currentStep === 'details' && (!paymentToken || !bioData || !selectedLocation || !packageDetails) && (
              <div className="bg-memorial-900 p-6 rounded-lg border border-memorial-800 text-center">
                <p className="text-memorial-300">Menunggu data lengkap...</p>
              </div>
            )}
          </div>
        </div>
      </main>

      {/* Alert Dialog */}
      {config && (
        <AlertDialog
          isOpen={isOpen}
          onClose={hideAlert}
          onConfirm={handleConfirm}
          title={config.title}
          description={config.description}
          confirmText={config.confirmText}
          cancelText={config.cancelText}
          variant={config.variant}
        />
      )}
    </PageWithoutFooter>
  );
}





