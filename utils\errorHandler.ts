import { NextResponse } from 'next/server';
import { ApiError } from './ApiError';

export function handleApiError(error: unknown) {
  console.error('API Error:', error);
  
  if (error instanceof ApiError) {
    return NextResponse.json(
      { 
        success: false, 
        error: error.message,
        code: error.code
      },
      { status: error.statusCode }
    );
  }
  
  // Handle validation errors
  if (error instanceof Error && error.name === 'ValidationError') {
    return NextResponse.json(
      { 
        success: false, 
        error: error.message,
        code: 'VALIDATION_ERROR'
      },
      { status: 400 }
    );
  }
  
  // Default error response
  return NextResponse.json(
    { 
      success: false, 
      error: error instanceof Error ? error.message : 'An unexpected error occurred',
      code: 'INTERNAL_SERVER_ERROR'
    },
    { status: 500 }
  );
}