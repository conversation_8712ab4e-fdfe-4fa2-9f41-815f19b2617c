import { createClient } from '@supabase/supabase-js';
import { env } from '@/lib/env';

// Supabase Service Role client configuration
// Hanya digunakan untuk operasi yang memerlukan bypass RLS setelah pembayaran
const supabaseUrl = env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceRoleKey = env.SUPABASE_SERVICE_ROLE_KEY;

// Service Role client - HANYA untuk server-side operations
export const supabaseServiceRole = supabaseUrl && supabaseServiceRoleKey
  ? createClient(supabaseUrl, supabaseServiceRoleKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })
  : null;

// Helper function untuk cek apakah service role tersedia
export const isServiceRoleAvailable = () => Boolean(supabaseUrl && supabaseServiceRoleKey);

// Type untuk payment token validation
export interface PaymentTokenData {
  token: string;
  timestamp: number;
  packageId: string;
  isValid: boolean;
}

/**
 * Validate payment token
 * Memastikan token valid dan pembayaran sudah dilakukan
 */
export function validatePaymentToken(token: string): PaymentTokenData {
  try {
    // Parse token format: pay_timestamp_randomstring
    const parts = token.split('_');
    if (parts.length < 3 || parts[0] !== 'pay') {
      return { token, timestamp: 0, packageId: '', isValid: false };
    }

    const timestamp = parseInt(parts[1]);
    const now = Date.now();
    
    // Token valid untuk 24 jam
    const tokenAge = now - timestamp;
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    
    if (tokenAge > maxAge) {
      console.warn('Payment token expired:', { token, age: tokenAge });
      return { token, timestamp, packageId: '', isValid: false };
    }

    return {
      token,
      timestamp,
      packageId: 'unknown', // Could be extracted from token if needed
      isValid: true
    };
  } catch (error) {
    console.error('Error validating payment token:', error);
    return { token, timestamp: 0, packageId: '', isValid: false };
  }
}

/**
 * Check if user has valid payment authorization
 * Hanya user yang sudah bayar yang bisa upload
 */
export function hasPaymentAuthorization(paymentToken?: string | null): boolean {
  if (!paymentToken) {
    console.log('No payment token provided');
    return false;
  }

  const validation = validatePaymentToken(paymentToken);
  if (!validation.isValid) {
    console.log('Invalid payment token:', paymentToken);
    return false;
  }

  console.log('Payment authorization valid:', { token: paymentToken, timestamp: validation.timestamp });
  return true;
}

/**
 * Get appropriate Supabase client based on payment status
 * Service role untuk user yang sudah bayar, regular client untuk yang belum
 */
export function getSupabaseClient(paymentToken?: string | null) {
  // Jika user sudah bayar dan service role tersedia, gunakan service role
  if (hasPaymentAuthorization(paymentToken) && isServiceRoleAvailable()) {
    console.log('Using service role client for authorized user');
    return supabaseServiceRole;
  }

  // Fallback ke regular client (akan kena RLS)
  console.log('Using regular client (RLS applies)');
  return null; // Will trigger fallback to placeholder
}
