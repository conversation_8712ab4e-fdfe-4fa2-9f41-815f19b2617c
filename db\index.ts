import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema';
import { env, logEnvironmentStatus } from '@/lib/env';
import '@/lib/forceEnv'; // Force load environment before anything else

// Log environment status for debugging
if (env.NODE_ENV === 'development') {
  logEnvironmentStatus();
}

// Database connection
// Development: PostgreSQL lokal
// Production: Supabase Cloud PostgreSQL
const connectionString = env.DATABASE_URL ||
  `postgresql://${process.env.DB_USER || 'postgres'}:${process.env.DB_PASSWORD || 'password'}@${process.env.DB_HOST || 'localhost'}:${process.env.DB_PORT || '5432'}/${process.env.DB_NAME || 'pemakaman_digital'}`;

if (!connectionString || connectionString.includes('undefined')) {
  console.error('❌ Database configuration error:');
  console.error(`   NODE_ENV: ${env.NODE_ENV}`);
  console.error(`   DATABASE_URL: ${env.DATABASE_URL ? '[SET]' : '[NOT SET]'}`);
  console.error(`   Environment file: ${env._envInfo.envFile}`);
  throw new Error('DATABASE_URL or individual DB environment variables are not properly configured');
}

// Create postgres client
const queryClient = postgres(connectionString);

// Create drizzle instance
export const db = drizzle(queryClient, { schema });