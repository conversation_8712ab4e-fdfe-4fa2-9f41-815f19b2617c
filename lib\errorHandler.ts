import { toast } from "@/hooks/useToast"
import { ApiError } from "@/types"

export class ErrorHandler {
  static handle(error: any, context?: string): void {
    console.error(`❌ Error in ${context || 'Unknown context'}:`, error)
    
    let errorMessage = "Terja<PERSON> kesalahan yang tidak terduga"
    let errorTitle = "Error"
    
    // Handle different types of errors
    if (error instanceof Error) {
      errorMessage = error.message
    } else if (typeof error === 'string') {
      errorMessage = error
    } else if (error?.message) {
      errorMessage = error.message
    }
    
    // Handle specific error types
    if (error?.status || error?.code) {
      switch (error.status || error.code) {
        case 400:
          errorTitle = "Data Tidak Valid"
          errorMessage = error.message || "Data yang dikirim tidak valid"
          break
        case 401:
          errorTitle = "Tidak Terotorisasi"
          errorMessage = "Sesi Anda telah berakhir, silakan login kembali"
          break
        case 403:
          errorTitle = "Aks<PERSON>"
          errorMessage = "Anda tidak memiliki izin untuk melakukan aksi ini"
          break
        case 404:
          errorTitle = "Tidak Ditemukan"
          errorMessage = "Data yang dicari tidak ditemukan"
          break
        case 422:
          errorTitle = "Validasi Gagal"
          errorMessage = error.message || "Data tidak memenuhi kriteria validasi"
          break
        case 429:
          errorTitle = "Terlalu Banyak Permintaan"
          errorMessage = "Silakan tunggu beberapa saat sebelum mencoba lagi"
          break
        case 500:
          errorTitle = "Error Server"
          errorMessage = "Terjadi kesalahan pada server, silakan coba lagi nanti"
          break
        case 503:
          errorTitle = "Layanan Tidak Tersedia"
          errorMessage = "Layanan sedang dalam pemeliharaan"
          break
        default:
          if (error.status >= 500) {
            errorTitle = "Error Server"
            errorMessage = "Terjadi kesalahan pada server"
          }
      }
    }
    
    // Handle network errors
    if (error?.name === 'NetworkError' || error?.message?.includes('fetch')) {
      errorTitle = "Koneksi Bermasalah"
      errorMessage = "Periksa koneksi internet Anda dan coba lagi"
    }
    
    // Show toast notification
    toast({
      variant: "destructive",
      title: errorTitle,
      description: errorMessage,
    })
  }
  
  static handleSuccess(message: string, title?: string): void {
    toast({
      variant: "success",
      title: title || "Berhasil",
      description: message,
    })
  }
  
  static handleWarning(message: string, title?: string): void {
    toast({
      variant: "warning",
      title: title || "Peringatan",
      description: message,
    })
  }
  
  static handleInfo(message: string, title?: string): void {
    toast({
      variant: "default",
      title: title || "Informasi",
      description: message,
    })
  }
}

// Wrapper for async functions with error handling
export async function withErrorHandling<T>(
  asyncFn: () => Promise<T>,
  context?: string,
  showSuccessMessage?: string
): Promise<T | null> {
  try {
    const result = await asyncFn()
    
    if (showSuccessMessage) {
      ErrorHandler.handleSuccess(showSuccessMessage)
    }
    
    return result
  } catch (error) {
    ErrorHandler.handle(error, context)
    return null
  }
}

// API request wrapper with error handling
export async function apiRequest<T>(
  url: string,
  options?: RequestInit,
  context?: string
): Promise<T | null> {
  return withErrorHandling(async () => {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      ...options,
    })
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw {
        status: response.status,
        message: errorData.message || `HTTP ${response.status}: ${response.statusText}`,
        details: errorData,
      }
    }
    
    return response.json()
  }, context || `API Request to ${url}`)
}
