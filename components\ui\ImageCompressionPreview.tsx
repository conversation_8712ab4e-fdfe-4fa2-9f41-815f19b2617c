"use client";

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ImageHelpers } from '@/lib/utils/imageHelpers';
import { useImageCompressionWithPreview, CompressedImageResult, CompressionStatistics } from '@/hooks/useImageCompression';
import { Loader2, Download, Eye, EyeOff, FileImage, Zap } from 'lucide-react';

interface ImageCompressionPreviewProps {
  files: File[];
  onCompressionComplete?: (compressedImages: CompressedImageResult[]) => void;
  onError?: (error: string) => void;
  className?: string;
}

export default function ImageCompressionPreview({
  files,
  onCompressionComplete,
  onError,
  className = ""
}: ImageCompressionPreviewProps) {
  const {
    compressWithPreview,
    clearPreviews,
    previews,
    loading,
    error,
    statistics
  } = useImageCompressionWithPreview();

  const [showPreviews, setShowPreviews] = useState(false);
  const [compressedResults, setCompressedResults] = useState<CompressedImageResult[]>([]);

  const handleCompress = async () => {
    try {
      const results = await compressWithPreview(files);
      setCompressedResults(results);
      setShowPreviews(true);
      onCompressionComplete?.(results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Compression failed';
      onError?.(errorMessage);
    }
  };

  const handleClear = () => {
    clearPreviews();
    setShowPreviews(false);
    setCompressedResults([]);
  };

  const downloadCompressedImage = (base64: string, index: number) => {
    const link = document.createElement('a');
    link.href = base64;
    link.download = `compressed_image_${index + 1}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (files.length === 0) {
    return (
      <div className={`text-center py-8 text-memorial-400 ${className}`}>
        <FileImage className="h-12 w-12 mx-auto mb-2 opacity-50" />
        <p>No images selected for compression</p>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Compression Controls */}
      <Card className="bg-memorial-800 border-memorial-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-memorial-50">
            <Zap className="h-5 w-5 text-candle-500" />
            Image Compression
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* File Summary */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-memorial-400">Files selected:</span>
              <span className="ml-2 font-medium text-memorial-50">{files.length}</span>
            </div>
            <div>
              <span className="text-memorial-400">Total size:</span>
              <span className="ml-2 font-medium text-memorial-50">
                {ImageHelpers.formatFileSize(files.reduce((sum, file) => sum + file.size, 0))}
              </span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button
              onClick={handleCompress}
              disabled={loading}
              className="bg-candle-500 hover:bg-candle-400 text-memorial-950"
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Compressing...
                </>
              ) : (
                <>
                  <Zap className="h-4 w-4 mr-2" />
                  Compress Images
                </>
              )}
            </Button>

            {previews.length > 0 && (
              <>
                <Button
                  onClick={() => setShowPreviews(!showPreviews)}
                  variant="outline"
                  className="border-memorial-600 text-memorial-300 hover:bg-memorial-700"
                >
                  {showPreviews ? (
                    <>
                      <EyeOff className="h-4 w-4 mr-2" />
                      Hide Preview
                    </>
                  ) : (
                    <>
                      <Eye className="h-4 w-4 mr-2" />
                      Show Preview
                    </>
                  )}
                </Button>

                <Button
                  onClick={handleClear}
                  variant="outline"
                  className="border-memorial-600 text-memorial-300 hover:bg-memorial-700"
                >
                  Clear
                </Button>
              </>
            )}
          </div>

          {/* Error Display */}
          {error && (
            <div className="p-3 bg-red-900/30 border border-red-800 rounded-lg">
              <p className="text-sm text-red-200">{error}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Compression Statistics */}
      {statistics && (
        <Card className="bg-memorial-800 border-memorial-700">
          <CardHeader>
            <CardTitle className="text-memorial-50">Compression Results</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="text-center">
                <div className="text-2xl font-bold text-candle-500">
                  {statistics.averageCompressionRatio.toFixed(1)}%
                </div>
                <div className="text-memorial-400">Avg. Reduction</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-500">
                  {ImageHelpers.formatFileSize(statistics.totalSizeSaved)}
                </div>
                <div className="text-memorial-400">Size Saved</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-500">
                  {ImageHelpers.formatFileSize(statistics.totalCompressedSize)}
                </div>
                <div className="text-memorial-400">Final Size</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-500">
                  {statistics.processingTime}ms
                </div>
                <div className="text-memorial-400">Process Time</div>
              </div>
            </div>

            {/* Compression Progress Bar */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-memorial-400">Compression Efficiency</span>
                <span className="text-memorial-50">{statistics.averageCompressionRatio.toFixed(1)}%</span>
              </div>
              <div className="w-full bg-memorial-600 rounded-full h-2">
                <div
                  className="bg-candle-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${Math.min(statistics.averageCompressionRatio, 100)}%` }}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Image Previews */}
      {showPreviews && previews.length > 0 && (
        <Card className="bg-memorial-800 border-memorial-700">
          <CardHeader>
            <CardTitle className="text-memorial-50">Before & After Preview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6">
              {previews.map((preview, index) => (
                <div key={index} className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-memorial-50">Image {index + 1}</h4>
                    <div className="flex items-center gap-2">
                      {compressedResults[index] && (
                        <Badge variant="secondary" className="bg-green-900/30 text-green-200">
                          {compressedResults[index].compressionRatio.toFixed(1)}% smaller
                        </Badge>
                      )}
                      <Button
                        size="sm"
                        onClick={() => downloadCompressedImage(preview.compressed, index)}
                        className="bg-candle-500 hover:bg-candle-400 text-memorial-950"
                      >
                        <Download className="h-3 w-3 mr-1" />
                        Download
                      </Button>
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-4">
                    {/* Original */}
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-memorial-400">Original</span>
                        <span className="text-memorial-50">
                          {compressedResults[index] && 
                            ImageHelpers.formatFileSize(compressedResults[index].originalSize)
                          }
                        </span>
                      </div>
                      <div className="relative aspect-video bg-memorial-700 rounded-lg overflow-hidden">
                        <img
                          src={preview.original}
                          alt={`Original ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </div>

                    {/* Compressed */}
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-memorial-400">Compressed</span>
                        <span className="text-memorial-50">
                          {compressedResults[index] && 
                            ImageHelpers.formatFileSize(compressedResults[index].size)
                          }
                        </span>
                      </div>
                      <div className="relative aspect-video bg-memorial-700 rounded-lg overflow-hidden">
                        <img
                          src={preview.compressed}
                          alt={`Compressed ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
