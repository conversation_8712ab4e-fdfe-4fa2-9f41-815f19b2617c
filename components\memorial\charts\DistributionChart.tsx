interface ChartData {
  packageId: string;
  packageName: string;
  count: number;
  percentage: number;
  color: string;
}

interface DistributionChartProps {
  data: ChartData[];
  maxCount: number;
}

export default function DistributionChart({ data, maxCount }: DistributionChartProps) {
  return (
    <div className="flex-1">
      <h4 className="text-lg font-bold text-memorial-50 mb-6">Detail Distribusi</h4>
      <div className="space-y-6">
        {data.map((item) => (
          <div key={item.packageId} className="space-y-3 p-3 rounded-lg bg-memorial-800/30">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-3">
                <div
                  className="w-5 h-5 rounded-full border-2 border-white/20"
                  style={{ backgroundColor: item.color }}
                ></div>
                <span className="text-memorial-100 font-semibold text-base">{item.packageName}</span>
              </div>
              <div className="text-memorial-200 text-base font-bold">
                {item.count} memorial
              </div>
            </div>
            <div className="w-full bg-memorial-900 rounded-full h-4 shadow-inner border border-memorial-700">
              <div
                className="h-4 rounded-full transition-all duration-700 ease-out shadow-sm"
                style={{
                  width: `${(item.count / maxCount) * 100}%`,
                  background: `linear-gradient(90deg, ${item.color}, ${item.color}dd)`,
                  boxShadow: `0 0 8px ${item.color}44`
                }}
              ></div>
            </div>
            <div className="text-right">
              <span className="text-memorial-200 text-base font-semibold">{item.percentage.toFixed(1)}%</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
