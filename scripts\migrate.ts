import { drizzle } from 'drizzle-orm/postgres-js';
import { migrate } from 'drizzle-orm/postgres-js/migrator';
import postgres from 'postgres';
import { religions } from '@/db/schema';
import { env, logEnvironmentStatus } from '@/lib/env';
import { generateReligionSlug } from '@/lib/utils/slug';
import '@/lib/forceEnv'; // Force load environment

// Log environment status
const validation = logEnvironmentStatus();

if (!validation.isValid) {
  console.error('❌ Environment validation failed. Cannot proceed with migration.');
  process.exit(1);
}

// Database connection
const connectionString = env.DATABASE_URL;

if (!connectionString) {
  console.error('❌ DATABASE_URL not found in environment');
  process.exit(1);
}

console.log('🔗 Connection details:');
if (connectionString.includes('supabase.co')) {
  console.log('  Type: Supabase Cloud PostgreSQL');
  const maskedUrl = connectionString.replace(/:([^:@]+)@/, ':***@');
  console.log(`  URL: ${maskedUrl}`);
} else if (connectionString.includes('localhost')) {
  console.log('  Type: Local PostgreSQL');
  console.log(`  URL: ${connectionString.replace(/:([^:@]+)@/, ':***@')}`);
} else {
  console.log('  Type: External PostgreSQL');
  console.log(`  URL: ${connectionString.replace(/:([^:@]+)@/, ':***@')}`);
}
console.log('');

async function main() {
  console.log('🚀 Starting database migration...\n');

  // Create connection
  const migrationClient = postgres(connectionString, { max: 1 });
  const db = drizzle(migrationClient);

  try {
    // Run migrations
    console.log('⚡ Running migrations...');
    await migrate(db, { migrationsFolder: './db/migrations' });
    console.log('✅ Migrations completed successfully\n');

    // Seed religions data
    console.log('🌱 Seeding religions data...');
    const religionData = [
      { name: 'Islam', slug: generateReligionSlug('Islam') },
      { name: 'Kristen', slug: generateReligionSlug('Kristen') },
      { name: 'Katolik', slug: generateReligionSlug('Katolik') },
      { name: 'Hindu', slug: generateReligionSlug('Hindu') },
      { name: 'Buddha', slug: generateReligionSlug('Buddha') },
      { name: 'Konghucu', slug: generateReligionSlug('Konghucu') },
    ];

    await db.insert(religions).values(religionData).onConflictDoNothing();
    console.log('✅ Religions data seeded successfully\n');

    console.log('🎉 Database setup completed successfully!');
    console.log('💡 You can now test the purchase flow in your application.');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await migrationClient.end();
  }
}

// Run if this file is executed directly
if (require.main === module) {
  main();
}

export { main as runMigration };
