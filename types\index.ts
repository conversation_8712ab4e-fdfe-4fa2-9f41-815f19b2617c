// Central type definitions file
// This file exports all types used throughout the application

// Re-export location types
export * from './location';

// Re-export memorial types
export * from './memorial';

// Re-export all database types from Drizzle schema
export type {
  Memorial,
  NewMemorial,
  Location,
  NewLocation,
  Religion,
  NewReligion,
  MemorialImage,
  NewMemorialImage,
  Order,
  NewOrder,
} from '@/db/types';

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface ApiError {
  message: string;
  status: number;
  code?: string;
  details?: any;
}

// Form types
export interface FormValidationError {
  field: string;
  message: string;
}

// Package types (aligned with DetailsStep usage)
export interface PackageDetails {
  id: string;
  name: string;
  price: number;
  duration: string;
  features: string[];
}

// Purchase result type
export interface PurchaseResult {
  memorialId: string;
  memorialSlug: string;
  locationId: string;
  orderId: string;
  packageDetails: PackageDetails;
  imageUrls: string[];
  evidenceImageUrl: string | null;
  pricing: {
    packagePrice: number;
    adminFee: number;
    total: number;
  };
}

// Purchase flow types
export type PurchaseStep = 'biodata' | 'location' | 'details' | 'payment' | 'confirmation';

// Date utility types
export type DateString = string; // ISO date string format
export type TimestampString = string; // ISO datetime string format

// File types
export interface FileUpload {
  file: File;
  preview?: string;
  uploaded?: boolean;
  url?: string;
}

// Search types
export interface SearchFilters {
  name?: string;
  birthDate?: string;
  deathDate?: string;
  religion?: string;
  location?: string;
}

export interface SearchResult<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// Pagination types
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  nextPage: number | null;
  prevPage: number | null;
}

export interface PaginatedApiResponse<T> {
  success: boolean;
  message: string;
  data: T[];
  pagination: PaginationInfo;
}

// Error handling types
export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  timestamp?: Date;
}

// Toast notification types
export interface ToastMessage {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  message: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

// Theme types
export type ThemeMode = 'light' | 'dark' | 'system';

// User types (for future authentication)
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'user' | 'admin';
  createdAt: Date;
  updatedAt: Date;
}

// Configuration types
export interface AppConfig {
  apiUrl: string;
  googleMapsApiKey: string;
  maxFileSize: number;
  allowedFileTypes: string[];
  features: {
    authentication: boolean;
    payments: boolean;
    analytics: boolean;
  };
}
