import { z } from 'zod';

// Helper function untuk validasi file
const validateImageFile = (file: any) => {
  if (!file) return true;
  
  // Validasi tipe file
  const validTypes = ['image/jpeg', 'image/png', 'image/jpg'];
  if (!validTypes.includes(file.type)) {
    return false;
  }
  
  // Validasi ukuran file (max 5MB)
  const maxSize = 5 * 1024 * 1024; // 5MB
  if (file.size > maxSize) {
    return false;
  }
  
  return true;
};

export const createMemorialSchema = z.object({
  name: z.string().min(1, "Nama tidak boleh kosong"),
  birthPlace: z.string().min(1, "Tempat lahir tidak boleh kosong"),
  birthDate: z.string().min(1, "Tanggal lahir tidak boleh kosong"),
  deathDate: z.string().min(1, "Tanggal meninggal tidak boleh kosong"),
  description: z.string().optional(),  // Changed from lifeStory to description
  religionId: z.string().uuid().optional(),
  locationId: z.string().uuid().optional(),
  submittedBy: z.string().min(1, "Nama pengaju tidak boleh kosong"),
  images: z.array(
    z.any()
      .refine(file => validateImageFile(file), {
        message: "File harus berupa gambar (JPG/PNG) dengan ukuran maksimal 5MB"
      })
  ).optional(),
  // Tambahan untuk bukti kematian
  evidenceName: z.string().min(1, "Nama bukti kematian tidak boleh kosong"),
  evidenceImage: z.any()
    .refine(file => !file || validateImageFile(file), {
      message: "File bukti harus berupa gambar (JPG/PNG) dengan ukuran maksimal 5MB"
    })
    .nullable()
    .optional()
});

export const updateMemorialSchema = createMemorialSchema.partial();

export type CreateMemorialInput = z.infer<typeof createMemorialSchema>;
export type UpdateMemorialInput = z.infer<typeof updateMemorialSchema>;

