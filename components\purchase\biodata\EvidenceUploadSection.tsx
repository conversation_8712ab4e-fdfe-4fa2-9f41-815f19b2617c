import { Input } from "@/components/ui/input";
import { Label } from "@radix-ui/react-label";
import { Upload } from "lucide-react";
import { ImageHelpers } from "@/lib/utils/imageHelpers";

interface EvidenceUploadSectionProps {
  evidenceName: string;
  evidenceImage: File | null;
  onEvidenceNameChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onEvidenceImageUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onRemoveEvidenceImage: () => void;
}

export default function EvidenceUploadSection({
  evidenceName,
  evidenceImage,
  onEvidenceNameChange,
  onEvidenceImageUpload,
  onRemoveEvidenceImage
}: EvidenceUploadSectionProps) {
  return (
    <div>
      <Label htmlFor="evidenceName" className="text-sm sm:text-base text-memorial-200 mb-1 block">
        <PERSON><PERSON><PERSON><PERSON>
      </Label>
      <Input
        id="evidenceName"
        name="evidenceName"
        value={evidenceName}
        onChange={onEvidenceNameChange}
        className="bg-memorial-800 border-memorial-700 text-memorial-50 text-sm sm:text-base mb-3"
        placeholder="Contoh: Surat Kematian RS XYZ"
      />

      <Label className="text-sm sm:text-base text-memorial-200 block mb-1 sm:mb-2">
        Foto Bukti Kematian <span className="text-red-500">*</span>
      </Label>
      <div className="flex gap-3 sm:gap-4">
        {evidenceImage && (
          <div className="relative w-24 h-32 sm:w-32 sm:h-40 bg-memorial-800 rounded-md overflow-hidden">
            <img
              src={ImageHelpers.createPreviewUrl(evidenceImage)}
              alt="Evidence Preview"
              className="w-full h-full object-cover"
            />
            <button
              type="button"
              onClick={onRemoveEvidenceImage}
              className="absolute top-1 right-1 bg-memorial-950/80 text-memorial-50 rounded-full p-1 hover:bg-memorial-950 transition-colors"
              aria-label="Remove evidence image"
            >
              ✕
            </button>
          </div>
        )}

        {!evidenceImage && (
          <label className="flex items-center justify-center w-24 h-32 sm:w-32 sm:h-40 bg-memorial-800 border border-dashed border-memorial-600 rounded-md cursor-pointer hover:bg-memorial-700/50 transition-colors">
            <div className="flex flex-col items-center">
              <Upload className="h-5 w-5 sm:h-6 sm:w-6 text-memorial-400 mb-1 sm:mb-2" />
              <span className="text-xs sm:text-sm text-memorial-400">Upload Bukti</span>
            </div>
            <input
              type="file"
              accept={ImageHelpers.getAcceptedFormats()}
              className="hidden"
              onChange={onEvidenceImageUpload}
            />
          </label>
        )}
      </div>
      <p className="text-xs sm:text-sm text-memorial-400 mt-1 sm:mt-2">
        Format: {ImageHelpers.getAcceptedExtensions()}. Ukuran maksimal: {ImageHelpers.formatFileSize(ImageHelpers.getMaxFileSize())}.
      </p>
    </div>
  );
}
