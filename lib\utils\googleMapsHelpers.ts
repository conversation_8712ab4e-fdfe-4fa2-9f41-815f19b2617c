import { LocationSearchResult } from "@/types/location";

export class GoogleMapsHelpers {
  static getMapOptions(location: { lat: number; lng: number }): any {
    return {
      center: location,
      zoom: 15,
      mapTypeId: 'ROADMAP',
      mapTypeControl: true,
      streetViewControl: true,
      fullscreenControl: true,
      styles: [
        {
          featureType: "all",
          elementType: "all",
          stylers: [{ saturation: -100 }]
        }
      ]
    };
  }

  static parseCoordinates(result: LocationSearchResult): { lat: number; lng: number } {
    const { latitude, longitude } = result.lokasi;
    return {
      lat: parseFloat(latitude),
      lng: parseFloat(longitude)
    };
  }

  static createMarkerOptions(location: { lat: number; lng: number }, title: string): any {
    return {
      position: location,
      title: title,
      draggable: true
    };
  }

  static generateGoogleMapsUrl(placeId: string): string {
    return `https://www.google.com/maps/place/?q=place_id:${placeId}`;
  }

  static formatCoordinates(lat: number, lng: number): string {
    return `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
  }

  static isValidCoordinates(lat: number, lng: number): boolean {
    return (
      !isNaN(lat) && 
      !isNaN(lng) && 
      lat >= -90 && 
      lat <= 90 && 
      lng >= -180 && 
      lng <= 180
    );
  }

  static createLocationFromCoordinates(lat: number, lng: number): { lat: number; lng: number } {
    if (!this.isValidCoordinates(lat, lng)) {
      throw new Error('Invalid coordinates provided');
    }
    return { lat, lng };
  }
}
