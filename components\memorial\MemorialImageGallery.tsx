import Image from "next/image";
import { useState, useEffect } from "react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  type CarouselApi,
} from "@/components/ui/carousel";
import { CarouselDots } from "@/components/ui/carousel-dots";
import { cn } from "@/lib/utils";

interface MemorialImageGalleryProps {
  images: string[];
  name: string;
  isMobile: boolean;
}

interface MemorialImageProps {
  image: string;
  name: string;
  index: number;
  className?: string;
}

function MemorialImage({ image, name, index, className }: MemorialImageProps) {
  return (
    <div className={className}>
      <div className="relative aspect-[3/4] w-full">
        <Image
          src={image}
          alt={`${name} - Photo ${index + 1}`}
          fill
          className="object-cover rounded-md"
        />
      </div>
    </div>
  );
}

// Component version that always returns JSX for use in JSX
export function MemorialImageGallery({ images, name, isMobile }: MemorialImageGalleryProps) {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);

  const hasMultipleImages = images.length > 1;

  useEffect(() => {
    if (!api) {
      return;
    }

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  if (isMobile && hasMultipleImages) {
    // Mobile: Use carousel for multiple images with infinite scroll
    return (
      <div className="w-full">
        <Carousel
          setApi={setApi}
          className="w-full"
          opts={{
            align: "start",
            loop: true, // Enable infinite scroll
            dragFree: true, // Enable free dragging
            containScroll: "trimSnaps", // Better scroll behavior
          }}
        >
          <CarouselContent className="-ml-2 md:-ml-4">
            {images.map((image, index) => (
              <CarouselItem key={index} className="pl-2 md:pl-4">
                <div className="relative aspect-[3/4] w-full">
                  <Image
                    src={image}
                    alt={`${name} - Photo ${index + 1}`}
                    fill
                    className="object-cover rounded-lg"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    priority={index === 0}
                  />
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>

          {/* Navigation buttons - only show on mobile if there are multiple images */}
          <CarouselPrevious className="left-2 h-8 w-8 bg-black/50 border-0 text-white hover:bg-black/70" />
          <CarouselNext className="right-2 h-8 w-8 bg-black/50 border-0 text-white hover:bg-black/70" />
        </Carousel>

        {/* Dots indicator */}
        <CarouselDots api={api} count={images.length} className="mt-4" />
      </div>
    );
  }

  if (isMobile && !hasMultipleImages) {
    // Mobile: Single image with better mobile styling
    return (
      <div className="w-full">
        <div className="relative aspect-[3/4] w-full">
          <Image
            src={images[0]}
            alt={`${name} - Photo`}
            fill
            className="object-cover rounded-lg"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            priority
          />
        </div>
      </div>
    );
  }

  // Desktop: Return single image (layout will be handled by parent)
  return <MemorialImage image={images[0]} name={name} index={0} className="w-full" />;
}

// Function version that returns object structure for layout control
export function getMemorialImageLayout({ images, name, isMobile }: MemorialImageGalleryProps) {
  const hasTwoImages = images.length > 1;

  if (hasTwoImages && !isMobile) {
    // Desktop with two images: Return both images separately for layout control
    return {
      leftImage: <MemorialImage image={images[0]} name={name} index={0} className="w-full" />,
      rightImage: <MemorialImage image={images[1]} name={name} index={1} className="w-full" />
    };
  }

  // Single image: Return just the first image
  return {
    leftImage: <MemorialImage image={images[0]} name={name} index={0} className="w-full" />,
    rightImage: null
  };
}
