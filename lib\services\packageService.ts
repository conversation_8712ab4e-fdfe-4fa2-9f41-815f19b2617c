/**
 * Package Service
 * Handles package-related operations
 */

export interface PackageData {
  id: string;
  name: string;
  price: number;
  duration: string;
  features: string[];
  isActive: number;
  sortOrder: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface PackageDetails {
  id: string;
  name: string;
  price: number;
  duration: string;
  features: string[];
}

class PackageService {
  private baseUrl = '/api/packages';

  /**
   * Get all active packages
   */
  async getAllPackages(): Promise<PackageData[]> {
    try {
      console.log('📦 Fetching all packages...');

      const response = await fetch(this.baseUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch packages');
      }

      console.log(`✅ Fetched ${result.data.length} packages`);
      return result.data;

    } catch (error) {
      console.error('❌ Error fetching packages:', error);
      throw error;
    }
  }

  /**
   * Get package by ID
   */
  async getPackageById(packageId: string): Promise<PackageData | null> {
    try {
      console.log(`📦 Fetching package: ${packageId}`);

      const response = await fetch(`${this.baseUrl}/${packageId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.status === 404) {
        return null;
      }

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch package');
      }

      console.log(`✅ Fetched package: ${result.data.name}`);
      return result.data;

    } catch (error) {
      console.error(`❌ Error fetching package ${packageId}:`, error);
      throw error;
    }
  }

  /**
   * Convert PackageData to PackageDetails format
   */
  toPackageDetails(packageData: PackageData): PackageDetails {
    return {
      id: packageData.id,
      name: packageData.name,
      price: packageData.price,
      duration: packageData.duration,
      features: packageData.features,
    };
  }

  /**
   * Get packages as PackageDetails format (for compatibility)
   */
  async getPackagesAsDetails(): Promise<Record<string, PackageDetails>> {
    try {
      const packages = await this.getAllPackages();
      const packagesRecord: Record<string, PackageDetails> = {};

      packages.forEach(pkg => {
        packagesRecord[pkg.id] = this.toPackageDetails(pkg);
      });

      return packagesRecord;

    } catch (error) {
      console.error('❌ Error getting packages as details:', error);
      throw error;
    }
  }

  /**
   * Format price for display
   */
  formatPrice(price: number): string {
    if (price === 0) return "Gratis";
    return new Intl.NumberFormat('id-ID', { 
      style: 'currency', 
      currency: 'IDR',
      maximumFractionDigits: 0
    }).format(price);
  }

  /**
   * Validate package ID
   */
  isValidPackageId(packageId: string): boolean {
    const validPackages = ['free', 'standard', 'premium'];
    return validPackages.includes(packageId);
  }
}

export const packageService = new PackageService();
