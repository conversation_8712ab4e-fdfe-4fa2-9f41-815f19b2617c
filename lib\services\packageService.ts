import { db } from '@/db';
import { packages } from '@/db/schema';
import { eq, asc } from 'drizzle-orm';
import { ApiError } from '@/utils/ApiError';
import { BaseService, handleServiceError } from '@/lib/services/base/BaseService';

/**
 * Package Service
 * Handles package-related operations using BaseService pattern
 */

export interface PackageData {
  id: string;
  name: string;
  price: number;
  duration: string;
  features: string[];
  isActive: number;
  sortOrder: number;
  createdAt: Date;
  updatedAt: Date;
}

// Compatible interface for existing components
export interface PackageDetails {
  id: string;
  name: string;
  price: number;
  duration: string;
  features: string[];
}

export class PackageService extends BaseService {
  constructor() {
    super('PackageService', {
      enableCache: true,
      cacheTimeout: 10 * 60 * 1000, // 10 minutes (packages don't change often)
      enableLogging: true,
    });
  }

  /**
   * Get all active packages (implements BaseService.getAll)
   */
  async getAll(): Promise<PackageData[]> {
    return this.executeOperation(
      async () => {
        this.log('Fetching all active packages from database');

        const packagesData = await db
          .select()
          .from(packages)
          .where(eq(packages.isActive, 1))
          .orderBy(asc(packages.sortOrder));

        // Transform features from JSON string to array
        const transformedPackages = packagesData.map(pkg => ({
          ...pkg,
          features: JSON.parse(pkg.features)
        }));

        this.log(`Found ${transformedPackages.length} active packages`);
        return transformedPackages;
      },
      'getAll',
      'all-packages'
    );
  }

  /**
   * Get all active packages (alias for compatibility)
   */
  async getAllPackages(): Promise<PackageData[]> {
    return this.getAll();
  }

  /**
   * Get package by ID (implements BaseService.getById)
   */
  async getById(id: string): Promise<PackageData | null> {
    return this.executeOperation(
      async () => {
        this.log(`Fetching package with ID: ${id}`);

        const packageData = await db
          .select()
          .from(packages)
          .where(eq(packages.id, id))
          .limit(1);

        if (packageData.length === 0) {
          this.log(`Package not found: ${id}`);
          return null;
        }

        // Transform features from JSON string to array
        const transformedPackage = {
          ...packageData[0],
          features: JSON.parse(packageData[0].features)
        };

        this.log(`Found package: ${transformedPackage.name}`);
        return transformedPackage;
      },
      'getById',
      `package-${id}`
    );
  }

  /**
   * Get package by ID (alias for compatibility)
   */
  async getPackageById(packageId: string): Promise<PackageData | null> {
    return this.getById(packageId);
  }

  /**
   * Convert PackageData to PackageDetails format
   */
  toPackageDetails(packageData: PackageData): PackageDetails {
    return {
      id: packageData.id,
      name: packageData.name,
      price: packageData.price,
      duration: packageData.duration,
      features: packageData.features,
    };
  }

  /**
   * Get packages as PackageDetails format (for compatibility)
   */
  async getPackagesAsDetails(): Promise<Record<string, PackageDetails>> {
    return this.executeOperation(
      async () => {
        const packages = await this.getAllPackages();
        const packagesRecord: Record<string, PackageDetails> = {};

        packages.forEach(pkg => {
          packagesRecord[pkg.id] = this.toPackageDetails(pkg);
        });

        this.log(`Converted ${packages.length} packages to details format`);
        return packagesRecord;
      },
      'getPackagesAsDetails',
      'packages-as-details'
    );
  }

  /**
   * Format price for display
   */
  formatPrice(price: number): string {
    if (price === 0) return "Gratis";
    return new Intl.NumberFormat('id-ID', { 
      style: 'currency', 
      currency: 'IDR',
      maximumFractionDigits: 0
    }).format(price);
  }

  /**
   * Validate package ID (checks if package exists in database)
   */
  async isValidPackageId(packageId: string): Promise<boolean> {
    try {
      const packageData = await this.getById(packageId);
      return packageData !== null;
    } catch (error) {
      this.log(`Error validating package ID ${packageId}: ${error}`);
      return false;
    }
  }

  /**
   * Get default packages for fallback (static data)
   */
  getDefaultPackages(): Record<string, PackageDetails> {
    return {
      free: {
        id: 'free',
        name: 'Paket Free',
        price: 0,
        duration: 'Selamanya',
        features: ['1 foto memorial', 'Informasi dasar almarhum', 'Akses publik']
      },
      standard: {
        id: 'standard',
        name: 'Paket Standard',
        price: 50000,
        duration: '1 tahun',
        features: ['2 foto memorial', 'Informasi lengkap almarhum', 'Lokasi pemakaman', 'Akses publik']
      },
      premium: {
        id: 'premium',
        name: 'Paket Premium',
        price: 100000,
        duration: '5 tahun',
        features: ['Foto unlimited', 'Video memorial', 'Informasi lengkap almarhum', 'Lokasi pemakaman', 'Akses publik', 'Backup data']
      }
    };
  }
}

// Export singleton instance
export const packageService = new PackageService();

export const packageService = new PackageService();
