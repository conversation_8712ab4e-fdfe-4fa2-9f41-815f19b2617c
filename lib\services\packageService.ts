// Client-side package service - no database imports

/**
 * Package Service
 * Handles package-related operations using BaseService pattern
 */

export interface PackageData {
  id: string;
  name: string;
  price: number;
  duration: string;
  features: string[];
  isActive: number;
  sortOrder: number;
  createdAt: Date;
  updatedAt: Date;
}

// Compatible interface for existing components
export interface PackageDetails {
  id: string;
  name: string;
  price: number;
  duration: string;
  features: string[];
}

export class PackageService {
  private baseUrl = '/api/packages';
  private cache = new Map<string, { data: any; timestamp: number }>();
  private cacheTimeout = 10 * 60 * 1000; // 10 minutes

  /**
   * Get from cache if available and not expired
   */
  private getFromCache<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (!cached) return null;

    const isExpired = Date.now() - cached.timestamp > this.cacheTimeout;
    if (isExpired) {
      this.cache.delete(key);
      return null;
    }

    console.log(`[PackageService] Cache hit for key: ${key}`);
    return cached.data;
  }

  /**
   * Set cache
   */
  private setCache<T>(key: string, data: T): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    });
    console.log(`[PackageService] Cache set for key: ${key}`);
  }

  /**
   * Get all active packages
   */
  async getAll(): Promise<PackageData[]> {
    const cacheKey = 'all-packages';
    const cached = this.getFromCache<PackageData[]>(cacheKey);
    if (cached) return cached;

    try {
      console.log('[PackageService] Fetching all packages from API...');

      const response = await fetch(this.baseUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch packages');
      }

      console.log(`[PackageService] Fetched ${result.data.length} packages`);
      this.setCache(cacheKey, result.data);
      return result.data;

    } catch (error) {
      console.error('[PackageService] Error fetching packages:', error);
      throw error;
    }
  }

  /**
   * Get all active packages (alias for compatibility)
   */
  async getAllPackages(): Promise<PackageData[]> {
    return this.getAll();
  }

  /**
   * Get package by ID
   */
  async getById(id: string): Promise<PackageData | null> {
    const cacheKey = `package-${id}`;
    const cached = this.getFromCache<PackageData>(cacheKey);
    if (cached) return cached;

    try {
      console.log(`[PackageService] Fetching package: ${id}`);

      const response = await fetch(`${this.baseUrl}/${id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.status === 404) {
        return null;
      }

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch package');
      }

      console.log(`[PackageService] Fetched package: ${result.data.name}`);
      this.setCache(cacheKey, result.data);
      return result.data;

    } catch (error) {
      console.error(`[PackageService] Error fetching package ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get package by ID (alias for compatibility)
   */
  async getPackageById(packageId: string): Promise<PackageData | null> {
    return this.getById(packageId);
  }

  /**
   * Convert PackageData to PackageDetails format
   */
  toPackageDetails(packageData: PackageData): PackageDetails {
    return {
      id: packageData.id,
      name: packageData.name,
      price: packageData.price,
      duration: packageData.duration,
      features: packageData.features,
    };
  }

  /**
   * Get packages as PackageDetails format (for compatibility)
   */
  async getPackagesAsDetails(): Promise<Record<string, PackageDetails>> {
    const cacheKey = 'packages-as-details';
    const cached = this.getFromCache<Record<string, PackageDetails>>(cacheKey);
    if (cached) return cached;

    try {
      const packages = await this.getAllPackages();
      const packagesRecord: Record<string, PackageDetails> = {};

      packages.forEach(pkg => {
        packagesRecord[pkg.id] = this.toPackageDetails(pkg);
      });

      console.log(`[PackageService] Converted ${packages.length} packages to details format`);
      this.setCache(cacheKey, packagesRecord);
      return packagesRecord;

    } catch (error) {
      console.error('[PackageService] Error getting packages as details:', error);
      throw error;
    }
  }

  /**
   * Format price for display
   */
  formatPrice(price: number): string {
    if (price === 0) return "Gratis";
    return new Intl.NumberFormat('id-ID', { 
      style: 'currency', 
      currency: 'IDR',
      maximumFractionDigits: 0
    }).format(price);
  }

  /**
   * Validate package ID (checks if package exists in database)
   */
  async isValidPackageId(packageId: string): Promise<boolean> {
    try {
      const packageData = await this.getById(packageId);
      return packageData !== null;
    } catch (error) {
      console.error(`[PackageService] Error validating package ID ${packageId}:`, error);
      return false;
    }
  }

  /**
   * Get default packages for fallback (static data)
   */
  getDefaultPackages(): Record<string, PackageDetails> {
    return {
      free: {
        id: 'free',
        name: 'Paket Free',
        price: 0,
        duration: 'Selamanya',
        features: ['1 foto memorial', 'Informasi dasar almarhum', 'Akses publik']
      },
      standard: {
        id: 'standard',
        name: 'Paket Standard',
        price: 50000,
        duration: '1 tahun',
        features: ['2 foto memorial', 'Informasi lengkap almarhum', 'Lokasi pemakaman', 'Akses publik']
      },
      premium: {
        id: 'premium',
        name: 'Paket Premium',
        price: 100000,
        duration: '5 tahun',
        features: ['Foto unlimited', 'Video memorial', 'Informasi lengkap almarhum', 'Lokasi pemakaman', 'Akses publik', 'Backup data']
      }
    };
  }
}

// Export singleton instance
export const packageService = new PackageService();
