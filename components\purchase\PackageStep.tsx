import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "../ui/badge";
import { PackageDetails } from "@/types";
import { PriceHelper } from "@/lib/utils/priceHelpers";

interface PackageStepProps {
  initialPackage?: string;
  onProceedToPayment: (packageDetails: PackageDetails) => void;
  packages: Record<string, PackageDetails>;
  loading: boolean;
}

export default function PackageStep({
  initialPackage,
  onProceedToPayment,
  packages,
  loading
}: PackageStepProps) {
  const [selectedPackage, setSelectedPackage] = useState<string>(initialPackage || "free");

  // Reset selectedPackage when initialPackage changes (e.g., when store is reset)
  useEffect(() => {
    setSelectedPackage(initialPackage || "free");
  }, [initialPackage]);


  const handlePackageSelect = (packageId: string) => {
    setSelectedPackage(packageId);
  };

  const handleProceedToPayment = () => {
    onProceedToPayment(packages[selectedPackage]);
  };



  if (loading) {
    return (
      <div className="bg-memorial-900 p-6 rounded-lg border border-memorial-800">
        <h2 className="text-xl font-semibold mb-4 text-center">Pilih Paket Memorial Digital</h2>
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-candle-500"></div>
          <span className="ml-3 text-memorial-300">Memuat paket...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-memorial-900 p-6 rounded-lg border border-memorial-800">
      <h2 className="text-xl font-semibold mb-4 text-center">Pilih Paket Memorial Digital</h2>
      <p className="text-memorial-300 text-center mb-8">
        Pilih paket yang sesuai dengan kebutuhan Anda. Setelah pembayaran, Anda dapat mengisi data biodata dan upload foto.
      </p>

      {/* Package Selection */}
      <div className="mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-6xl mx-auto">
          {Object.values(packages).map((pkg) => (
            <div
              key={pkg.id}
              className={`bg-memorial-800 p-6 rounded-lg border cursor-pointer transition-all ${
                selectedPackage === pkg.id
                  ? "border-candle-500 ring-2 ring-candle-500"
                  : "border-memorial-700 hover:border-memorial-600"
              }`}
              onClick={() => handlePackageSelect(pkg.id)}
            >
              <div className="flex justify-between items-start mb-2">
                <h4 className={`font-medium text-lg ${selectedPackage === pkg.id ? "text-candle-500" : ""}`}>
                  {pkg.name}
                </h4>
                <Badge variant="outline" className="bg-memorial-700 text-memorial-200">
                  {pkg.duration}
                </Badge>
              </div>
              <p className="text-sm text-memorial-300 mb-3">
                {pkg.id === 'free' && 'Memorial digital gratis dengan masa aktif terbatas'}
                {pkg.id === 'standard' && 'Memorial digital lengkap dengan fitur standar'}
                {pkg.id === 'premium' && 'Memorial digital premium dengan semua fitur'}
              </p>
              <p className={`text-2xl font-bold mb-4 ${pkg.price === 0 ? 'text-green-400' : 'text-candle-500'}`}>
                {PriceHelper.formatPrice(pkg.price)}
              </p>
              <div className="text-sm text-memorial-400">
                <ul className="space-y-2">
                  {pkg.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <span className="text-candle-500 mr-2">✓</span>
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}


        </div>
      </div>
      
      <div className="flex justify-center">
        <Button
          className="bg-candle-500 text-memorial-950 hover:bg-candle-400 px-8"
          onClick={handleProceedToPayment}
          disabled={!packages[selectedPackage]}
        >
          Lanjut ke Pembayaran - {packages[selectedPackage] ? PriceHelper.formatPrice(packages[selectedPackage].price) : 'Loading...'}
        </Button>
      </div>
    </div>
  );
}
