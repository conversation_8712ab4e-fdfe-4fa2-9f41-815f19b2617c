import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "../ui/badge";
import { PackageDetails } from "@/types";

interface PackageStepProps {
  initialPackage?: string;
  onProceedToPayment: (packageDetails: PackageDetails) => void;
}

export default function PackageStep({
  initialPackage,
  onProceedToPayment
}: PackageStepProps) {
  const [selectedPackage, setSelectedPackage] = useState<string>(initialPackage || "free");

  // Reset selectedPackage when initialPackage changes (e.g., when store is reset)
  useEffect(() => {
    setSelectedPackage(initialPackage || "free");
  }, [initialPackage]);

  const packages: Record<string, PackageDetails> = {
    free: {
      id: "free",
      name: "Paket Free",
      price: 0,
      duration: "3 bulan",
      features: [
        "Memorial digital dasar",
        "1 foto",
        "Informasi dasar almarhum/almarhumah",
        "Masa aktif 3 bulan"
      ]
    },
    standard: {
      id: "standard",
      name: "Paket Standar",
      price: 150000,
      duration: "1 tahun",
      features: [
        "Memorial digital lengkap",
        "2 foto",
        "Informasi lengkap almarhum/almarhumah",
        "Masa aktif 1 tahun",
        "Akses ke lokasi pemakaman"
      ]
    },
    premium: {
      id: "premium",
      name: "Paket Premium",
      price: 970000,
      duration: "Selamanya",
      features: [
        "Semua fitur Paket Standar",
        "Coming Soon: Layanan ziarah virtual",
        "Coming Soon: Generate kisah hidup almarhum/almarhumah with AI",
        "Coming Soon: Generate QR code untuk akses langsung ke memorial"
      ]
    }
  };

  const handlePackageSelect = (packageId: string) => {
    setSelectedPackage(packageId);
  };

  const handleProceedToPayment = () => {
    onProceedToPayment(packages[selectedPackage]);
  };

  const formatPrice = (price: number) => {
    if (price === 0) return "Gratis";
    return new Intl.NumberFormat('id-ID', { 
      style: 'currency', 
      currency: 'IDR',
      maximumFractionDigits: 0
    }).format(price);
  };

  return (
    <div className="bg-memorial-900 p-6 rounded-lg border border-memorial-800">
      <h2 className="text-xl font-semibold mb-4 text-center">Pilih Paket Memorial Digital</h2>
      <p className="text-memorial-300 text-center mb-8">
        Pilih paket yang sesuai dengan kebutuhan Anda. Setelah pembayaran, Anda dapat mengisi data biodata dan upload foto.
      </p>
      
      {/* Package Selection */}
      <div className="mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-6xl mx-auto">
          {/* Free Package */}
          <div
            className={`bg-memorial-800 p-6 rounded-lg border cursor-pointer transition-all ${
              selectedPackage === "free"
                ? "border-candle-500 ring-2 ring-candle-500"
                : "border-memorial-700 hover:border-memorial-600"
            }`}
            onClick={() => handlePackageSelect("free")}
          >
            <div className="flex justify-between items-start mb-2">
              <h4 className={`font-medium text-lg ${selectedPackage === "free" ? "text-candle-500" : ""}`}>
                Paket Free
              </h4>
              <Badge variant="outline" className="bg-memorial-700 text-memorial-200">
                3 bulan
              </Badge>
            </div>
            <p className="text-sm text-memorial-300 mb-3">Memorial digital gratis dengan masa aktif 3 bulan</p>
            <p className="text-2xl font-bold mb-4 text-green-400">{formatPrice(packages.free.price)}</p>
            <div className="text-sm text-memorial-400">
              <ul className="space-y-2">
                {packages.free.features.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-candle-500 mr-2">✓</span>
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Standard Package */}
          <div
            className={`bg-memorial-800 p-6 rounded-lg border cursor-pointer transition-all ${
              selectedPackage === "standard"
                ? "border-candle-500 ring-2 ring-candle-500"
                : "border-memorial-700 hover:border-memorial-600"
            }`}
            onClick={() => handlePackageSelect("standard")}
          >
            <div className="flex justify-between items-start mb-2">
              <h4 className={`font-medium text-lg ${selectedPackage === "standard" ? "text-candle-500" : ""}`}>
                Paket Standar
              </h4>
              <Badge variant="outline" className="bg-blue-900 text-blue-200">
                1 tahun
              </Badge>
            </div>
            <p className="text-sm text-memorial-300 mb-3">Memorial digital lengkap dengan masa aktif 1 tahun</p>
            <p className="text-2xl font-bold mb-4 text-blue-400">{formatPrice(packages.standard.price)}</p>
            <div className="text-sm text-memorial-400">
              <ul className="space-y-2">
                {packages.standard.features.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-candle-500 mr-2">✓</span>
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Premium Package */}
          <div
            className={`bg-memorial-800 p-6 rounded-lg border cursor-pointer transition-all relative ${
              selectedPackage === "premium"
                ? "border-candle-500 ring-2 ring-candle-500"
                : "border-memorial-700 hover:border-memorial-600"
            }`}
            onClick={() => handlePackageSelect("premium")}
          >
            <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
              <Badge className="bg-candle-500 text-memorial-950">TERPOPULER</Badge>
            </div>
            <div className="flex justify-between items-start mb-2">
              <h4 className={`font-medium text-lg ${selectedPackage === "premium" ? "text-candle-500" : ""}`}>
                Paket Premium
              </h4>
              <Badge variant="outline" className="bg-candle-900 text-candle-200">
                Selamanya
              </Badge>
            </div>
            <p className="text-sm text-memorial-300 mb-3">Memorial digital premium dengan masa aktif selamanya</p>
            <p className="text-2xl font-bold mb-4 text-candle-400">{formatPrice(packages.premium.price)}</p>
            <div className="text-sm text-memorial-400">
              <ul className="space-y-2">
                {packages.premium.features.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-candle-500 mr-2">✓</span>
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
      
      <div className="flex justify-center">
        <Button
          className="bg-candle-500 text-memorial-950 hover:bg-candle-400 px-8"
          onClick={handleProceedToPayment}
        >
          Lanjut ke Pembayaran - {formatPrice(packages[selectedPackage].price)}
        </Button>
      </div>
    </div>
  );
}
