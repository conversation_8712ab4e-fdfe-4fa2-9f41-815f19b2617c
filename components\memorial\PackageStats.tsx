import { Deceased } from "@/data/deceased";
import DonutChart from "./charts/DonutChart";
import ChartLegend from "./charts/ChartLegend";
import DistributionChart from "./charts/DistributionChart";
import SummaryCards from "./charts/SummaryCards";
import StatisticsCards from "./charts/StatisticsCards";
import { 
  calculatePackageStats, 
  calculateAdditionalStats, 
  prepareChartData, 
  prepareDonutData 
} from "./charts/utils";

interface PackageStatsProps {
  deceasedList: Deceased[];
}

export default function PackageStats({ deceasedList }: PackageStatsProps) {
  const { stats, total } = calculatePackageStats(deceasedList);

  if (total === 0) return null;

  const {
    currentYear,
    recentMemorials,
    averageAge,
    paidPackages,
    conversionRate
  } = calculateAdditionalStats(deceasedList);

  const chartData = prepareChartData(stats, total);
  const maxCount = Math.max(...chartData.map(d => d.count));
  const donutData = prepareDonutData(chartData);

  return (
    <div className="bg-memorial-900 border-2 border-memorial-700 rounded-xl p-8 mb-8 shadow-xl">
      <h3 className="text-2xl font-bold text-white mb-8 text-center">📊 Statistik Paket Memorial</h3>

      <div className="flex flex-col lg:flex-row gap-8">
        {/* Donut Chart Section */}
        <div className="flex flex-col items-center lg:items-start">
          <DonutChart data={donutData} size={160} />
          <ChartLegend data={chartData} />
        </div>

        {/* Distribution Chart Section */}
        <DistributionChart data={chartData} maxCount={maxCount} />

        {/* Summary Cards Section */}
        <SummaryCards total={total} chartData={chartData} />
      </div>

      {/* Additional Statistics */}
      <StatisticsCards
        recentMemorials={recentMemorials}
        averageAge={averageAge}
        paidPackages={paidPackages}
        conversionRate={conversionRate}
        currentYear={currentYear}
      />
    </div>
  );
}
