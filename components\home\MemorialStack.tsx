"use client";
import Stack from "@/components/Stack";
import { useEffect, useState } from "react";

export default function MemorialStack() {
  const [stackDimensions, setStackDimensions] = useState({ width: 280, height: 280 });
  const [isClient, setIsClient] = useState(false);

  // Update stack dimensions based on screen size
  useEffect(() => {
    setIsClient(true);

    const handleResize = () => {
      if (window.innerWidth >= 1024) { // lg breakpoint
        setStackDimensions({ width: 420, height: 420 });
      } else if (window.innerWidth >= 768) { // md breakpoint
        setStackDimensions({ width: 400, height: 400 });
      } else {
        setStackDimensions({ width: 280, height: 280 });
      }
    };

    // Set initial dimensions
    handleResize();

    // Add event listener for window resize
    window.addEventListener('resize', handleResize);

    // Clean up
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <section className="py-8">
      <h2 className="text-2xl md:text-3xl font-bold mb-8 text-center">Memorial Digital</h2>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        {/* Left side: Stack component */}
        <div className="flex justify-center">
          {isClient ? (
            <Stack
              randomRotation={true}
              sensitivity={150}
              cardDimensions={stackDimensions}
              sendToBackOnClick={true}
              cardsData={[
                {
                  id: 1,
                  img: "/images/stack-1.webp",
                },
                {
                  id: 2,
                  img: "/images/stack-2.webp",
                },
                {
                  id: 3,
                  img: "/images/stack-3.webp",
                },
                {
                  id: 4,
                  img: "/images/stack-4.webp",
                },
              ]}
              animationConfig={{ stiffness: 300, damping: 25 }}
            />
          ) : (
            // Placeholder untuk SSR
            <div
              className="relative bg-memorial-800 rounded-2xl animate-pulse"
              style={{
                width: stackDimensions.width,
                height: stackDimensions.height,
              }}
            />
          )}
        </div>
        
        {/* Right side: Description */}
        <div className="p-8 rounded-lg border border-memorial-800 hover:border-candle-500 transition-colors">
          <h3 className="text-2xl font-semibold mb-4 text-memorial-50">
            Abadikan Kenangan <span className="text-candle-500">Orang Tercinta</span>
          </h3>
          
          <p className="text-memorial-300 mb-4">
            Setiap kehidupan memiliki kisah yang berharga untuk dikenang. Pemakaman Digital memungkinkan Anda mengabadikan kenangan dan kisah hidup orang tercinta dalam bentuk memorial digital yang dapat diakses kapan saja.
          </p>
          
          <p className="text-memorial-300 mb-4">
            Bagikan foto, cerita, dan momen berharga yang dapat dilihat oleh keluarga dan teman di seluruh dunia. Buat warisan digital yang akan bertahan selamanya.
          </p>
          
          <div className="space-y-3 mt-6 mb-8">
            <div className="flex items-center">
              <div className="w-2 h-2 rounded-full bg-candle-500 mr-3"></div>
              <p className="text-memorial-200">Galeri foto dan video kenangan</p>
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 rounded-full bg-candle-500 mr-3"></div>
              <p className="text-memorial-200">Riwayat hidup dan pencapaian</p>
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 rounded-full bg-candle-500 mr-3"></div>
              <p className="text-memorial-200">Pesan penghormatan dari keluarga dan teman</p>
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 rounded-full bg-candle-500 mr-3"></div>
              <p className="text-memorial-200">Akses mudah melalui QR code di lokasi pemakaman</p>
            </div>
          </div>
          
        </div>
      </div>
    </section>
  );
}
