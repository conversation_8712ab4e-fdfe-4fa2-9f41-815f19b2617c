"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Modal } from "@/components/ui/modal";

interface ReligionSelectionPopupProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectReligion: (religion: string) => void;
}

export default function ReligionSelectionPopup({ 
  isOpen, 
  onClose, 
  onSelectReligion 
}: ReligionSelectionPopupProps) {
  // Define religions as an array for easier maintenance
  const religions = [
    "Islam",
    "Kristen",
    "Katolik",
    "Hindu",
    "Buddha",
    "Konghucu"
  ];

  // Common button styling
  const buttonClassName = "bg-memorial-800 hover:bg-memorial-700 text-memorial-50 border border-memorial-700";

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Pilih Agama Anda">
      <p className="text-memorial-300 mb-6 text-center">
        Untuk memberikan pengalaman yang sesuai dengan keyakinan Anda, silakan pilih agama Anda:
      </p>
      
      <div className="grid grid-cols-2 gap-4">
        {religions.map((religion) => (
          <Button 
            key={religion}
            onClick={() => onSelectReligion(religion)}
            className={buttonClassName}
            variant="outline"
          >
            {religion}
          </Button>
        ))}
      </div>
      
      <p className="text-xs text-memorial-400 mt-6 text-center">
        Anda dapat mengubah pilihan ini nanti di pengaturan profil.
      </p>
    </Modal>
  );
}
