// Location interfaces sesuai dengan database schema dan API response

export interface LocationCoordinates {
  latitude: string;
  longitude: string;
}

export interface LocationSearchResult {
  provinsi: string;
  kabupaten_kota: string;
  kecamatan: string;
  kelurahan: string;
  nama_lengkap: string;
  lokasi: LocationCoordinates;
  formatted_address: string;
  place_id: string;
  // Additional fields for storing IDs
  provinsi_id?: string;
  kabupaten_kota_id?: string;
  kecamatan_id?: string;
  kelurahan_id?: string;
  alamat_detail?: string;
  // Database compatibility
  name?: string;
  addressDetail?: string;
  province?: string;
  city?: string;
  district?: string;
  subDistrict?: string;
  latitude?: string;
  longitude?: string;
  placeId?: string;
}

export interface LocationFormData {
  provinsi: string;
  kabupaten_kota: string;
  kecamatan: string;
  kelurahan: string;
  alamat_detail: string;
}

export interface LocationSearchRequest {
  provinsi: string;
  kabupaten_kota: string;
  kecamatan: string;
  kelurahan: string;
  alamat_lengkap: string;
}

// Database location model (sesuai dengan schema.ts)
export interface DatabaseLocation {
  id: string;
  name: string;
  addressDetail: string;
  province: string;
  city: string;
  district: string;
  subDistrict: string;
  latitude: string;
  longitude: string;
  placeId?: string;
  createdAt: Date;
}

// Region interfaces (dari regionService)
export interface Province {
  id: string;
  name: string;
}

export interface Regency {
  id: string;
  name: string;
  province_id: string;
}

export interface District {
  id: string;
  name: string;
  regency_id: string;
}

export interface Village {
  id: string;
  name: string;
  district_id: string;
}
