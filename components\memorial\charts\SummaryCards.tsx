interface ChartData {
  packageId: string;
  packageName: string;
  count: number;
  percentage: number;
  color: string;
}

interface SummaryCardsProps {
  total: number;
  chartData: ChartData[];
}

export default function SummaryCards({ total, chartData }: SummaryCardsProps) {
  return (
    <div className="lg:w-80">
      <h4 className="text-lg font-bold text-memorial-50 mb-6">Ringkasan Statistik</h4>
      <div className="space-y-4">
        {/* Total Memorial Card */}
        <div className="bg-gradient-to-r from-memorial-700 to-memorial-600 rounded-lg p-6 text-center border-2 border-memorial-500 shadow-lg">
          <div className="text-4xl font-bold text-white mb-2">{total}</div>
          <div className="text-memorial-100 text-base font-semibold">Total Memorial</div>
        </div>

        {/* Package Cards - Enhanced readability */}
        {chartData.map((item, index) => (
          <div key={item.packageId} className="bg-memorial-800 rounded-lg p-5 border-2 border-memorial-600 hover:border-memorial-500 transition-colors shadow-lg">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-3">
                <div
                  className="w-5 h-5 rounded-full border-2 border-white/20"
                  style={{ backgroundColor: item.color }}
                ></div>
                <span className="text-memorial-100 font-semibold text-base">{item.packageName}</span>
              </div>
              <div className="text-white font-bold text-2xl">{item.count}</div>
            </div>
            <div className="flex justify-between items-center mb-3">
              <div className="text-memorial-300 text-sm font-medium">
                {index === 0 ? '🏆 Terpopuler' :
                 index === 1 ? '📈 Populer' :
                 '📊 Tersedia'}
              </div>
              <div className="text-memorial-100 text-base font-bold">
                {item.percentage.toFixed(1)}%
              </div>
            </div>
            {/* Progress bar */}
            <div className="w-full bg-memorial-900 rounded-full h-3 shadow-inner border border-memorial-700">
              <div
                className="h-3 rounded-full transition-all duration-700 ease-out"
                style={{
                  width: `${item.percentage}%`,
                  background: `linear-gradient(90deg, ${item.color}, ${item.color}cc)`,
                  boxShadow: `0 0 6px ${item.color}66`
                }}
              ></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
