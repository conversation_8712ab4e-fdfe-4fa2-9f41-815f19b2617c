import { User, MapPin, CreditCard, Check, ShoppingCart, FileText } from "lucide-react";
import { Fragment } from "react";

interface PurchaseStepperProps {
  currentStep: 'package' | 'payment' | 'biodata' | 'location' | 'details' | 'confirmation';
}

export default function PurchaseStepper({ currentStep }: PurchaseStepperProps) {
  // Define steps for easier management
  const steps = [
    { id: 'package', label: 'Pilih Paket', icon: <ShoppingCart className="h-4 w-4 md:h-5 md:w-5" /> },
    { id: 'payment', label: 'Pembayaran', icon: <CreditCard className="h-4 w-4 md:h-5 md:w-5" /> },
    { id: 'biodata', label: 'Biodata & Foto', icon: <User className="h-4 w-4 md:h-5 md:w-5" /> },
    { id: 'location', label: 'Lokasi', icon: <MapPin className="h-4 w-4 md:h-5 md:w-5" /> },
    { id: 'details', label: 'Review Data', icon: <FileText className="h-4 w-4 md:h-5 md:w-5" /> },
    { id: 'confirmation', label: 'Selesai', icon: <Check className="h-4 w-4 md:h-5 md:w-5" /> }
  ];

  // Helper function to determine if a step is active or completed
  const isStepActiveOrCompleted = (stepId: string) => {
    const stepIndex = steps.findIndex(step => step.id === stepId);
    const currentIndex = steps.findIndex(step => step.id === currentStep);
    return currentIndex >= stepIndex;
  };

  return (
    <div className="mb-8 md:mb-12">
      <div className="hidden md:flex justify-between">
        {steps.map((step, index) => (
          <Fragment key={step.id}>
            {/* Step circle and label */}
            <div className="flex flex-col items-center">
              <div className={`w-10 h-10 rounded-full flex items-center justify-center 
                ${currentStep === step.id ? 'bg-candle-500 text-memorial-950' : 
                  isStepActiveOrCompleted(step.id) ? 'bg-candle-500 text-memorial-950' : 'bg-memorial-800 text-memorial-50'}`}>
                {step.icon}
              </div>
              <span className="text-sm mt-2">{step.label}</span>
            </div>
            
            {/* Connector line (except after last step) */}
            {index < steps.length - 1 && (
              <div className="flex-1 self-center mx-2 flex justify-center">
                <div className="flex space-x-2">
                  {[1, 2, 3, 4].map((dot) => (
                    <div 
                      key={dot} 
                      className={`w-2 h-2 rounded-full
                        ${isStepActiveOrCompleted(steps[index + 1].id) ? 'bg-candle-500' : 'bg-memorial-800'}`}
                    />
                  ))}
                </div>
              </div>
            )}
          </Fragment>
        ))}
      </div>
    </div>
  );
}






