'use client'

import { useEffect } from 'react'

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Global error:', error)
    
    // You can also send the error to your analytics or monitoring service
    // Example: sendToErrorMonitoring(error)
  }, [error])

  return (
    <html>
      <body>
        <div className="min-h-screen flex items-center justify-center bg-gray-100">
          <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
            <h2 className="text-2xl font-bold text-red-600 mb-4"><PERSON><PERSON><PERSON><PERSON></h2>
            <p className="text-gray-700 mb-6">
              <PERSON><PERSON>, terjadi kesalahan pada aplikasi. Tim kami telah diberitahu dan sedang menyelesaikan masalah ini.
            </p>
            <button
              onClick={() => reset()}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded transition duration-200"
            >
              <PERSON>ba <PERSON>
            </button>
          </div>
        </div>
      </body>
    </html>
  )
}