import { useState } from "react";

interface DonutChartProps {
  data: Array<{ name: string; value: number; color: string; percentage: number }>;
  size?: number;
}

export default function DonutChart({ data, size = 120 }: DonutChartProps) {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const center = size / 2;
  const radius = size / 2 - 12;
  const circumference = 2 * Math.PI * radius;

  let cumulativePercentage = 0;

  return (
    <div className="relative group" style={{ width: size, height: size }}>
      <svg width={size} height={size} className="transform -rotate-90">
        {/* Gradient definitions - High contrast for elderly users */}
        <defs>
          <linearGradient id="freeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#047857" />
            <stop offset="100%" stopColor="#065F46" />
          </linearGradient>
          <linearGradient id="standardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#1E40AF" />
            <stop offset="100%" stopColor="#1E3A8A" />
          </linearGradient>
          <linearGradient id="premiumGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#B45309" />
            <stop offset="100%" stopColor="#92400E" />
          </linearGradient>
        </defs>
        
        {/* Background circle - High contrast for better visibility */}
        <circle
          cx={center}
          cy={center}
          r={radius}
          fill="none"
          stroke="#111827"
          strokeWidth="8"
        />

        {/* Data segments */}
        {data.map((item, index) => {
          const strokeDasharray = `${(item.percentage / 100) * circumference} ${circumference}`;
          const strokeDashoffset = -cumulativePercentage * circumference / 100;
          const isHovered = hoveredIndex === index;
          const strokeWidth = isHovered ? "12" : "8";

          // Get gradient URL based on package name
          const getStrokeColor = (name: string) => {
            if (name.toLowerCase().includes('free')) return 'url(#freeGradient)';
            if (name.toLowerCase().includes('standar')) return 'url(#standardGradient)';
            if (name.toLowerCase().includes('premium')) return 'url(#premiumGradient)';
            return item.color;
          };

          cumulativePercentage += item.percentage;

          return (
            <circle
              key={index}
              cx={center}
              cy={center}
              r={radius}
              fill="none"
              stroke={getStrokeColor(item.name)}
              strokeWidth={strokeWidth}
              strokeDasharray={strokeDasharray}
              strokeDashoffset={strokeDashoffset}
              className="transition-all duration-300 cursor-pointer hover:opacity-90"
              onMouseEnter={() => setHoveredIndex(index)}
              onMouseLeave={() => setHoveredIndex(null)}
              style={{
                filter: isHovered ? 'drop-shadow(0 0 8px rgba(255,255,255,0.4))' : 'drop-shadow(0 0 2px rgba(0,0,0,0.3))'
              }}
            />
          );
        })}
      </svg>

      {/* Center text */}
      <div className="absolute inset-0 flex flex-col items-center justify-center pointer-events-none">
        {hoveredIndex !== null ? (
          <>
            <div className="text-lg font-bold text-memorial-50">
              {data[hoveredIndex].value}
            </div>
            <div className="text-xs text-memorial-300 text-center">
              {data[hoveredIndex].name}
            </div>
            <div className="text-xs text-memorial-400">
              {data[hoveredIndex].percentage.toFixed(1)}%
            </div>
          </>
        ) : (
          <>
            <div className="text-xl font-bold text-memorial-50">
              {data.reduce((sum, item) => sum + item.value, 0)}
            </div>
            <div className="text-xs text-memorial-400">Total</div>
          </>
        )}
      </div>

      {/* Hover tooltip */}
      {hoveredIndex !== null && (
        <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-memorial-800 text-memorial-50 px-2 py-1 rounded text-xs whitespace-nowrap border border-memorial-600 shadow-lg">
          {data[hoveredIndex].name}: {data[hoveredIndex].value} memorial
        </div>
      )}
    </div>
  );
}
