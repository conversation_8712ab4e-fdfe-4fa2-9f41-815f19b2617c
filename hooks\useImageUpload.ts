import { useState, useCallback } from "react";
import { ImageHelpers } from "@/lib/utils/imageHelpers";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/errorHandler";
import { useImageCompression, CompressedImageResult } from "./useImageCompression";

interface UseImageUploadProps {
  maxImages?: number;
  onImagesChange?: (images: File[]) => void;
  onEvidenceImageChange?: (image: File | null) => void;
  enableCompression?: boolean; // New option to enable/disable compression
}

export function useImageUpload({
  maxImages = 2,
  onImagesChange,
  onEvidenceImageChange,
  enableCompression = true // Enable compression by default
}: UseImageUploadProps = {}) {
  const [images, setImages] = useState<File[]>([]);
  const [evidenceImage, setEvidenceImage] = useState<File | null>(null);
  const [compressedImages, setCompressedImages] = useState<CompressedImageResult[]>([]);
  const [compressedEvidenceImage, setCompressedEvidenceImage] = useState<CompressedImageResult | null>(null);

  // Use compression hook
  const { compressImages, loading: compressionLoading } = useImageCompression();

  // Handle multiple image upload with compression
  const handleImageUpload = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newImages = Array.from(e.target.files);

      // Validate each image
      for (const image of newImages) {
        const validation = ImageHelpers.validateImageFile(image);
        if (!validation.isValid) {
          ErrorHandler.handle(validation.error || "Invalid image file", "Image Upload");
          return;
        }
      }

      // Limit to maximum images
      const limitedImages = ImageHelpers.limitImages(images, newImages, maxImages);

      // Store original files
      setImages(limitedImages);
      onImagesChange?.(limitedImages);

      // Compress images if enabled
      if (enableCompression && limitedImages.length > 0) {
        try {
          console.log(`🔄 Compressing ${limitedImages.length} memorial image(s)...`);
          const compressed = await compressImages(limitedImages);
          console.log('✅ Memorial compression result:', compressed);

          if (compressed && compressed.length > 0) {
            setCompressedImages(compressed);

            // Calculate total savings using API data
            const totalSizeSaved = compressed.reduce((sum, img) => sum + img.sizeSaved, 0);
            const totalOriginalSize = compressed.reduce((sum, img) => sum + img.originalSize, 0);
            const averageCompressionRatio = compressed.reduce((sum, img) => sum + img.compressionRatio, 0) / compressed.length;

            console.log('📊 Memorial compression stats:', {
              totalOriginalSize,
              totalSizeSaved,
              averageCompressionRatio,
              imagesCount: compressed.length
            });

            ErrorHandler.handleSuccess(
              `✅ ${compressed.length} gambar memorial berhasil dikompresi! Ukuran berkurang ${averageCompressionRatio.toFixed(1)}% (${ImageHelpers.formatFileSize(totalSizeSaved)} dihemat)`
            );
          } else {
            console.warn('⚠️ No compressed memorial images returned');
            ErrorHandler.handle('Tidak ada gambar memorial yang berhasil dikompresi', 'Image Compression');
          }

        } catch (error) {
          console.error('❌ Memorial image compression error:', error);
          ErrorHandler.handle('Kompresi gambar gagal, menggunakan gambar asli', 'Image Compression');
        }
      }
    }
  }, [images, maxImages, onImagesChange, enableCompression, compressImages]);

  // Handle evidence image upload with compression
  const handleEvidenceImageUpload = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];

      // Validate image
      const validation = ImageHelpers.validateImageFile(file);
      if (!validation.isValid) {
        ErrorHandler.handle(validation.error || "Invalid image file", "Evidence Upload");
        return;
      }

      // Store original file
      setEvidenceImage(file);
      onEvidenceImageChange?.(file);

      // Compress evidence image if enabled
      if (enableCompression) {
        try {
          console.log('🔄 Compressing evidence image...');
          const compressed = await compressImages([file]);
          console.log('✅ Evidence compression result:', compressed);

          if (compressed && compressed.length > 0) {
            const compressedImage = compressed[0];
            console.log('📊 Compressed image data:', {
              originalSize: file.size,
              compressedSize: compressedImage.size,
              format: compressedImage.format,
              compressionRatio: compressedImage.compressionRatio
            });

            setCompressedEvidenceImage(compressedImage);

            // Show compression success message using the compressionRatio from API
            ErrorHandler.handleSuccess(
              `✅ Gambar bukti berhasil dikompresi! Ukuran berkurang ${compressedImage.compressionRatio.toFixed(1)}% (${ImageHelpers.formatFileSize(compressedImage.sizeSaved)} dihemat)`
            );
          } else {
            console.warn('⚠️ No compressed images returned');
            ErrorHandler.handle('Tidak ada gambar yang berhasil dikompresi', 'Image Compression');
          }
        } catch (error) {
          console.error('❌ Evidence image compression error:', error);
          ErrorHandler.handle('Kompresi gambar bukti gagal, menggunakan gambar asli', 'Image Compression');
        }
      }
    }
  }, [onEvidenceImageChange, enableCompression, compressImages, setCompressedEvidenceImage]);

  // Remove image by index
  const handleRemoveImage = useCallback((index: number) => {
    const updatedImages = ImageHelpers.removeImageByIndex(images, index);
    setImages(updatedImages);
    onImagesChange?.(updatedImages);

    // Also remove from compressed images
    const updatedCompressed = compressedImages.filter((_, i) => i !== index);
    setCompressedImages(updatedCompressed);
  }, [images, onImagesChange, compressedImages]);

  // Remove evidence image
  const handleRemoveEvidenceImage = useCallback(() => {
    setEvidenceImage(null);
    onEvidenceImageChange?.(null);
    setCompressedEvidenceImage(null);
  }, [onEvidenceImageChange]);

  // Set images from external source (e.g., form state)
  const setImagesFromExternal = useCallback((newImages: File[]) => {
    setImages(newImages);
  }, []);

  // Set evidence image from external source
  const setEvidenceImageFromExternal = useCallback((newImage: File | null) => {
    setEvidenceImage(newImage);
  }, []);

  // Get preview URL for image
  const getPreviewUrl = useCallback((file: File): string => {
    return ImageHelpers.createPreviewUrl(file);
  }, []);

  // Check if can add more images
  const canAddMoreImages = images.length < maxImages;

  // Get remaining image slots
  const remainingSlots = maxImages - images.length;

  return {
    // Original images (for form submission)
    images,
    evidenceImage,

    // Compressed images (for display/preview)
    compressedImages,
    compressedEvidenceImage,

    // Upload handlers
    handleImageUpload,
    handleEvidenceImageUpload,
    handleRemoveImage,
    handleRemoveEvidenceImage,

    // External setters
    setImagesFromExternal,
    setEvidenceImageFromExternal,

    // Utilities
    getPreviewUrl,
    canAddMoreImages,
    remainingSlots,
    maxImages,

    // Compression state
    compressionLoading,
    enableCompression
  };
}
