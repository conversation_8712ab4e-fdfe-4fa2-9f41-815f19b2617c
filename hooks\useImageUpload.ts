import { useState, useCallback } from "react";
import { ImageHelpers } from "@/lib/utils/imageHelpers";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/errorHandler";
import { useImageCompression, CompressedImageResult } from "./useImageCompression";

interface UseImageUploadProps {
  maxImages?: number;
  onImagesChange?: (images: File[]) => void;
  onEvidenceImageChange?: (image: File | null) => void;
  enableCompression?: boolean; // New option to enable/disable compression
}

export function useImageUpload({
  maxImages = 2,
  onImagesChange,
  onEvidenceImageChange,
  enableCompression = true // Enable compression by default
}: UseImageUploadProps = {}) {
  const [images, setImages] = useState<File[]>([]);
  const [evidenceImage, setEvidenceImage] = useState<File | null>(null);
  const [compressedImages, setCompressedImages] = useState<CompressedImageResult[]>([]);
  const [compressedEvidenceImage, setCompressedEvidenceImage] = useState<CompressedImageResult | null>(null);

  // Use compression hook
  const { compressImages, loading: compressionLoading } = useImageCompression();

  // Handle multiple image upload with compression
  const handleImageUpload = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newImages = Array.from(e.target.files);

      // Validate each image
      for (const image of newImages) {
        const validation = ImageHelpers.validateImageFile(image);
        if (!validation.isValid) {
          ErrorHandler.handle(validation.error || "Invalid image file", "Image Upload");
          return;
        }
      }

      // Limit to maximum images
      const limitedImages = ImageHelpers.limitImages(images, newImages, maxImages);

      // Store original files
      setImages(limitedImages);
      onImagesChange?.(limitedImages);

      // Compress images if enabled
      if (enableCompression && limitedImages.length > 0) {
        try {
          console.log(`🔄 Compressing ${limitedImages.length} memorial image(s)...`);
          const compressed = await compressImages(limitedImages);
          setCompressedImages(compressed);

          // Show compression success message
          const totalOriginalSize = limitedImages.reduce((sum, file) => sum + file.size, 0);
          const totalCompressedSize = compressed.reduce((sum, img) => sum + img.size, 0);
          const compressionRatio = ((totalOriginalSize - totalCompressedSize) / totalOriginalSize) * 100;

          ErrorHandler.handleSuccess(
            `✅ ${compressed.length} gambar memorial berhasil dikompresi! Ukuran berkurang ${compressionRatio.toFixed(1)}% (${ImageHelpers.formatFileSize(totalOriginalSize - totalCompressedSize)} dihemat)`
          );

        } catch (error) {
          console.warn('⚠️ Image compression failed, using original images:', error);
          ErrorHandler.handle('Kompresi gambar gagal, menggunakan gambar asli', 'Image Compression');
        }
      }
    }
  }, [images, maxImages, onImagesChange, enableCompression, compressImages]);

  // Handle evidence image upload with compression
  const handleEvidenceImageUpload = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];

      // Validate image
      const validation = ImageHelpers.validateImageFile(file);
      if (!validation.isValid) {
        ErrorHandler.handle(validation.error || "Invalid image file", "Evidence Upload");
        return;
      }

      // Store original file
      setEvidenceImage(file);
      onEvidenceImageChange?.(file);

      // Compress evidence image if enabled
      if (enableCompression) {
        try {
          console.log('🔄 Compressing evidence image...');
          const compressed = await compressImages([file]);
          if (compressed.length > 0) {
            setCompressedEvidenceImage(compressed[0]);

            // Show compression success message
            const compressionRatio = ((file.size - compressed[0].size) / file.size) * 100;
            ErrorHandler.handleSuccess(
              `✅ Gambar bukti berhasil dikompresi! Ukuran berkurang ${compressionRatio.toFixed(1)}% (${ImageHelpers.formatFileSize(file.size - compressed[0].size)} dihemat)`
            );
          }
        } catch (error) {
          console.warn('⚠️ Evidence image compression failed, using original:', error);
          ErrorHandler.handle('Kompresi gambar bukti gagal, menggunakan gambar asli', 'Image Compression');
        }
      }
    }
  }, [onEvidenceImageChange, enableCompression, compressImages, setCompressedEvidenceImage]);

  // Remove image by index
  const handleRemoveImage = useCallback((index: number) => {
    const updatedImages = ImageHelpers.removeImageByIndex(images, index);
    setImages(updatedImages);
    onImagesChange?.(updatedImages);

    // Also remove from compressed images
    const updatedCompressed = compressedImages.filter((_, i) => i !== index);
    setCompressedImages(updatedCompressed);
  }, [images, onImagesChange, compressedImages]);

  // Remove evidence image
  const handleRemoveEvidenceImage = useCallback(() => {
    setEvidenceImage(null);
    onEvidenceImageChange?.(null);
    setCompressedEvidenceImage(null);
  }, [onEvidenceImageChange]);

  // Set images from external source (e.g., form state)
  const setImagesFromExternal = useCallback((newImages: File[]) => {
    setImages(newImages);
  }, []);

  // Set evidence image from external source
  const setEvidenceImageFromExternal = useCallback((newImage: File | null) => {
    setEvidenceImage(newImage);
  }, []);

  // Get preview URL for image
  const getPreviewUrl = useCallback((file: File): string => {
    return ImageHelpers.createPreviewUrl(file);
  }, []);

  // Check if can add more images
  const canAddMoreImages = images.length < maxImages;

  // Get remaining image slots
  const remainingSlots = maxImages - images.length;

  return {
    // Original images (for form submission)
    images,
    evidenceImage,

    // Compressed images (for display/preview)
    compressedImages,
    compressedEvidenceImage,

    // Upload handlers
    handleImageUpload,
    handleEvidenceImageUpload,
    handleRemoveImage,
    handleRemoveEvidenceImage,

    // External setters
    setImagesFromExternal,
    setEvidenceImageFromExternal,

    // Utilities
    getPreviewUrl,
    canAddMoreImages,
    remainingSlots,
    maxImages,

    // Compression state
    compressionLoading,
    enableCompression
  };
}
