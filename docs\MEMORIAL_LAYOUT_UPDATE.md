# Memorial Layout Update

## Overview

Updated memorial card layout to center the bio section between two images when there are multiple images, providing better visual balance and improved readability.

## Layout Changes

### Before (Old Layout)

#### Two Images Desktop
```
┌─────────────────────────────────────────────────────────────┐
│ [Image 1] [Image 2]           [Bio Section]                │
│   1/4       1/4                    2/4                     │
└─────────────────────────────────────────────────────────────┘
```

#### Single Image Desktop
```
┌─────────────────────────────────────────────────────────────┐
│ [Image 1]                    [Bio Section]                 │
│   1/4                           3/4                        │
└─────────────────────────────────────────────────────────────┘
```

### After (New Layout)

#### Two Images Desktop
```
┌─────────────────────────────────────────────────────────────┐
│ [Image 1]        [Bio Section]        [Image 2]           │
│   1/4               2/4                 1/4               │
└─────────────────────────────────────────────────────────────┘
```

#### Single Image Desktop
```
┌─────────────────────────────────────────────────────────────┐
│ [Image 1]                    [Bio Section]                 │
│   1/4                           3/4                        │
└─────────────────────────────────────────────────────────────┘
```

#### Mobile (All Cases)
```
┌─────────────────────────┐
│    [Image Carousel]     │
│       (if multiple)     │
│         or              │
│     [Single Image]      │
├─────────────────────────┤
│     [Bio Section]       │
│      (full width)       │
└─────────────────────────┘
```

## Implementation Details

### 1. MemorialImageGallery Component

#### Updated Return Type
```typescript
// Before: Always returned JSX
return <div>...</div>;

// After: Returns object for desktop, JSX for mobile
if (hasTwoImages && !isMobile) {
  return {
    leftImage: <MemorialImage ... />,
    rightImage: <MemorialImage ... />
  };
}

return {
  leftImage: <MemorialImage ... />,
  rightImage: null
};
```

#### New MemorialImage Component
```typescript
interface MemorialImageProps {
  image: string;
  name: string;
  index: number;
  className?: string;
}

function MemorialImage({ image, name, index, className }: MemorialImageProps) {
  return (
    <div className={className}>
      <div className="relative aspect-[3/4] w-full">
        <Image 
          src={image} 
          alt={`${name} - Photo ${index + 1}`}
          fill
          className="object-cover rounded-md"
        />
      </div>
    </div>
  );
}
```

### 2. DeceasedCard Component

#### Updated Layout Logic
```typescript
// Desktop layout with IIFE to handle gallery object
(() => {
  const gallery = MemorialImageGallery({ 
    images: deceased.images, 
    name: deceased.name, 
    isMobile 
  });
  
  if (hasTwoImages && typeof gallery === 'object' && 'leftImage' in gallery) {
    // Three-column layout: Image - Bio - Image
    return (
      <div className="flex flex-col md:flex-row gap-6">
        <div className="md:w-1/4">{gallery.leftImage}</div>
        <div className="md:w-2/4"><MemorialBioSection deceased={deceased} /></div>
        <div className="md:w-1/4">{gallery.rightImage}</div>
      </div>
    );
  } else {
    // Two-column layout: Image - Bio
    return (
      <div className="flex flex-col md:flex-row gap-6">
        <div className="md:w-1/4">{gallery.leftImage}</div>
        <div className="md:w-3/4"><MemorialBioSection deceased={deceased} /></div>
      </div>
    );
  }
})()
```

### 3. MemorialBioSection Component

#### Simplified Props
```typescript
// Before: Multiple props
interface MemorialBioSectionProps {
  deceased: Deceased;
  hasTwoImages: boolean;
  isMobile: boolean;
}

// After: Single prop
interface MemorialBioSectionProps {
  deceased: Deceased;
}
```

#### Flexible Width
```typescript
// Before: Conditional width classes
<div className={`${hasTwoImages && !isMobile ? 'md:w-2/4' : 'md:w-3/4'} space-y-3`}>

// After: Full width (parent controls width)
<div className="w-full space-y-3">
```

## Width Calculations

### Two Images Desktop Layout
- **Left Image**: `md:w-1/4` (25%)
- **Bio Section**: `md:w-2/4` (50%)
- **Right Image**: `md:w-1/4` (25%)
- **Total**: 100%

### Single Image Desktop Layout
- **Image**: `md:w-1/4` (25%)
- **Bio Section**: `md:w-3/4` (75%)
- **Total**: 100%

### Mobile Layout
- **All Elements**: `w-full` (100% stacked vertically)

## Responsive Behavior

### Desktop (>= 768px)
- **Two Images**: Three-column horizontal layout
- **Single Image**: Two-column horizontal layout
- **Spacing**: `gap-6` between columns

### Mobile (< 768px)
- **Two Images**: Carousel + bio below
- **Single Image**: Image + bio below
- **Layout**: Vertical stacking with `flex-col`

## Benefits

### 1. **Improved Visual Balance**
- Bio section centered between images creates symmetrical layout
- Better visual hierarchy and focus on content
- More professional appearance

### 2. **Better Readability**
- Bio section gets adequate width (50% vs competing with two images)
- Text content is easier to read in centered position
- Reduced visual clutter

### 3. **Flexible Space Utilization**
- Single image: Bio gets 75% width for comfortable reading
- Two images: Bio gets 50% width, balanced with images
- Mobile: Full width utilization with vertical stacking

### 4. **Maintained Functionality**
- Mobile carousel functionality preserved
- All existing features intact
- Responsive design maintained

## Testing Results

### Layout Structure Tests
```bash
npm run test:memorial:layout

# Results:
✅ Desktop three-column layout (Image-Bio-Image)
✅ Desktop two-column layout (Image-Bio)
✅ Mobile carousel layout
✅ Width calculations correct (25%-50%-25% and 25%-75%)
```

### Component Structure Tests
```bash
# Results:
✅ MemorialImageGallery returns object for desktop
✅ DeceasedCard uses IIFE to handle gallery object
✅ MemorialBioSection simplified props
✅ Responsive behavior working
```

## Browser Testing

### Desktop View
1. **Two Images**: 
   - ✅ Bio appears centered between images
   - ✅ Equal spacing and proportions
   - ✅ Professional layout

2. **Single Image**:
   - ✅ Bio takes majority of space
   - ✅ Comfortable reading width
   - ✅ Balanced composition

### Mobile View
1. **Two Images**:
   - ✅ Carousel functionality preserved
   - ✅ Bio appears below carousel
   - ✅ Full width utilization

2. **Single Image**:
   - ✅ Vertical stacking works correctly
   - ✅ Touch-friendly interface

## Migration Notes

### Breaking Changes
- `MemorialImageGallery` return type changed for desktop
- `MemorialBioSection` props simplified
- Layout structure updated

### Backward Compatibility
- Mobile functionality preserved
- All existing features maintained
- No API changes for external consumers

## Performance Impact

### Positive
- Simplified component props reduce re-renders
- Cleaner component structure
- Better separation of concerns

### Neutral
- No significant performance changes
- Same number of DOM elements
- Equivalent CSS complexity

## Future Enhancements

1. **Animation Support**
   - Smooth transitions between layouts
   - Image hover effects
   - Bio section animations

2. **Customization Options**
   - Configurable width ratios
   - Alternative layout modes
   - Theme-based spacing

3. **Accessibility Improvements**
   - Better screen reader support
   - Keyboard navigation
   - Focus management

This layout update provides a more balanced and professional appearance for memorial cards while maintaining all existing functionality and responsive behavior.
