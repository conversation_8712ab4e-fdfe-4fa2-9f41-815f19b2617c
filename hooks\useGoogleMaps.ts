import { useRef, useEffect } from "react";
import { Loader } from "@googlemaps/js-api-loader";
import { <PERSON>rrorHandler } from "@/lib/errorHandler";
import { LocationSearchResult } from "@/types/location";

// Add Google Maps type definitions
declare global {
  interface Window {
    google: {
      maps: {
        Map: any;
        MapOptions: any;
        Marker: any;
        MapTypeId: {
          ROADMAP: any;
        };
        Animation: {
          DROP: any;
        };
        Geocoder: any;
        GeocoderResult: any;
      }
    };
  }
}

interface UseGoogleMapsProps {
  result: LocationSearchResult | null;
  onLocationUpdate: (result: LocationSearchResult) => void;
}

export function useGoogleMaps({ result, onLocationUpdate }: UseGoogleMapsProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const googleMapRef = useRef<Window['google']['maps']['Map'] | null>(null);
  const markerRef = useRef<Window['google']['maps']['Marker'] | null>(null);

  // Initialize Google Maps when result is available
  useEffect(() => {
    if (result && mapRef.current) {
      const initMap = async () => {
        try {
          const loader = new Loader({
            apiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || "",
            version: "weekly",
            libraries: ["marker"]
          });
          
          const google = await loader.load();
          const { latitude, longitude } = result.lokasi;
          const location = { lat: parseFloat(latitude), lng: parseFloat(longitude) };
          
          const mapOptions: typeof google.maps.MapOptions = {
            center: location,
            zoom: 15,
            mapTypeId: google.maps.MapTypeId.ROADMAP,
            mapTypeControl: true,
            streetViewControl: true,
            fullscreenControl: true,
            styles: [
              {
                featureType: "all",
                elementType: "all",
                stylers: [{ saturation: -100 }]
              }
            ]
          };
          
          googleMapRef.current = new google.maps.Map(mapRef.current, mapOptions);
          
          markerRef.current = new google.maps.Marker({
            position: location,
            map: googleMapRef.current,
            title: result.formatted_address,
            draggable: true
          });
          
          // Update coordinates when marker is dragged
          markerRef.current.addListener("dragend", () => {
            if (markerRef.current) {
              const position = markerRef.current.getPosition();
              if (position) {
                const newLat = position.lat();
                const newLng = position.lng();
                
                // Update result with new coordinates
                const updatedResult = {
                  ...result,
                  place_id: result.place_id,
                  lokasi: {
                    latitude: newLat.toString(),
                    longitude: newLng.toString()
                  }
                };
                
                onLocationUpdate(updatedResult);
                
                // Get address for new coordinates
                const geocoder = new google.maps.Geocoder();
                geocoder.geocode({ location: { lat: newLat, lng: newLng } }, 
                  (results: Window['google']['maps']['GeocoderResult'][] | null, status: string) => {
                    if (status === "OK" && results && results[0]) {
                      const finalResult = {
                        ...updatedResult,
                        formatted_address: results[0].formatted_address,
                        place_id: results[0].place_id
                      };
                      onLocationUpdate(finalResult);
                    }
                  });
              }
            }
          });
          
        } catch (err) {
          console.error("Error loading Google Maps:", err);
          ErrorHandler.handle(err, "Google Maps Loading");
        }
      };
      
      initMap();
    }
  }, [result, onLocationUpdate]);

  return {
    mapRef
  };
}
