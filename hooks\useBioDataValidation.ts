import { useCallback } from "react";
import { BioData } from "@/components/purchase/BioDataStep";
import { BioDataValidators } from "@/lib/utils/bioDataValidators";
import { <PERSON><PERSON>r<PERSON>and<PERSON> } from "@/lib/errorHandler";

export function useBioDataValidation() {
  // Validate individual fields
  const validateName = useCallback((name: string) => {
    return BioDataValidators.validateName(name);
  }, []);

  const validateBirthPlace = useCallback((birthPlace: string) => {
    return BioDataValidators.validateBirthPlace(birthPlace);
  }, []);

  const validateBirthDate = useCallback((birthDate: string) => {
    return BioDataValidators.validateBirthDate(birthDate);
  }, []);

  const validateDeathDate = useCallback((deathDate: string) => {
    return BioDataValidators.validateDeathDate(deathDate);
  }, []);

  const validateDateRange = useCallback((birthDate: string, deathDate: string) => {
    return BioDataValidators.validateDateRange(birthDate, deathDate);
  }, []);

  const validateReligion = useCallback((religionId: string | null) => {
    return BioDataValidators.validateReligion(religionId);
  }, []);

  const validateSubmittedBy = useCallback((submittedBy: string) => {
    return BioDataValidators.validateSubmittedBy(submittedBy);
  }, []);

  const validateDescription = useCallback((description: string) => {
    return BioDataValidators.validateDescription(description);
  }, []);

  const validateEvidenceName = useCallback((evidenceName: string) => {
    return BioDataValidators.validateEvidenceName(evidenceName);
  }, []);

  const validateEvidenceImage = useCallback((evidenceImage: File | null) => {
    return BioDataValidators.validateEvidenceImage(evidenceImage);
  }, []);

  // Validate all fields
  const validateAll = useCallback((bioData: BioData) => {
    return BioDataValidators.validateAll(bioData);
  }, []);

  // Validate and handle errors with toast
  const validateAndHandle = useCallback((bioData: BioData): boolean => {
    try {
      // Debug: Log current bioData
      console.log('BioData before validation:', {
        birthDate: bioData.birthDate,
        deathDate: bioData.deathDate,
        name: bioData.name
      });

      return BioDataValidators.validateAndHandle(bioData);
    } catch (error) {
      ErrorHandler.handle(error, "Validation Error");
      return false;
    }
  }, []);

  // Validate specific field and show error if invalid
  const validateFieldAndHandle = useCallback((
    fieldName: keyof BioData, 
    value: any, 
    bioData?: BioData
  ): boolean => {
    let result;

    switch (fieldName) {
      case 'name':
        result = validateName(value);
        break;
      case 'birthPlace':
        result = validateBirthPlace(value);
        break;
      case 'birthDate':
        result = validateBirthDate(value);
        break;
      case 'deathDate':
        result = validateDeathDate(value);
        // Also validate date range if both dates are available
        if (result.isValid && bioData?.birthDate) {
          result = validateDateRange(bioData.birthDate, value);
        }
        break;
      case 'religionId':
        result = validateReligion(value);
        break;
      case 'submittedBy':
        result = validateSubmittedBy(value);
        break;
      case 'description':
        result = validateDescription(value);
        break;
      case 'evidenceName':
        result = validateEvidenceName(value);
        break;
      case 'evidenceImage':
        result = validateEvidenceImage(value);
        break;
      default:
        result = { isValid: true };
    }

    if (!result.isValid && result.error) {
      ErrorHandler.handle(result.error, "Field Validation");
      return false;
    }

    return true;
  }, [
    validateName,
    validateBirthPlace,
    validateBirthDate,
    validateDeathDate,
    validateDateRange,
    validateReligion,
    validateSubmittedBy,
    validateDescription,
    validateEvidenceName,
    validateEvidenceImage
  ]);

  return {
    validateName,
    validateBirthPlace,
    validateBirthDate,
    validateDeathDate,
    validateDateRange,
    validateReligion,
    validateSubmittedBy,
    validateDescription,
    validateEvidenceName,
    validateEvidenceImage,
    validateAll,
    validateAndHandle,
    validateFieldAndHandle
  };
}
