import { NextRequest, NextResponse } from 'next/server';
import { ImageCompressionService } from '@/lib/services/imageCompressionService';
import { <PERSON><PERSON>r<PERSON>andler } from '@/lib/errorHandler';
import { ApiError } from '@/utils/ApiError';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { images, options = {} } = body;

    // Validate request
    if (!images || !Array.isArray(images)) {
      throw new ApiError('Images array is required', 400, 'INVALID_REQUEST');
    }

    if (images.length === 0) {
      throw new ApiError('At least one image is required', 400, 'EMPTY_IMAGES');
    }

    if (images.length > 5) {
      throw new ApiError('Maximum 5 images allowed per request', 400, 'TOO_MANY_IMAGES');
    }

    // Validate each image
    for (let i = 0; i < images.length; i++) {
      const validation = ImageCompressionService.validateImageInput(images[i]);
      if (!validation.isValid) {
        throw new ApiError(`Image ${i + 1}: ${validation.error}`, 400, 'INVALID_IMAGE');
      }
    }

    console.log(`🖼️ Processing ${images.length} image(s) for compression...`);

    // Process images
    const results = await ImageCompressionService.processMultipleImages(images, options);
    
    // Get compression statistics
    const stats = ImageCompressionService.getCompressionStats(results);
    
    console.log(`✅ Compression completed:
      - Original total size: ${(stats.totalOriginalSize / 1024 / 1024).toFixed(2)} MB
      - Compressed total size: ${(stats.totalCompressedSize / 1024 / 1024).toFixed(2)} MB
      - Total size saved: ${(stats.totalSizeSaved / 1024 / 1024).toFixed(2)} MB
      - Average compression: ${stats.averageCompressionRatio.toFixed(1)}%
      - Processing time: ${stats.totalProcessingTime}ms`);

    // Return compressed images (base64 format for easy client consumption)
    const compressedImages = results.map(result => ({
      base64: result.compressed.base64,
      size: result.compressed.size,
      format: result.compressed.format,
      originalSize: result.original.size,
      compressionRatio: result.metadata.compressionRatio,
      sizeSaved: result.metadata.sizeSaved
    }));

    return NextResponse.json({
      success: true,
      data: {
        images: compressedImages,
        statistics: {
          totalImages: results.length,
          totalOriginalSize: stats.totalOriginalSize,
          totalCompressedSize: stats.totalCompressedSize,
          totalSizeSaved: stats.totalSizeSaved,
          averageCompressionRatio: stats.averageCompressionRatio,
          processingTime: stats.totalProcessingTime
        }
      }
    });

  } catch (error) {
    console.error('Image compression API error:', error);
    return ErrorHandler.handle(error, 'Image Compression');
  }
}

// Health check endpoint
export async function GET() {
  return NextResponse.json({
    success: true,
    message: 'Image compression service is running',
    features: {
      maxFileSize: '5MB',
      supportedFormats: ['JPEG', 'PNG', 'WebP'],
      maxImagesPerRequest: 5,
      targetCompressionSize: '500KB'
    }
  });
}
