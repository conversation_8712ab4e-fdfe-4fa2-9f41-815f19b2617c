export class DateHelpers {
  /**
   * Get today's date in YYYY-MM-DD format
   */
  static getTodayString(): string {
    return new Date().toISOString().split('T')[0];
  }

  /**
   * Get minimum allowed date (1500-01-01)
   */
  static getMinDateString(): string {
    return '1500-01-01';
  }

  /**
   * Get today's date with time set to end of day
   */
  static getTodayEndOfDay(): Date {
    const today = new Date();
    today.setHours(23, 59, 59, 999);
    return today;
  }

  /**
   * Get minimum allowed date object
   */
  static getMinDate(): Date {
    return new Date('1500-01-01');
  }

  /**
   * Check if date string is in valid format (YYYY-MM-DD)
   */
  static isValidDateFormat(dateString: string): boolean {
    return /^\d{4}-\d{2}-\d{2}$/.test(dateString);
  }

  /**
   * Check if date is not in the future
   */
  static isNotInFuture(dateString: string): boolean {
    const date = new Date(dateString);
    const today = this.getTodayEndOfDay();
    return date <= today;
  }

  /**
   * Check if date is not before minimum allowed date
   */
  static isNotTooOld(dateString: string): boolean {
    const date = new Date(dateString);
    const minDate = this.getMinDate();
    return date >= minDate;
  }

  /**
   * Check if birth date is before death date
   */
  static isBirthBeforeDeath(birthDate: string, deathDate: string): boolean {
    return new Date(birthDate) < new Date(deathDate);
  }

  /**
   * Format date for display (remove time component)
   */
  static formatDateForDisplay(dateString: string): string {
    if (!dateString) return '';
    return dateString.split('T')[0];
  }

  /**
   * Get date input attributes for HTML date input
   */
  static getDateInputAttributes() {
    return {
      min: this.getMinDateString(),
      max: this.getTodayString()
    };
  }
}
