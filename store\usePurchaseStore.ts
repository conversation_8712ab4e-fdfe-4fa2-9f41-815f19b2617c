import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { BioData } from '@/components/purchase/BioDataStep';
import { PackageDetails, LocationSearchResult } from '@/types';

interface PurchaseState {
  currentStep: 'package' | 'payment' | 'biodata' | 'location' | 'details' | 'confirmation';
  bioData: BioData | null;
  selectedLocation: LocationSearchResult | null;
  packageDetails: PackageDetails | null;
  paymentToken: string | null; // Token untuk auth upload setelah payment

  // Actions
  setCurrentStep: (step: 'package' | 'payment' | 'biodata' | 'location' | 'details' | 'confirmation') => void;
  setBioData: (data: BioData) => void;
  setSelectedLocation: (location: LocationSearchResult) => void;
  setPackageDetails: (details: PackageDetails) => void;
  setPaymentToken: (token: string | null) => void;
  resetPurchase: () => void;
  resetBioData: () => void;
  resetLocation: () => void;
  resetPackageDetails: () => void;
  resetAfterPayment: () => void; // Reset aman setelah payment
}

// Default state untuk BioData
const defaultBioData: BioData = {
  name: "",
  birthPlace: "",
  birthDate: "",
  deathDate: "",
  religionId: null,  // Use null instead of 0 to match BioData interface
  submittedBy: "",
  description: "",
  images: [],
  evidenceName: "",
  evidenceImage: null
};

export const usePurchaseStore = create<PurchaseState>()(
  persist(
    (set) => ({
      currentStep: 'package',
      bioData: defaultBioData,
      selectedLocation: null,
      packageDetails: null,
      paymentToken: null,

      setCurrentStep: (step) => set({ currentStep: step }),
      setBioData: (data) => set({ bioData: data }),
      setSelectedLocation: (location) => set({ selectedLocation: location }),
      setPackageDetails: (details) => set({ packageDetails: details }),
      setPaymentToken: (token) => set({ paymentToken: token }),
      resetPurchase: () => {
        console.log('🔄 Resetting entire purchase');
        set({
          currentStep: 'package',
          bioData: { ...defaultBioData }, // Create new object reference
          selectedLocation: null,
          packageDetails: null,
          paymentToken: null
        });
      },
      resetBioData: () => {
        console.log('🔄 Resetting bioData');
        set({ bioData: { ...defaultBioData } }); // Create new object reference
      },
      resetLocation: () => {
        console.log('🔄 Resetting location');
        set({ selectedLocation: null });
      },
      resetPackageDetails: () => {
        console.log('🔄 Resetting package details');
        set({ packageDetails: null });
      },
      resetAfterPayment: () => {
        console.log('🔄 Resetting after payment (keeping payment token and package)');
        set((state) => ({
          currentStep: 'biodata', // Kembali ke biodata step
          bioData: { ...defaultBioData }, // Reset biodata
          selectedLocation: null, // Reset location
          // Tetap pertahankan paymentToken dan packageDetails
          paymentToken: state.paymentToken,
          packageDetails: state.packageDetails
        }));
      },
    }),
    {
      name: 'purchase-storage',
      partialize: (state) => ({
        currentStep: state.currentStep,
        bioData: state.bioData ? {
          ...state.bioData,
          images: [],
          evidenceImage: null
        } : null,
        selectedLocation: state.selectedLocation,
        packageDetails: state.packageDetails,
        paymentToken: state.paymentToken,
      }),
    }
  )
);