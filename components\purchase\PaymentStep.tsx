import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { PackageDetails } from "@/types";
import { ErrorHandler } from "@/lib/errorHandler";
import Loading from "@/components/ui/loading";

interface PaymentStepProps {
  packageDetails: PackageDetails;
  onCompletePayment: (paymentToken: string) => void;
}

export default function PaymentStep({ packageDetails, onCompletePayment }: PaymentStepProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<'bank' | 'ewallet'>('bank');

  // Format price to Indonesian Rupiah
  const formatPrice = (price: number) => {
    if (price === 0) return "Gratis";
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      maximumFractionDigits: 0
    }).format(price);
  };

  // Calculate admin fee (5% of package price, minimum 0 for free package)
  const adminFee = packageDetails.price === 0 ? 0 : Math.max(packageDetails.price * 0.05, 2.500);
  const totalPrice = packageDetails.price + adminFee;

  // Handle payment submission
  const handlePayment = async () => {
    setIsProcessing(true);

    try {
      // No validation needed - payment happens before biodata input
      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Generate payment token (in real app, this would come from payment gateway)
      const paymentToken = `pay_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;

      ErrorHandler.handleSuccess('Pembayaran berhasil! Silakan lanjutkan untuk mengisi biodata.');
      onCompletePayment(paymentToken);

    } catch (error) {
      ErrorHandler.handle(error, 'Payment Processing');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="bg-memorial-900 p-6 rounded-lg border border-memorial-800">
      <h2 className="text-xl font-semibold mb-4">Pembayaran</h2>

      <div className="mb-6">
        <h3 className="text-lg font-medium mb-2">Ringkasan Pemesanan</h3>
        <div className="bg-memorial-800 p-4 rounded-lg mb-4">
          <div className="flex justify-between mb-2">
            <span className="text-memorial-300">{packageDetails.name}</span>
            <span className="font-medium">{formatPrice(packageDetails.price)}</span>
          </div>
          {packageDetails.price > 0 && (
            <div className="flex justify-between mb-2">
              <span className="text-memorial-300">Biaya Administrasi</span>
              <span className="font-medium">{formatPrice(adminFee)}</span>
            </div>
          )}
          <div className="border-t border-memorial-700 my-2"></div>
          <div className="flex justify-between">
            <span className="font-medium">Total</span>
            <span className="font-bold text-candle-500">{formatPrice(totalPrice)}</span>
          </div>
        </div>

        {/* Package Features */}
        <div className="bg-memorial-800 p-4 rounded-lg mb-4">
          <h4 className="text-lg font-medium mb-3 text-memorial-50">Fitur yang Didapat</h4>
          <div className="space-y-2">
            {packageDetails.features.map((feature, index) => (
              <div key={index} className="flex items-start">
                <span className="text-candle-500 mr-2 mt-1">✓</span>
                <span className="text-memorial-300 text-sm">{feature}</span>
              </div>
            ))}
          </div>
          <div className="mt-3 pt-3 border-t border-memorial-700">
            <div className="flex items-center text-sm text-memorial-400">
              <span className="mr-2">⏱️</span>
              <span>Masa aktif: {packageDetails.duration}</span>
            </div>
          </div>
        </div>

        {/* Show payment methods only for paid packages */}
        {packageDetails.price > 0 ? (
          <>
            <h3 className="text-lg font-medium mb-2">Metode Pembayaran</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div
                className={`bg-memorial-800 p-4 rounded-lg border cursor-pointer transition-colors ${
                  selectedPaymentMethod === 'bank'
                    ? 'border-candle-500 ring-1 ring-candle-500'
                    : 'border-memorial-700 hover:border-candle-500'
                }`}
                onClick={() => setSelectedPaymentMethod('bank')}
              >
                <h4 className={`font-medium ${selectedPaymentMethod === 'bank' ? 'text-candle-500' : 'text-memorial-50'}`}>
                  Transfer Bank
                </h4>
                <p className="text-sm text-memorial-300 mt-1">Transfer ke rekening yang tertera</p>
              </div>
              <div
                className={`bg-memorial-800 p-4 rounded-lg border cursor-pointer transition-colors ${
                  selectedPaymentMethod === 'ewallet'
                    ? 'border-candle-500 ring-1 ring-candle-500'
                    : 'border-memorial-700 hover:border-candle-500'
                }`}
                onClick={() => setSelectedPaymentMethod('ewallet')}
              >
                <h4 className={`font-medium ${selectedPaymentMethod === 'ewallet' ? 'text-candle-500' : 'text-memorial-50'}`}>
                  E-Wallet
                </h4>
                <p className="text-sm text-memorial-300 mt-1">Pembayaran melalui e-wallet</p>
              </div>
            </div>
          </>
        ) : (
          <div className="bg-green-900/30 border border-green-800 rounded-lg p-4 mb-6">
            <h3 className="text-lg font-medium mb-2 text-green-200">Paket Gratis</h3>
            <p className="text-sm text-green-300">
              Anda memilih paket gratis. Tidak ada pembayaran yang diperlukan.
              Klik tombol di bawah untuk melanjutkan aktivasi memorial digital Anda.
            </p>
          </div>
        )}
      </div>

      <Button
        className="w-full bg-candle-500 text-memorial-950 hover:bg-candle-400 disabled:opacity-50"
        onClick={handlePayment}
        disabled={isProcessing}
      >
        {isProcessing ? (
          <div className="flex items-center justify-center">
            <Loading size="sm" message="Memproses..." className="py-0" />
          </div>
        ) : (
          packageDetails.price > 0 ? 'Bayar Sekarang' : 'Aktivasi Memorial Gratis'
        )}
      </Button>
    </div>
  );
}