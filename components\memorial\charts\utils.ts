import { Deceased } from "@/data/deceased";

export interface ChartData {
  packageId: string;
  packageName: string;
  count: number;
  percentage: number;
  color: string;
}

export function calculatePackageStats(deceasedList: Deceased[]) {
  const stats = deceasedList.reduce((acc, deceased) => {
    const packageId = deceased.packageId || 'unknown';
    acc[packageId] = (acc[packageId] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const total = deceasedList.length;

  return { stats, total };
}

export function calculateAdditionalStats(deceasedList: Deceased[]) {
  const currentYear = new Date().getFullYear();
  
  const recentMemorials = deceasedList.filter(d =>
    d.deathYear === currentYear
  ).length;

  const averageAge = deceasedList.length > 0 ?
    Math.round(deceasedList.reduce((sum, d) => {
      const age = d.deathYear - d.birthYear;
      return sum + (age > 0 ? age : 0);
    }, 0) / deceasedList.length) : 0;

  const paidPackages = deceasedList.filter(d =>
    d.packageId && d.packageId !== 'free'
  ).length;

  const conversionRate = deceasedList.length > 0 ? (paidPackages / deceasedList.length) * 100 : 0;

  return {
    currentYear,
    recentMemorials,
    averageAge,
    paidPackages,
    conversionRate
  };
}

export function prepareChartData(stats: Record<string, number>, total: number): ChartData[] {
  return Object.entries(stats).map(([packageId, count]) => {
    const percentage = (count / total) * 100;
    const packageName =
      packageId === 'free' ? 'Free' :
      packageId === 'standard' ? 'Standar' :
      packageId === 'premium' ? 'Premium' :
      packageId.charAt(0).toUpperCase() + packageId.slice(1);

    const color =
      packageId === 'free' ? '#059669' :      // Emerald-600 - hijau tua untuk kontras tinggi
      packageId === 'standard' ? '#1E40AF' :  // Blue-800 - biru tua untuk visibilitas optimal
      packageId === 'premium' ? '#B45309' : '#374151';  // Amber-700 - oranye tua yang mudah dilihat

    return {
      packageId,
      packageName,
      count,
      percentage,
      color
    };
  }).sort((a, b) => b.count - a.count); // Sort by count descending
}

export function prepareDonutData(chartData: ChartData[]) {
  return chartData.map(item => ({
    name: item.packageName,
    value: item.count,
    color: item.color,
    percentage: item.percentage
  }));
}
