import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { config } from 'dotenv';

// Load environment variables from .env.local
config({ path: '.env.local' });

// Database connection
const connectionString = process.env.DATABASE_URL || 
  `postgresql://${process.env.DB_USER || 'postgres'}:${process.env.DB_PASSWORD || 'password'}@${process.env.DB_HOST || 'localhost'}:${process.env.DB_PORT || '5432'}/${process.env.DB_NAME || 'pemakaman_digital'}`;

async function resetDatabase() {
  console.log('🗑️ Resetting database...\n');

  const client = postgres(connectionString, { max: 1 });
  const db = drizzle(client);

  try {
    // Drop all tables in correct order (considering foreign keys)
    console.log('⚡ Dropping all tables...');
    
    await client`DROP TABLE IF EXISTS orders CASCADE`;
    console.log('✅ Dropped orders table');
    
    await client`DROP TABLE IF EXISTS memorial_images CASCADE`;
    console.log('✅ Dropped memorial_images table');
    
    await client`DROP TABLE IF EXISTS memorials CASCADE`;
    console.log('✅ Dropped memorials table');
    
    await client`DROP TABLE IF EXISTS locations CASCADE`;
    console.log('✅ Dropped locations table');
    
    await client`DROP TABLE IF EXISTS religions CASCADE`;
    console.log('✅ Dropped religions table');

    // Drop any remaining sequences
    await client`DROP SEQUENCE IF EXISTS locations_id_seq CASCADE`;
    await client`DROP SEQUENCE IF EXISTS memorials_id_seq CASCADE`;
    await client`DROP SEQUENCE IF EXISTS memorial_images_id_seq CASCADE`;
    await client`DROP SEQUENCE IF EXISTS orders_id_seq CASCADE`;
    await client`DROP SEQUENCE IF EXISTS religions_id_seq CASCADE`;
    console.log('✅ Dropped sequences');

    // Drop any remaining functions
    await client`DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE`;
    console.log('✅ Dropped functions');

    console.log('\n🎉 Database reset completed successfully!');
    console.log('💡 Now run: npm run db:migrate');

  } catch (error) {
    console.error('❌ Reset failed:', error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

// Run if this file is executed directly
if (require.main === module) {
  resetDatabase();
}

export { resetDatabase };
