import { Button } from "@/components/ui/button";

interface FormActionsProps {
  onReset?: () => void;
  onSubmit: (e: React.FormEvent) => void;
  showResetButton?: boolean;
  submitButtonText?: string;
  resetButtonText?: string;
  isSubmitting?: boolean;
}

export default function FormActions({
  onReset,
  onSubmit,
  showResetButton = true,
  submitButtonText = "Lanjutkan ke Pemilihan <PERSON>emaka<PERSON>",
  resetButtonText = "Reset Form",
  isSubmitting = false
}: FormActionsProps) {
  return (
    <div className="flex flex-col sm:flex-row gap-3">
      {showResetButton && onReset && (
        <Button
          type="button"
          variant="outline"
          onClick={onReset}
          disabled={isSubmitting}
          className="w-full sm:w-auto border-memorial-600 text-memorial-300 hover:bg-memorial-800 hover:text-memorial-50 text-sm sm:text-base py-2 sm:py-3"
        >
          {resetButtonText}
        </Button>
      )}
      <Button
        type="submit"
        onClick={onSubmit}
        disabled={isSubmitting}
        className="flex-1 bg-candle-500 text-memorial-950 hover:bg-candle-400 disabled:opacity-50 disabled:cursor-not-allowed text-sm sm:text-base py-2 sm:py-3"
      >
        {isSubmitting ? "Memproses..." : submitButtonText}
      </Button>
    </div>
  );
}
