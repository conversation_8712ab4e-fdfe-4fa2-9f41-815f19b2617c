#!/usr/bin/env tsx

/**
 * Fresh migration script - drops all tables and recreates from schema
 * Use this when changing from integer IDs to UUIDs
 */

import { drizzle } from 'drizzle-orm/postgres-js';
import { migrate } from 'drizzle-orm/postgres-js/migrator';
import postgres from 'postgres';
import { religions } from '@/db/schema';
import { env, logEnvironmentStatus } from '@/lib/env';
import { generateReligionSlug } from '@/lib/utils/slug';
import { sql } from 'drizzle-orm';
import '@/lib/forceEnv'; // Force load environment

// Log environment status
const validation = logEnvironmentStatus();

if (!validation.isValid) {
  console.error('❌ Environment validation failed. Cannot proceed with migration.');
  process.exit(1);
}

// Database connection
const connectionString = env.DATABASE_URL;

if (!connectionString) {
  console.error('❌ DATABASE_URL not found in environment');
  process.exit(1);
}

console.log('🔗 Connection details:');
if (connectionString.includes('supabase.co')) {
  console.log('  Type: Supabase Cloud PostgreSQL');
  const maskedUrl = connectionString.replace(/:([^:@]+)@/, ':***@');
  console.log(`  URL: ${maskedUrl}`);
} else if (connectionString.includes('localhost')) {
  console.log('  Type: Local PostgreSQL');
  console.log(`  URL: ${connectionString.replace(/:([^:@]+)@/, ':***@')}`);
} else {
  console.log('  Type: External PostgreSQL');
  console.log(`  URL: ${connectionString.replace(/:([^:@]+)@/, ':***@')}`);
}
console.log('');

async function main() {
  console.log('🚀 Starting fresh database migration...\n');

  // Create connection
  const migrationClient = postgres(connectionString, { max: 1 });
  const db = drizzle(migrationClient);

  try {
    // Drop all existing tables
    console.log('🗑️  Dropping existing tables...');
    
    await db.execute(sql`DROP TABLE IF EXISTS memorial_images CASCADE`);
    await db.execute(sql`DROP TABLE IF EXISTS orders CASCADE`);
    await db.execute(sql`DROP TABLE IF EXISTS memorials CASCADE`);
    await db.execute(sql`DROP TABLE IF EXISTS locations CASCADE`);
    await db.execute(sql`DROP TABLE IF EXISTS religions CASCADE`);
    
    // Drop drizzle migration tables
    await db.execute(sql`DROP TABLE IF EXISTS drizzle.__drizzle_migrations CASCADE`);
    await db.execute(sql`DROP SCHEMA IF EXISTS drizzle CASCADE`);
    
    console.log('✅ All tables dropped successfully\n');

    // Create tables manually with UUID
    console.log('⚡ Creating tables with UUID...');

    // Enable UUID extension
    await db.execute(sql`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`);

    // Create religions table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS "religions" (
        "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
        "slug" varchar(100) NOT NULL,
        "name" varchar(100) NOT NULL,
        CONSTRAINT "religions_slug_unique" UNIQUE("slug"),
        CONSTRAINT "religions_name_unique" UNIQUE("name")
      )
    `);

    // Create locations table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS "locations" (
        "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
        "slug" varchar(255) NOT NULL,
        "name" varchar(255) NOT NULL,
        "address_detail" text NOT NULL,
        "province" varchar(100) NOT NULL,
        "city" varchar(100) NOT NULL,
        "district" varchar(100) NOT NULL,
        "sub_district" varchar(100) NOT NULL,
        "latitude" varchar(50) NOT NULL,
        "longitude" varchar(50) NOT NULL,
        "place_id" varchar(255),
        "created_at" timestamp DEFAULT now() NOT NULL,
        CONSTRAINT "locations_slug_unique" UNIQUE("slug")
      )
    `);

    // Create memorials table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS "memorials" (
        "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
        "slug" varchar(255) NOT NULL,
        "name" varchar(255) NOT NULL,
        "birth_place" varchar(255) NOT NULL,
        "birth_date" date NOT NULL,
        "death_date" date NOT NULL,
        "religion_id" uuid,
        "location_id" uuid,
        "description" text,
        "submitted_by" varchar(255) NOT NULL,
        "evidence_name" varchar(255) NOT NULL,
        "evidence_image_url" varchar(255),
        "created_at" timestamp DEFAULT now() NOT NULL,
        "updated_at" timestamp DEFAULT now() NOT NULL,
        CONSTRAINT "memorials_slug_unique" UNIQUE("slug")
      )
    `);

    // Create memorial_images table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS "memorial_images" (
        "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
        "memorial_id" uuid NOT NULL,
        "image_url" varchar(255) NOT NULL,
        "caption" text,
        "created_at" timestamp DEFAULT now() NOT NULL
      )
    `);

    // Create orders table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS "orders" (
        "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
        "memorial_id" uuid NOT NULL,
        "package_id" varchar(50) NOT NULL,
        "package_name" varchar(100) NOT NULL,
        "package_price" integer DEFAULT 0 NOT NULL,
        "package_duration" varchar(50) NOT NULL,
        "package_features" text NOT NULL,
        "admin_fee" integer DEFAULT 0 NOT NULL,
        "total_price" integer DEFAULT 0 NOT NULL,
        "payment_method" varchar(50),
        "payment_status" varchar(20) DEFAULT 'pending' NOT NULL,
        "created_at" timestamp DEFAULT now() NOT NULL,
        "updated_at" timestamp DEFAULT now() NOT NULL
      )
    `);

    console.log('✅ Tables created successfully\n');

    // Seed religions data
    console.log('🌱 Seeding religions data...');
    const religionData = [
      { name: 'Islam', slug: generateReligionSlug('Islam') },
      { name: 'Kristen', slug: generateReligionSlug('Kristen') },
      { name: 'Katolik', slug: generateReligionSlug('Katolik') },
      { name: 'Hindu', slug: generateReligionSlug('Hindu') },
      { name: 'Buddha', slug: generateReligionSlug('Buddha') },
      { name: 'Konghucu', slug: generateReligionSlug('Konghucu') },
    ];
    
    await db.insert(religions).values(religionData).onConflictDoNothing();
    console.log('✅ Religions data seeded successfully\n');

    // Verify tables
    console.log('🔍 Verifying table structure...');
    
    const tables = await db.execute(sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('memorials', 'locations', 'orders', 'memorial_images', 'religions')
      ORDER BY table_name
    `);
    
    console.log('📋 Created tables:');
    tables.forEach((table: any) => {
      console.log(`  ✅ ${table.table_name}`);
    });

    // Check UUID extension
    console.log('\n🔧 Checking UUID extension...');
    await db.execute(sql`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`);
    console.log('✅ UUID extension enabled\n');

    console.log('🎉 Fresh database migration completed successfully!');
    console.log('💡 Database is now ready with UUID primary keys and slug fields.');
    console.log('🚀 You can now test the purchase flow in your application.');

  } catch (error) {
    console.error('❌ Fresh migration failed:', error);
    process.exit(1);
  } finally {
    await migrationClient.end();
  }
}

// Run if this file is executed directly
if (require.main === module) {
  main();
}

export { main as runFreshMigration };
