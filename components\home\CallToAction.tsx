import { But<PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON> } from "lucide-react";
import Link from "next/link";

export default function CallToAction() {
  return (
    <section className="py-16 bg-memorial-900 rounded-lg border border-memorial-800">
      <div className="container mx-auto px-4 text-center">
        <h2 className="text-2xl md:text-3xl font-bold mb-4">Siap Menggunakan Pemakaman Digital?</h2>
        <p className="text-memorial-300 max-w-2xl mx-auto mb-8">
          <PERSON><PERSON> jelajahi lokasi pemakaman atau abadikan kenangan orang tercinta Anda dengan layanan memorial digital kami.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href="/memorial">
            <Button className="bg-memorial-800 text-memorial-50 hover:bg-memorial-700 border border-memorial-700 px-6">
              <PERSON><PERSON><PERSON><PERSON> Pemakaman
            </Button>
          </Link>
          
          <Link href="/purchase">
            <Button className="bg-candle-500 text-memorial-950 hover:bg-candle-400 px-6">
              Beli Memorial <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
}