import type { <PERSON>ada<PERSON> } from "next";
import { Poppins } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import { SearchProvider } from "@/store/SearchProvider";
import { ReligionProvider } from "@/store/ReligionProvider";
import { PurchaseProvider } from "@/store/PurchaseProvider";
import ClientLayout from "@/components/layouts/ClientLayout";
import { Toaster } from "@/components/ui/toaster";

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
});

export const metadata: Metadata = {
  title: "Pemakaman Digital - Abadikan memori",
  description: "Abadikan memori",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${poppins.className} bg-black antialiased`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <SearchProvider>
            <ReligionProvider>
              <PurchaseProvider>
                <ClientLayout>{children}</ClientLayout>
                <Toaster />
              </PurchaseProvider>
            </ReligionProvider>
          </SearchProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}


