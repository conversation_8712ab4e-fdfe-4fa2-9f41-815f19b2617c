"use client";

import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import Navbar from "@/components/layouts/Navbar";
import SearchModal from "@/components/SearchModal";
import ReligionSelectionPopup from "@/components/ReligionSelectionPopup";
import { useSearchStore } from "@/store/useSearchStore";
import { useReligionStore } from "@/store/useReligionStore";
import Footer from "@/components/layouts/Footer";

// Routes that should not display the footer
const routesWithoutFooter = [
  "/",
  "/pemakaman",
  "/purchase",
  // Add other routes here as needed
];

export default function ClientLayout({ 
  children,
  hideFooter = false
}: { 
  children: React.ReactNode;
  hideFooter?: boolean;
}) {
  const { isSearchOpen, openSearch, closeSearch } = useSearchStore();
  const { hasSeenPopup, setHasSeenPopup, setSelectedReligion } = useReligionStore();
  const pathname = usePathname();
  
  // State to track if the popup has been shown in this session
  const [hasSeenPopupThisSession, setHasSeenPopupThisSession] = useState(true);
  
  // Determine if footer should be shown
  const shouldShowFooter = !hideFooter && !routesWithoutFooter.includes(pathname);
  
  useEffect(() => {
    // Check if the user has seen the popup before
    setHasSeenPopupThisSession(hasSeenPopup);
  }, [hasSeenPopup]);
  
  const handleClosePopup = () => {
    setHasSeenPopup(true);
    setHasSeenPopupThisSession(true);
  };
  
  const handleSelectReligion = (religion: string) => {
    setSelectedReligion(religion);
    setHasSeenPopup(true);
    setHasSeenPopupThisSession(true);
  };
  
  const handleReopenReligionSelection = () => {
    setHasSeenPopupThisSession(false);
  };

  return (
    <>
      <Navbar 
        onOpenSearch={openSearch} 
        onOpenReligionSelection={handleReopenReligionSelection}
      />
      
      {children}
      
      {shouldShowFooter && <Footer />}
      
      <SearchModal 
        isOpen={isSearchOpen} 
        onClose={closeSearch} 
      />
      
      <ReligionSelectionPopup 
        isOpen={!hasSeenPopupThisSession}
        onClose={handleClosePopup}
        onSelectReligion={handleSelectReligion}
      />
    </>
  );
}


