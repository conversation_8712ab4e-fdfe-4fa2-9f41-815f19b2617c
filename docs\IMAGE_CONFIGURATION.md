# Image Configuration

## Next.js Image Configuration

File `next.config.mjs` dikonfigurasi untuk mendukung external images dari berbagai hostname.

### Configured Hostnames

#### 1. **Placeholder Services**
```javascript
{
  protocol: "https",
  hostname: "placehold.co",
  port: "",
  pathname: "/**",
},
{
  protocol: "https", 
  hostname: "via.placeholder.com",
  port: "",
  pathname: "/**",
}
```

**Usage:**
- `placehold.co` - Untuk placeholder images di development
- `via.placeholder.com` - Untuk local development fallback images

#### 2. **Google Services**
```javascript
{
  protocol: "https",
  hostname: "lh3.googleusercontent.com",
  port: "",
  pathname: "/**",
}
```

**Usage:**
- Google user profile images
- Google Maps static images

#### 3. **Supabase Storage**
```javascript
{
  protocol: "https",
  hostname: "*.supabase.co",
  port: "",
  pathname: "/storage/v1/object/public/**",
}
```

**Usage:**
- Production image storage
- Memorial images dan evidence images

### Local Development Images

Untuk local development tanpa Supabase, sistem menggunakan placeholder URLs:

```typescript
// imageStorageService.ts
private createLocalPlaceholder(fileName: string, folder: string): UploadResult {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 15);
  const placeholderPath = `${folder}/${timestamp}_${randomString}_${fileName}`;
  
  // Use via.placeholder.com for local development
  const placeholderUrl = `https://via.placeholder.com/400x300/1a1a1a/ffffff?text=${encodeURIComponent(fileName)}`;
  
  return {
    url: placeholderPath,
    path: placeholderPath,
    publicUrl: placeholderUrl
  };
}
```

### Production Images

Untuk production, sistem akan menggunakan Supabase Storage:

```typescript
// Environment variables untuk production
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

### Image Optimization

Next.js secara otomatis mengoptimasi images dengan:

- **Format Conversion**: WebP/AVIF untuk browser yang support
- **Responsive Images**: Multiple sizes untuk different devices
- **Lazy Loading**: Images dimuat saat diperlukan
- **Quality Optimization**: Kompresi otomatis

### Security Configuration

```javascript
dangerouslyAllowSVG: true,
contentDispositionType: 'attachment',
contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
```

**Security Features:**
- SVG support dengan sandbox
- Content disposition untuk download
- CSP untuk mencegah XSS

### Troubleshooting

#### Error: hostname not configured
```
Error: Invalid src prop (...) on `next/image`, hostname "..." is not configured under images in your `next.config.js`
```

**Solution:**
1. Tambahkan hostname ke `remotePatterns` di `next.config.mjs`
2. Restart development server
3. Clear browser cache jika diperlukan

#### Example untuk hostname baru:
```javascript
{
  protocol: "https",
  hostname: "example.com",
  port: "",
  pathname: "/**",
}
```

### Best Practices

1. **Development**: Gunakan placeholder services
2. **Production**: Gunakan CDN atau cloud storage
3. **Security**: Selalu validasi image sources
4. **Performance**: Gunakan Next.js Image component
5. **Fallback**: Sediakan fallback untuk missing images

### Commands

```bash
# Restart development server setelah config change
npm run dev

# Update Next.js ke versi terbaru
npm run update:nextjs

# Test image loading
npm run test:memorials
```
