import { useState, useCallback } from 'react';
import { api<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/errorHandler';
import { ImageHelpers } from '@/lib/utils/imageHelpers';

export interface CompressedImageResult {
  base64: string;
  size: number;
  format: string;
  originalSize: number;
  compressionRatio: number;
  sizeSaved: number;
}

export interface CompressionStatistics {
  totalImages: number;
  totalOriginalSize: number;
  totalCompressedSize: number;
  totalSizeSaved: number;
  averageCompressionRatio: number;
  processingTime: number;
}

export interface UseImageCompressionReturn {
  compressImages: (files: File[]) => Promise<CompressedImageResult[]>;
  compressBase64Images: (base64Images: string[]) => Promise<CompressedImageResult[]>;
  loading: boolean;
  error: string | null;
  statistics: CompressionStatistics | null;
}

export function useImageCompression(): UseImageCompressionReturn {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [statistics, setStatistics] = useState<CompressionStatistics | null>(null);

  const compressBase64Images = useCallback(async (base64Images: string[]): Promise<CompressedImageResult[]> => {
    if (base64Images.length === 0) {
      throw new Error('No images provided for compression');
    }

    if (base64Images.length > 5) {
      throw new Error('Maximum 5 images allowed per compression request');
    }

    setLoading(true);
    setError(null);

    try {
      console.log(`🔄 Compressing ${base64Images.length} image(s)...`);

      const response = await apiRequest<{
        success: boolean;
        data: {
          images: CompressedImageResult[];
          statistics: CompressionStatistics;
        };
      }>(
        '/api/compress-image',
        {
          method: 'POST',
          body: JSON.stringify({
            images: base64Images,
            options: {} // Use smart compression
          }),
        },
        'Image Compression'
      );

        console.log('API Response:', response);
      if (!response?.data) {
        throw new Error('Invalid response from compression service');
      }

      const { images, statistics: stats } = response.data;
      
      setStatistics(stats);
      
      console.log(`✅ Compression completed:
        - ${stats.totalImages} images processed
        - Original: ${ImageHelpers.formatFileSize(stats.totalOriginalSize)}
        - Compressed: ${ImageHelpers.formatFileSize(stats.totalCompressedSize)}
        - Saved: ${ImageHelpers.formatFileSize(stats.totalSizeSaved)} (${stats.averageCompressionRatio.toFixed(1)}%)
        - Time: ${stats.processingTime}ms`);

      return images;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to compress images';
      setError(errorMessage);
      ErrorHandler.handle(err, 'Image Compression');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const compressImages = useCallback(async (files: File[]): Promise<CompressedImageResult[]> => {
    if (files.length === 0) {
      throw new Error('No files provided for compression');
    }

    try {
      // Convert files to base64
      const base64Images: string[] = [];
      
      for (const file of files) {
        // Validate file first
        const validation = ImageHelpers.validateImageFile(file);
        if (!validation.isValid) {
          throw new Error(validation.error || 'Invalid image file');
        }

        // Convert to base64
        const base64 = await new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => {
            if (typeof reader.result === 'string') {
              resolve(reader.result);
            } else {
              reject(new Error('Failed to read file as base64'));
            }
          };
          reader.onerror = () => reject(new Error('Failed to read file'));
          reader.readAsDataURL(file);
        });

        base64Images.push(base64);
      }

      // Compress the base64 images
      return await compressBase64Images(base64Images);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to process files for compression';
      setError(errorMessage);
      ErrorHandler.handle(err, 'File Processing');
      throw err;
    }
  }, [compressBase64Images]);

  return {
    compressImages,
    compressBase64Images,
    loading,
    error,
    statistics
  };
}

// Utility hook for automatic compression on file upload
export function useAutoImageCompression() {
  const { compressImages, loading, error } = useImageCompression();
  
  const handleFileUpload = useCallback(async (
    files: File[],
    onSuccess: (compressedImages: CompressedImageResult[]) => void,
    onError?: (error: string) => void
  ) => {
    try {
      const compressedImages = await compressImages(files);
      onSuccess(compressedImages);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Compression failed';
      onError?.(errorMessage);
    }
  }, [compressImages]);

  return {
    handleFileUpload,
    loading,
    error
  };
}

// Hook for compression with preview
export function useImageCompressionWithPreview() {
  const { compressImages, loading, error, statistics } = useImageCompression();
  const [previews, setPreviews] = useState<{ original: string; compressed: string }[]>([]);

  const compressWithPreview = useCallback(async (files: File[]) => {
    try {
      // Create original previews
      const originalPreviews = files.map(file => ImageHelpers.createPreviewUrl(file));
      
      // Compress images
      const compressedImages = await compressImages(files);
      
      // Create preview pairs
      const previewPairs = originalPreviews.map((original, index) => ({
        original,
        compressed: compressedImages[index].base64
      }));
      
      setPreviews(previewPairs);
      
      return compressedImages;
      
    } catch (err) {
      throw err;
    }
  }, [compressImages]);

  const clearPreviews = useCallback(() => {
    // Clean up object URLs
    previews.forEach(preview => {
      if (preview.original.startsWith('blob:')) {
        ImageHelpers.revokePreviewUrl(preview.original);
      }
    });
    setPreviews([]);
  }, [previews]);

  return {
    compressWithPreview,
    clearPreviews,
    previews,
    loading,
    error,
    statistics
  };
}
