import { createClient } from '@supabase/supabase-js';
import { env } from '@/lib/env';

// Supabase client configuration
// Development: Akan menggunakan PostgreSQL lokal via Drizzle ORM
// Production: Akan menggunakan Supabase Cloud
const supabaseUrl = env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// Hanya buat client jika URL dan key tersedia (production)
export const supabase = supabaseUrl && supabaseKey
  ? createClient(supabaseUrl, supabaseKey)
  : null;

// Helper function untuk cek apakah menggunakan Supabase
export const isUsingSupabase = () => Boolean(supabaseUrl && supabaseKey);
