import { useState, useEffect } from "react";
import { BioData } from "@/components/purchase/BioDataStep";
import { useReligionStore } from "@/store/useReligionStore";

interface UseBioDataFormProps {
  initialData?: BioData;
  religions: { id: string; slug: string; name: string }[];
}

export function useBioDataForm({ initialData, religions }: UseBioDataFormProps) {
  const { selectedReligion } = useReligionStore();

  const [bioData, setBioData] = useState<BioData>(
    initialData || {
      name: "",
      birthPlace: "",
      birthDate: "",
      deathDate: "",
      religionId: null,
      submittedBy: "",
      description: "",
      images: [],
      evidenceName: "",
      evidenceImage: null
    }
  );

  // Helper function to convert religion name to ID
  const getReligionIdByName = (religionName: string | null): string | null => {
    if (!religionName || religions.length === 0) return null;

    // Map religion names to match the database/service data
    const religionMap: { [key: string]: string } = {
      'Islam': 'Islam',
      '<PERSON>': '<PERSON>',
      '<PERSON><PERSON><PERSON>': '<PERSON><PERSON><PERSON>',
      'Hindu': 'Hindu',
      'Buddha': 'Buddha',
      '<PERSON><PERSON><PERSON>': '<PERSON>hu<PERSON>'
    };

    const mappedName = religionMap[religionName] || religionName;
    const religion = religions.find(r => r.name === mappedName);
    return religion ? religion.id : null;
  };

  // Reset bioData when initialData changes (e.g., when store is reset)
  useEffect(() => {
    const defaultData = {
      name: "",
      birthPlace: "",
      birthDate: "",
      deathDate: "",
      religionId: null,
      submittedBy: "",
      description: "",
      images: [],
      evidenceName: "",
      evidenceImage: null
    };

    if (initialData && Object.keys(initialData).length > 0) {
      // Check if initialData has actual content (not just empty strings)
      const hasContent = Object.values(initialData).some(value => {
        if (Array.isArray(value)) return value.length > 0;
        if (value === null || value === undefined) return false;
        if (typeof value === 'number') return value > 0;
        return String(value).trim() !== "";
      });

      if (hasContent) {
        // Get religionId from religion store if not in session data
        const religionIdFromStore = selectedReligion && religions.length > 0
          ? getReligionIdByName(selectedReligion)
          : null;

        setBioData({
          ...initialData,
          // Reset file arrays since they can't be persisted
          images: [],
          evidenceImage: null,
          // Prioritize session religionId, fallback to religion store, then null
          religionId: initialData.religionId || religionIdFromStore || null
        });
      } else {
        // For empty session, try to load from religion store
        const religionIdFromStore = selectedReligion && religions.length > 0
          ? getReligionIdByName(selectedReligion)
          : null;

        setBioData({
          ...defaultData,
          religionId: religionIdFromStore || null
        });
      }
    } else {
      setBioData(defaultData);
    }
  }, [initialData, JSON.stringify(initialData), selectedReligion, religions]);

  // Load religionId from religion store when religions data is available
  useEffect(() => {
    if (religions.length > 0 && selectedReligion && !bioData.religionId) {
      const religionId = getReligionIdByName(selectedReligion);
      if (religionId) {
        console.log(`🔄 Loading religionId from religion-storage: ${selectedReligion} → ${religionId}`);
        setBioData(prev => ({ ...prev, religionId }));
      }
    }
  }, [religions, selectedReligion, bioData.religionId]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setBioData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    if (name === 'religionId') {
      // Handle empty string as null, otherwise use the UUID string
      const uuidValue = value === '' ? null : value;
      setBioData(prev => ({ ...prev, [name]: uuidValue }));
    } else {
      setBioData(prev => ({ ...prev, [name]: value }));
    }
  };

  const updateBioData = (updates: Partial<BioData>) => {
    setBioData(prev => ({ ...prev, ...updates }));
  };

  return {
    bioData,
    setBioData,
    handleChange,
    handleSelectChange,
    updateBioData,
    getReligionIdByName
  };
}
