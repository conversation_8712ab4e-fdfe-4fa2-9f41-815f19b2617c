import { ApiError } from '@/utils/ApiError';
import { memorialIdSchema, updateMemorialSchema } from '@/validations/memorialParamSchema';

/**
 * Memvalidasi parameter ID memorial
 * @throws ApiError jika validasi gagal
 */
export function validateMemorialId(params: { id: string }) {
  const validationResult = memorialIdSchema.safeParse(params);
  
  if (!validationResult.success) {
    const errorMessages = validationResult.error.errors.map(err => 
      `${err.path.join('.')}: ${err.message}`
    ).join(', ');
    
    throw ApiError.badRequest(
      `Invalid ID format: ${errorMessages}`, 
      'INVALID_ID_FORMAT'
    );
  }
  
  return params.id;
}

/**
 * Memvalidasi request update memorial
 * @throws ApiError jika validasi gagal
 */
export function validateUpdateMemorialRequest(body: unknown) {
  const validationResult = updateMemorialSchema.safeParse(body);
  
  if (!validationResult.success) {
    const errorMessages = validationResult.error.errors.map(err => 
      `${err.path.join('.')}: ${err.message}`
    ).join(', ');
    
    throw ApiError.badRequest(
      `Validation error: ${errorMessages}`, 
      'VALIDATION_ERROR'
    );
  }
  
  return validationResult.data;
}